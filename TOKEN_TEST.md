# Token获取测试说明

## 当前配置

- **Secret ID (AK)**: `ak-BMSZyMnACKX6MPNne9zPfdFA`
- **Secret Key (SK)**: `sk-qrFpGRrDiAcu0YSUirhOVgPBT83qusb5`
- **User ID**: `76015687511834624`

## Token获取流程

### 1. 自动获取流程

应用启动时会使用官方文档指定的正确API端点获取token：

1. **官方API端点**: `https://api.pincaimao.com/agents/v1/auth/access-token`

### 2. 请求参数

```json
{
  "secretId": "ak-BMSZyMnACKX6MPNne9zPfdFA",
  "secretKey": "sk-qrFpGRrDiAcu0YSUirhOVgPBT83qusb5",
  "userId": "76015687511834624",
  "ak": "ak-BMSZyMnACKX6MPNne9zPfdFA",
  "sk": "sk-qrFpGRrDiAcu0YSUirhOVgPBT83qusb5",
  "user_id": "76015687511834624"
}
```

### 3. 期望响应格式

```json
{
  "code": 200,
  "data": {
    "token": "实际的访问token"
  }
}
```

或者简单格式：

```json
{
  "token": "实际的访问token"
}
```

### 4. 备用方案

如果所有API端点都失败，系统会：

1. **方案1**: 直接使用SK作为token
2. **方案2**: 生成JWT格式的备用token

## 测试步骤

1. 打开浏览器开发者工具
2. 访问 http://localhost:5173/
3. 查看控制台输出，观察token获取过程
4. 查看页面顶部的状态指示器
5. 尝试点击任意功能卡片测试SDK调用

## 状态指示器

- **⏳ 正在获取SDK授权...**: token获取中
- **✅ SDK授权成功，所有功能已就绪**: token获取成功
- **⚠️ 错误信息**: token获取失败，显示具体错误

## 调试信息

在浏览器控制台中可以看到：

- 尝试的API端点
- 请求和响应详情
- 最终使用的token（前20个字符）
- 错误信息（如果有）

## 注意事项

1. 确保网络连接正常
2. 检查CORS策略是否允许跨域请求
3. 验证提供的密钥是否有效
4. 如果API端点不正确，请联系聘才猫技术支持获取正确的端点

## 手动测试API

可以使用curl命令手动测试API：

```bash
curl -X POST https://api.pincaimao.com/agents/v1/auth/access-token \
  -H "Content-Type: application/json" \
  -d '{
    "secretId": "ak-BMSZyMnACKX6MPNne9zPfdFA",
    "secretKey": "sk-qrFpGRrDiAcu0YSUirhOVgPBT83qusb5",
    "userId": "76015687511834624"
  }'
```

---

如果需要更新token获取逻辑，请修改 `src/App.vue` 中的 `getSDKToken` 函数。
