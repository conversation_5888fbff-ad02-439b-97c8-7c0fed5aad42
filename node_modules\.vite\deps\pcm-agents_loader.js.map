{"version": 3, "sources": ["../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/parseSampleRate.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/instrument/handlers.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/instrument/globalError.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/instrument/globalUnhandledRejection.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/envelope.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/envelope.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/api.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/integration.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/eventUtils.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/transactionEvent.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/clientreport.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/client.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/logs/envelope.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/logs/exports.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/sdk.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/promisebuffer.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/ratelimit.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/transports/base.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/ipAddress.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/sdkMetadata.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/breadcrumbs.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/integrations/functiontostring.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/integrations/eventFilters.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/aggregate-errors.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/instrument/console.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/severity.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/integrations/dedupe.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/url.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/breadcrumb-log-level.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/supports.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/instrument/fetch.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/env.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/helpers.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/eventbuilder.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/client.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry-internal+browser-utils@9.22.0/node_modules/@sentry-internal/browser-utils/build/esm/debug-build.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry-internal+browser-utils@9.22.0/node_modules/@sentry-internal/browser-utils/build/esm/types.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry-internal+browser-utils@9.22.0/node_modules/@sentry-internal/browser-utils/build/esm/instrument/dom.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry-internal+browser-utils@9.22.0/node_modules/@sentry-internal/browser-utils/build/esm/instrument/history.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry-internal+browser-utils@9.22.0/node_modules/@sentry-internal/browser-utils/build/esm/instrument/xhr.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/transports/fetch.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/stack-parsers.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/debug-build.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/integrations/breadcrumbs.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/integrations/browserapierrors.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/integrations/browsersession.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/integrations/globalhandlers.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/integrations/httpcontext.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/integrations/linkederrors.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+browser@9.22.0/node_modules/@sentry/browser/build/npm/esm/sdk.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/src/utils/init.ts", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/@stencil/core/internal/app-globals", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/@lazy-external-entrypoint?app-data=conditional", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/loader/index.js"], "sourcesContent": ["/**\n * Parse a sample rate from a given value.\n * This will either return a boolean or number sample rate, if the sample rate is valid (between 0 and 1).\n * If a string is passed, we try to convert it to a number.\n *\n * Any invalid sample rate will return `undefined`.\n */\nfunction parseSampleRate(sampleRate) {\n  if (typeof sampleRate === 'boolean') {\n    return Number(sampleRate);\n  }\n\n  const rate = typeof sampleRate === 'string' ? parseFloat(sampleRate) : sampleRate;\n  if (typeof rate !== 'number' || isNaN(rate) || rate < 0 || rate > 1) {\n    return undefined;\n  }\n\n  return rate;\n}\n\nexport { parseSampleRate };\n//# sourceMappingURL=parseSampleRate.js.map\n", "import { DEBUG_BUILD } from '../../debug-build.js';\nimport { logger } from '../logger.js';\nimport { getFunctionName } from '../stacktrace.js';\n\n// We keep the handlers globally\nconst handlers = {};\nconst instrumented = {};\n\n/** Add a handler function. */\nfunction addHandler(type, handler) {\n  handlers[type] = handlers[type] || [];\n  (handlers[type] ).push(handler);\n}\n\n/**\n * Reset all instrumentation handlers.\n * This can be used by tests to ensure we have a clean slate of instrumentation handlers.\n */\nfunction resetInstrumentationHandlers() {\n  Object.keys(handlers).forEach(key => {\n    handlers[key ] = undefined;\n  });\n}\n\n/** Maybe run an instrumentation function, unless it was already called. */\nfunction maybeInstrument(type, instrumentFn) {\n  if (!instrumented[type]) {\n    instrumented[type] = true;\n    try {\n      instrumentFn();\n    } catch (e) {\n      DEBUG_BUILD && logger.error(`Error while instrumenting ${type}`, e);\n    }\n  }\n}\n\n/** Trigger handlers for a given instrumentation type. */\nfunction triggerHandlers(type, data) {\n  const typeHandlers = type && handlers[type];\n  if (!typeHandlers) {\n    return;\n  }\n\n  for (const handler of typeHandlers) {\n    try {\n      handler(data);\n    } catch (e) {\n      DEBUG_BUILD &&\n        logger.error(\n          `Error while triggering instrumentation handler.\\nType: ${type}\\nName: ${getFunctionName(handler)}\\nError:`,\n          e,\n        );\n    }\n  }\n}\n\nexport { addHandler, maybeInstrument, resetInstrumentationHandlers, triggerHandlers };\n//# sourceMappingURL=handlers.js.map\n", "import { GLOBAL_OBJ } from '../worldwide.js';\nimport { addHandler, maybeInstrument, triggerHandlers } from './handlers.js';\n\nlet _oldOnErrorHandler = null;\n\n/**\n * Add an instrumentation handler for when an error is captured by the global error handler.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nfunction addGlobalErrorInstrumentationHandler(handler) {\n  const type = 'error';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentError);\n}\n\nfunction instrumentError() {\n  _oldOnErrorHandler = GLOBAL_OBJ.onerror;\n\n  // Note: The reason we are doing window.onerror instead of window.addEventListener('error')\n  // is that we are using this handler in the Loader Script, to handle buffered errors consistently\n  GLOBAL_OBJ.onerror = function (\n    msg,\n    url,\n    line,\n    column,\n    error,\n  ) {\n    const handlerData = {\n      column,\n      error,\n      line,\n      msg,\n      url,\n    };\n    triggerHandlers('error', handlerData);\n\n    if (_oldOnErrorHandler) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnErrorHandler.apply(this, arguments);\n    }\n\n    return false;\n  };\n\n  GLOBAL_OBJ.onerror.__SENTRY_INSTRUMENTED__ = true;\n}\n\nexport { addGlobalErrorInstrumentationHandler };\n//# sourceMappingURL=globalError.js.map\n", "import { GLOBAL_OBJ } from '../worldwide.js';\nimport { addHandler, maybeInstrument, triggerHandlers } from './handlers.js';\n\nlet _oldOnUnhandledRejectionHandler = null;\n\n/**\n * Add an instrumentation handler for when an unhandled promise rejection is captured.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nfunction addGlobalUnhandledRejectionInstrumentationHandler(\n  handler,\n) {\n  const type = 'unhandledrejection';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentUnhandledRejection);\n}\n\nfunction instrumentUnhandledRejection() {\n  _oldOnUnhandledRejectionHandler = GLOBAL_OBJ.onunhandledrejection;\n\n  // Note: The reason we are doing window.onunhandledrejection instead of window.addEventListener('unhandledrejection')\n  // is that we are using this handler in the Loader Script, to handle buffered rejections consistently\n  GLOBAL_OBJ.onunhandledrejection = function (e) {\n    const handlerData = e;\n    triggerHandlers('unhandledrejection', handlerData);\n\n    if (_oldOnUnhandledRejectionHandler) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnUnhandledRejectionHandler.apply(this, arguments);\n    }\n\n    return true;\n  };\n\n  GLOBAL_OBJ.onunhandledrejection.__SENTRY_INSTRUMENTED__ = true;\n}\n\nexport { addGlobalUnhandledRejectionInstrumentationHandler };\n//# sourceMappingURL=globalUnhandledRejection.js.map\n", "import { getSentryCarrier } from '../carrier.js';\nimport { dsnToString } from './dsn.js';\nimport { normalize } from './normalize.js';\nimport { GLOBAL_OBJ } from './worldwide.js';\n\n/**\n * Creates an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nfunction createEnvelope(headers, items = []) {\n  return [headers, items] ;\n}\n\n/**\n * Add an item to an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nfunction addItemToEnvelope(envelope, newItem) {\n  const [headers, items] = envelope;\n  return [headers, [...items, newItem]] ;\n}\n\n/**\n * Convenience function to loop through the items and item types of an envelope.\n * (This function was mostly created because working with envelope types is painful at the moment)\n *\n * If the callback returns true, the rest of the items will be skipped.\n */\nfunction forEachEnvelopeItem(\n  envelope,\n  callback,\n) {\n  const envelopeItems = envelope[1];\n\n  for (const envelopeItem of envelopeItems) {\n    const envelopeItemType = envelopeItem[0].type;\n    const result = callback(envelopeItem, envelopeItemType);\n\n    if (result) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Returns true if the envelope contains any of the given envelope item types\n */\nfunction envelopeContainsItemType(envelope, types) {\n  return forEachEnvelopeItem(envelope, (_, type) => types.includes(type));\n}\n\n/**\n * Encode a string to UTF8 array.\n */\nfunction encodeUTF8(input) {\n  const carrier = getSentryCarrier(GLOBAL_OBJ);\n  return carrier.encodePolyfill ? carrier.encodePolyfill(input) : new TextEncoder().encode(input);\n}\n\n/**\n * Decode a UTF8 array to string.\n */\nfunction decodeUTF8(input) {\n  const carrier = getSentryCarrier(GLOBAL_OBJ);\n  return carrier.decodePolyfill ? carrier.decodePolyfill(input) : new TextDecoder().decode(input);\n}\n\n/**\n * Serializes an envelope.\n */\nfunction serializeEnvelope(envelope) {\n  const [envHeaders, items] = envelope;\n  // Initially we construct our envelope as a string and only convert to binary chunks if we encounter binary data\n  let parts = JSON.stringify(envHeaders);\n\n  function append(next) {\n    if (typeof parts === 'string') {\n      parts = typeof next === 'string' ? parts + next : [encodeUTF8(parts), next];\n    } else {\n      parts.push(typeof next === 'string' ? encodeUTF8(next) : next);\n    }\n  }\n\n  for (const item of items) {\n    const [itemHeaders, payload] = item;\n\n    append(`\\n${JSON.stringify(itemHeaders)}\\n`);\n\n    if (typeof payload === 'string' || payload instanceof Uint8Array) {\n      append(payload);\n    } else {\n      let stringifiedPayload;\n      try {\n        stringifiedPayload = JSON.stringify(payload);\n      } catch (e) {\n        // In case, despite all our efforts to keep `payload` circular-dependency-free, `JSON.stringify()` still\n        // fails, we try again after normalizing it again with infinite normalization depth. This of course has a\n        // performance impact but in this case a performance hit is better than throwing.\n        stringifiedPayload = JSON.stringify(normalize(payload));\n      }\n      append(stringifiedPayload);\n    }\n  }\n\n  return typeof parts === 'string' ? parts : concatBuffers(parts);\n}\n\nfunction concatBuffers(buffers) {\n  const totalLength = buffers.reduce((acc, buf) => acc + buf.length, 0);\n\n  const merged = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const buffer of buffers) {\n    merged.set(buffer, offset);\n    offset += buffer.length;\n  }\n\n  return merged;\n}\n\n/**\n * Parses an envelope\n */\nfunction parseEnvelope(env) {\n  let buffer = typeof env === 'string' ? encodeUTF8(env) : env;\n\n  function readBinary(length) {\n    const bin = buffer.subarray(0, length);\n    // Replace the buffer with the remaining data excluding trailing newline\n    buffer = buffer.subarray(length + 1);\n    return bin;\n  }\n\n  function readJson() {\n    let i = buffer.indexOf(0xa);\n    // If we couldn't find a newline, we must have found the end of the buffer\n    if (i < 0) {\n      i = buffer.length;\n    }\n\n    return JSON.parse(decodeUTF8(readBinary(i))) ;\n  }\n\n  const envelopeHeader = readJson();\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const items = [];\n\n  while (buffer.length) {\n    const itemHeader = readJson();\n    const binaryLength = typeof itemHeader.length === 'number' ? itemHeader.length : undefined;\n\n    items.push([itemHeader, binaryLength ? readBinary(binaryLength) : readJson()]);\n  }\n\n  return [envelopeHeader, items];\n}\n\n/**\n * Creates envelope item for a single span\n */\nfunction createSpanEnvelopeItem(spanJson) {\n  const spanHeaders = {\n    type: 'span',\n  };\n\n  return [spanHeaders, spanJson];\n}\n\n/**\n * Creates attachment envelope items\n */\nfunction createAttachmentEnvelopeItem(attachment) {\n  const buffer = typeof attachment.data === 'string' ? encodeUTF8(attachment.data) : attachment.data;\n\n  return [\n    {\n      type: 'attachment',\n      length: buffer.length,\n      filename: attachment.filename,\n      content_type: attachment.contentType,\n      attachment_type: attachment.attachmentType,\n    },\n    buffer,\n  ];\n}\n\nconst ITEM_TYPE_TO_DATA_CATEGORY_MAP = {\n  session: 'session',\n  sessions: 'session',\n  attachment: 'attachment',\n  transaction: 'transaction',\n  event: 'error',\n  client_report: 'internal',\n  user_report: 'default',\n  profile: 'profile',\n  profile_chunk: 'profile',\n  replay_event: 'replay',\n  replay_recording: 'replay',\n  check_in: 'monitor',\n  feedback: 'feedback',\n  span: 'span',\n  raw_security: 'security',\n  log: 'log_item',\n};\n\n/**\n * Maps the type of an envelope item to a data category.\n */\nfunction envelopeItemTypeToDataCategory(type) {\n  return ITEM_TYPE_TO_DATA_CATEGORY_MAP[type];\n}\n\n/** Extracts the minimal SDK info from the metadata or an events */\nfunction getSdkMetadataForEnvelopeHeader(metadataOrEvent) {\n  if (!metadataOrEvent?.sdk) {\n    return;\n  }\n  const { name, version } = metadataOrEvent.sdk;\n  return { name, version };\n}\n\n/**\n * Creates event envelope headers, based on event, sdk info and tunnel\n * Note: This function was extracted from the core package to make it available in Replay\n */\nfunction createEventEnvelopeHeaders(\n  event,\n  sdkInfo,\n  tunnel,\n  dsn,\n) {\n  const dynamicSamplingContext = event.sdkProcessingMetadata?.dynamicSamplingContext;\n  return {\n    event_id: event.event_id ,\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n    ...(dynamicSamplingContext && {\n      trace: dynamicSamplingContext,\n    }),\n  };\n}\n\nexport { addItemToEnvelope, createAttachmentEnvelopeItem, createEnvelope, createEventEnvelopeHeaders, createSpanEnvelopeItem, envelopeContainsItemType, envelopeItemTypeToDataCategory, forEachEnvelopeItem, getSdkMetadataForEnvelopeHeader, parseEnvelope, serializeEnvelope };\n//# sourceMappingURL=envelope.js.map\n", "import { getDynamicSamplingContextFromSpan } from './tracing/dynamicSamplingContext.js';\nimport { spanToJSON, showSpanDropWarning } from './utils/spanUtils.js';\nimport { dsnToString } from './utils-hoist/dsn.js';\nimport { getSdkMetadataForEnvelopeHeader, createEventEnvelopeHeaders, createEnvelope, createSpanEnvelopeItem } from './utils-hoist/envelope.js';\nimport './debug-build.js';\nimport './utils-hoist/logger.js';\n\n/**\n * Apply SdkInfo (name, version, packages, integrations) to the corresponding event key.\n * Merge with existing data if any.\n **/\nfunction enhanceEventWithSdkInfo(event, sdkInfo) {\n  if (!sdkInfo) {\n    return event;\n  }\n  event.sdk = event.sdk || {};\n  event.sdk.name = event.sdk.name || sdkInfo.name;\n  event.sdk.version = event.sdk.version || sdkInfo.version;\n  event.sdk.integrations = [...(event.sdk.integrations || []), ...(sdkInfo.integrations || [])];\n  event.sdk.packages = [...(event.sdk.packages || []), ...(sdkInfo.packages || [])];\n  return event;\n}\n\n/** Creates an envelope from a Session */\nfunction createSessionEnvelope(\n  session,\n  dsn,\n  metadata,\n  tunnel,\n) {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n  const envelopeHeaders = {\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n  };\n\n  const envelopeItem =\n    'aggregates' in session ? [{ type: 'sessions' }, session] : [{ type: 'session' }, session.toJSON()];\n\n  return createEnvelope(envelopeHeaders, [envelopeItem]);\n}\n\n/**\n * Create an Envelope from an event.\n */\nfunction createEventEnvelope(\n  event,\n  dsn,\n  metadata,\n  tunnel,\n) {\n  const sdkInfo = getSdkMetadataForEnvelopeHeader(metadata);\n\n  /*\n    Note: Due to TS, event.type may be `replay_event`, theoretically.\n    In practice, we never call `createEventEnvelope` with `replay_event` type,\n    and we'd have to adjust a looot of types to make this work properly.\n    We want to avoid casting this around, as that could lead to bugs (e.g. when we add another type)\n    So the safe choice is to really guard against the replay_event type here.\n  */\n  const eventType = event.type && event.type !== 'replay_event' ? event.type : 'event';\n\n  enhanceEventWithSdkInfo(event, metadata?.sdk);\n\n  const envelopeHeaders = createEventEnvelopeHeaders(event, sdkInfo, tunnel, dsn);\n\n  // Prevent this data (which, if it exists, was used in earlier steps in the processing pipeline) from being sent to\n  // sentry. (Note: Our use of this property comes and goes with whatever we might be debugging, whatever hacks we may\n  // have temporarily added, etc. Even if we don't happen to be using it at some point in the future, let's not get rid\n  // of this `delete`, lest we miss putting it back in the next time the property is in use.)\n  delete event.sdkProcessingMetadata;\n\n  const eventItem = [{ type: eventType }, event];\n  return createEnvelope(envelopeHeaders, [eventItem]);\n}\n\n/**\n * Create envelope from Span item.\n *\n * Takes an optional client and runs spans through `beforeSendSpan` if available.\n */\nfunction createSpanEnvelope(spans, client) {\n  function dscHasRequiredProps(dsc) {\n    return !!dsc.trace_id && !!dsc.public_key;\n  }\n\n  // For the moment we'll obtain the DSC from the first span in the array\n  // This might need to be changed if we permit sending multiple spans from\n  // different segments in one envelope\n  const dsc = getDynamicSamplingContextFromSpan(spans[0]);\n\n  const dsn = client?.getDsn();\n  const tunnel = client?.getOptions().tunnel;\n\n  const headers = {\n    sent_at: new Date().toISOString(),\n    ...(dscHasRequiredProps(dsc) && { trace: dsc }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n  };\n\n  const beforeSendSpan = client?.getOptions().beforeSendSpan;\n  const convertToSpanJSON = beforeSendSpan\n    ? (span) => {\n        const spanJson = spanToJSON(span);\n        const processedSpan = beforeSendSpan(spanJson);\n\n        if (!processedSpan) {\n          showSpanDropWarning();\n          return spanJson;\n        }\n\n        return processedSpan;\n      }\n    : spanToJSON;\n\n  const items = [];\n  for (const span of spans) {\n    const spanJson = convertToSpanJSON(span);\n    if (spanJson) {\n      items.push(createSpanEnvelopeItem(spanJson));\n    }\n  }\n\n  return createEnvelope(headers, items);\n}\n\nexport { createEventEnvelope, createSessionEnvelope, createSpanEnvelope };\n//# sourceMappingURL=envelope.js.map\n", "import { makeDsn, dsnToString } from './utils-hoist/dsn.js';\n\nconst SENTRY_API_VERSION = '7';\n\n/** Returns the prefix to construct Sentry ingestion API endpoints. */\nfunction getBaseApiEndpoint(dsn) {\n  const protocol = dsn.protocol ? `${dsn.protocol}:` : '';\n  const port = dsn.port ? `:${dsn.port}` : '';\n  return `${protocol}//${dsn.host}${port}${dsn.path ? `/${dsn.path}` : ''}/api/`;\n}\n\n/** Returns the ingest API endpoint for target. */\nfunction _getIngestEndpoint(dsn) {\n  return `${getBaseApiEndpoint(dsn)}${dsn.projectId}/envelope/`;\n}\n\n/** Returns a URL-encoded string with auth config suitable for a query string. */\nfunction _encodedAuth(dsn, sdkInfo) {\n  const params = {\n    sentry_version: SENTRY_API_VERSION,\n  };\n\n  if (dsn.publicKey) {\n    // We send only the minimum set of required information. See\n    // https://github.com/getsentry/sentry-javascript/issues/2572.\n    params.sentry_key = dsn.publicKey;\n  }\n\n  if (sdkInfo) {\n    params.sentry_client = `${sdkInfo.name}/${sdkInfo.version}`;\n  }\n\n  return new URLSearchParams(params).toString();\n}\n\n/**\n * Returns the envelope endpoint URL with auth in the query string.\n *\n * Sending auth as part of the query string and not as custom HTTP headers avoids CORS preflight requests.\n */\nfunction getEnvelopeEndpointWithUrlEncodedAuth(dsn, tunnel, sdkInfo) {\n  return tunnel ? tunnel : `${_getIngestEndpoint(dsn)}?${_encodedAuth(dsn, sdkInfo)}`;\n}\n\n/** Returns the url to the report dialog endpoint. */\nfunction getReportDialogEndpoint(dsnLike, dialogOptions) {\n  const dsn = makeDsn(dsnLike);\n  if (!dsn) {\n    return '';\n  }\n\n  const endpoint = `${getBaseApiEndpoint(dsn)}embed/error-page/`;\n\n  let encodedOptions = `dsn=${dsnToString(dsn)}`;\n  for (const key in dialogOptions) {\n    if (key === 'dsn') {\n      continue;\n    }\n\n    if (key === 'onClose') {\n      continue;\n    }\n\n    if (key === 'user') {\n      const user = dialogOptions.user;\n      if (!user) {\n        continue;\n      }\n      if (user.name) {\n        encodedOptions += `&name=${encodeURIComponent(user.name)}`;\n      }\n      if (user.email) {\n        encodedOptions += `&email=${encodeURIComponent(user.email)}`;\n      }\n    } else {\n      encodedOptions += `&${encodeURIComponent(key)}=${encodeURIComponent(dialogOptions[key] )}`;\n    }\n  }\n\n  return `${endpoint}?${encodedOptions}`;\n}\n\nexport { getEnvelopeEndpointWithUrlEncodedAuth, getReportDialogEndpoint };\n//# sourceMappingURL=api.js.map\n", "import { getClient } from './currentScopes.js';\nimport { DEBUG_BUILD } from './debug-build.js';\nimport { logger } from './utils-hoist/logger.js';\n\nconst installedIntegrations = [];\n\n/** Map of integrations assigned to a client */\n\n/**\n * Remove duplicates from the given array, preferring the last instance of any duplicate. Not guaranteed to\n * preserve the order of integrations in the array.\n *\n * @private\n */\nfunction filterDuplicates(integrations) {\n  const integrationsByName = {};\n\n  integrations.forEach((currentInstance) => {\n    const { name } = currentInstance;\n\n    const existingInstance = integrationsByName[name];\n\n    // We want integrations later in the array to overwrite earlier ones of the same type, except that we never want a\n    // default instance to overwrite an existing user instance\n    if (existingInstance && !existingInstance.isDefaultInstance && currentInstance.isDefaultInstance) {\n      return;\n    }\n\n    integrationsByName[name] = currentInstance;\n  });\n\n  return Object.values(integrationsByName);\n}\n\n/** Gets integrations to install */\nfunction getIntegrationsToSetup(options) {\n  const defaultIntegrations = options.defaultIntegrations || [];\n  const userIntegrations = options.integrations;\n\n  // We flag default instances, so that later we can tell them apart from any user-created instances of the same class\n  defaultIntegrations.forEach((integration) => {\n    integration.isDefaultInstance = true;\n  });\n\n  let integrations;\n\n  if (Array.isArray(userIntegrations)) {\n    integrations = [...defaultIntegrations, ...userIntegrations];\n  } else if (typeof userIntegrations === 'function') {\n    const resolvedUserIntegrations = userIntegrations(defaultIntegrations);\n    integrations = Array.isArray(resolvedUserIntegrations) ? resolvedUserIntegrations : [resolvedUserIntegrations];\n  } else {\n    integrations = defaultIntegrations;\n  }\n\n  return filterDuplicates(integrations);\n}\n\n/**\n * Given a list of integration instances this installs them all. When `withDefaults` is set to `true` then all default\n * integrations are added unless they were already provided before.\n * @param integrations array of integration instances\n * @param withDefault should enable default integrations\n */\nfunction setupIntegrations(client, integrations) {\n  const integrationIndex = {};\n\n  integrations.forEach((integration) => {\n    // guard against empty provided integrations\n    if (integration) {\n      setupIntegration(client, integration, integrationIndex);\n    }\n  });\n\n  return integrationIndex;\n}\n\n/**\n * Execute the `afterAllSetup` hooks of the given integrations.\n */\nfunction afterSetupIntegrations(client, integrations) {\n  for (const integration of integrations) {\n    // guard against empty provided integrations\n    if (integration?.afterAllSetup) {\n      integration.afterAllSetup(client);\n    }\n  }\n}\n\n/** Setup a single integration.  */\nfunction setupIntegration(client, integration, integrationIndex) {\n  if (integrationIndex[integration.name]) {\n    DEBUG_BUILD && logger.log(`Integration skipped because it was already installed: ${integration.name}`);\n    return;\n  }\n  integrationIndex[integration.name] = integration;\n\n  // `setupOnce` is only called the first time\n  if (installedIntegrations.indexOf(integration.name) === -1 && typeof integration.setupOnce === 'function') {\n    integration.setupOnce();\n    installedIntegrations.push(integration.name);\n  }\n\n  // `setup` is run for each client\n  if (integration.setup && typeof integration.setup === 'function') {\n    integration.setup(client);\n  }\n\n  if (typeof integration.preprocessEvent === 'function') {\n    const callback = integration.preprocessEvent.bind(integration) ;\n    client.on('preprocessEvent', (event, hint) => callback(event, hint, client));\n  }\n\n  if (typeof integration.processEvent === 'function') {\n    const callback = integration.processEvent.bind(integration) ;\n\n    const processor = Object.assign((event, hint) => callback(event, hint, client), {\n      id: integration.name,\n    });\n\n    client.addEventProcessor(processor);\n  }\n\n  DEBUG_BUILD && logger.log(`Integration installed: ${integration.name}`);\n}\n\n/** Add an integration to the current scope's client. */\nfunction addIntegration(integration) {\n  const client = getClient();\n\n  if (!client) {\n    DEBUG_BUILD && logger.warn(`Cannot add integration \"${integration.name}\" because no SDK Client is available.`);\n    return;\n  }\n\n  client.addIntegration(integration);\n}\n\n/**\n * Define an integration function that can be used to create an integration instance.\n * Note that this by design hides the implementation details of the integration, as they are considered internal.\n */\nfunction defineIntegration(fn) {\n  return fn;\n}\n\nexport { addIntegration, afterSetupIntegrations, defineIntegration, getIntegrationsToSetup, installedIntegrations, setupIntegration, setupIntegrations };\n//# sourceMappingURL=integration.js.map\n", "/**\n * Get a list of possible event messages from a Sentry event.\n */\nfunction getPossibleEventMessages(event) {\n  const possibleMessages = [];\n\n  if (event.message) {\n    possibleMessages.push(event.message);\n  }\n\n  try {\n    // @ts-expect-error Try catching to save bundle size\n    const lastException = event.exception.values[event.exception.values.length - 1];\n    if (lastException?.value) {\n      possibleMessages.push(lastException.value);\n      if (lastException.type) {\n        possibleMessages.push(`${lastException.type}: ${lastException.value}`);\n      }\n    }\n  } catch (e) {\n    // ignore errors here\n  }\n\n  return possibleMessages;\n}\n\nexport { getPossibleEventMessages };\n//# sourceMappingURL=eventUtils.js.map\n", "import { SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME, SEMANTIC_ATTRIBUTE_PROFILE_ID } from '../semanticAttributes.js';\n\n/**\n * Converts a transaction event to a span JSON object.\n */\nfunction convertTransactionEventToSpanJson(event) {\n  const { trace_id, parent_span_id, span_id, status, origin, data, op } = event.contexts?.trace ?? {};\n\n  return {\n    data: data ?? {},\n    description: event.transaction,\n    op,\n    parent_span_id,\n    span_id: span_id ?? '',\n    start_timestamp: event.start_timestamp ?? 0,\n    status,\n    timestamp: event.timestamp,\n    trace_id: trace_id ?? '',\n    origin,\n    profile_id: data?.[SEMANTIC_ATTRIBUTE_PROFILE_ID] ,\n    exclusive_time: data?.[SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME] ,\n    measurements: event.measurements,\n    is_segment: true,\n  };\n}\n\n/**\n * Converts a span JSON object to a transaction event.\n */\nfunction convertSpanJsonToTransactionEvent(span) {\n  return {\n    type: 'transaction',\n    timestamp: span.timestamp,\n    start_timestamp: span.start_timestamp,\n    transaction: span.description,\n    contexts: {\n      trace: {\n        trace_id: span.trace_id,\n        span_id: span.span_id,\n        parent_span_id: span.parent_span_id,\n        op: span.op,\n        status: span.status,\n        origin: span.origin,\n        data: {\n          ...span.data,\n          ...(span.profile_id && { [SEMANTIC_ATTRIBUTE_PROFILE_ID]: span.profile_id }),\n          ...(span.exclusive_time && { [SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME]: span.exclusive_time }),\n        },\n      },\n    },\n    measurements: span.measurements,\n  };\n}\n\nexport { convertSpanJsonToTransactionEvent, convertTransactionEventToSpanJson };\n//# sourceMappingURL=transactionEvent.js.map\n", "import { createEnvelope } from './envelope.js';\nimport { dateTimestampInSeconds } from './time.js';\n\n/**\n * Creates client report envelope\n * @param discarded_events An array of discard events\n * @param dsn A DSN that can be set on the header. Optional.\n */\nfunction createClientReportEnvelope(\n  discarded_events,\n  dsn,\n  timestamp,\n) {\n  const clientReportItem = [\n    { type: 'client_report' },\n    {\n      timestamp: timestamp || dateTimestampInSeconds(),\n      discarded_events,\n    },\n  ];\n  return createEnvelope(dsn ? { dsn } : {}, [clientReportItem]);\n}\n\nexport { createClientReportEnvelope };\n//# sourceMappingURL=clientreport.js.map\n", "import { getEnvelopeEndpointWithUrlEncodedAuth } from './api.js';\nimport { DEFAULT_ENVIRONMENT } from './constants.js';\nimport { getTraceContextFromScope, getCurrentScope, getIsolationScope } from './currentScopes.js';\nimport { DEBUG_BUILD } from './debug-build.js';\nimport { createEventEnvelope, createSessionEnvelope } from './envelope.js';\nimport { setupIntegration, afterSetupIntegrations, setupIntegrations } from './integration.js';\nimport { updateSession } from './session.js';\nimport { getDynamicSamplingContextFromScope, getDynamicSamplingContextFromSpan } from './tracing/dynamicSamplingContext.js';\nimport { getPossibleEventMessages } from './utils/eventUtils.js';\nimport { merge } from './utils/merge.js';\nimport { parseSampleRate } from './utils/parseSampleRate.js';\nimport { prepareEvent } from './utils/prepareEvent.js';\nimport { _getSpanForScope } from './utils/spanOnScope.js';\nimport { showSpanDropWarning, spanToTraceContext } from './utils/spanUtils.js';\nimport { convertTransactionEventToSpanJson, convertSpanJsonToTransactionEvent } from './utils/transactionEvent.js';\nimport { createClientReportEnvelope } from './utils-hoist/clientreport.js';\nimport { makeDsn, dsnToString } from './utils-hoist/dsn.js';\nimport { addItemToEnvelope, createAttachmentEnvelopeItem } from './utils-hoist/envelope.js';\nimport { isPrimitive, isParameterizedString, isThenable, isPlainObject } from './utils-hoist/is.js';\nimport { logger } from './utils-hoist/logger.js';\nimport { uuid4, checkOrSetAlreadyCaught } from './utils-hoist/misc.js';\nimport { resolvedSyncPromise, SyncPromise, rejectedSyncPromise } from './utils-hoist/syncpromise.js';\n\n/* eslint-disable max-lines */\n\nconst ALREADY_SEEN_ERROR = \"Not capturing exception because it's already been captured.\";\nconst MISSING_RELEASE_FOR_SESSION_ERROR = 'Discarded session because of missing or non-string release';\n\nconst INTERNAL_ERROR_SYMBOL = Symbol.for('SentryInternalError');\nconst DO_NOT_SEND_EVENT_SYMBOL = Symbol.for('SentryDoNotSendEventError');\n\nfunction _makeInternalError(message) {\n  return {\n    message,\n    [INTERNAL_ERROR_SYMBOL]: true,\n  };\n}\n\nfunction _makeDoNotSendEventError(message) {\n  return {\n    message,\n    [DO_NOT_SEND_EVENT_SYMBOL]: true,\n  };\n}\n\nfunction _isInternalError(error) {\n  return !!error && typeof error === 'object' && INTERNAL_ERROR_SYMBOL in error;\n}\n\nfunction _isDoNotSendEventError(error) {\n  return !!error && typeof error === 'object' && DO_NOT_SEND_EVENT_SYMBOL in error;\n}\n\n/**\n * Base implementation for all JavaScript SDK clients.\n *\n * Call the constructor with the corresponding options\n * specific to the client subclass. To access these options later, use\n * {@link Client.getOptions}.\n *\n * If a Dsn is specified in the options, it will be parsed and stored. Use\n * {@link Client.getDsn} to retrieve the Dsn at any moment. In case the Dsn is\n * invalid, the constructor will throw a {@link SentryException}. Note that\n * without a valid Dsn, the SDK will not send any events to Sentry.\n *\n * Before sending an event, it is passed through\n * {@link Client._prepareEvent} to add SDK information and scope data\n * (breadcrumbs and context). To add more custom information, override this\n * method and extend the resulting prepared event.\n *\n * To issue automatically created events (e.g. via instrumentation), use\n * {@link Client.captureEvent}. It will prepare the event and pass it through\n * the callback lifecycle. To issue auto-breadcrumbs, use\n * {@link Client.addBreadcrumb}.\n *\n * @example\n * class NodeClient extends Client<NodeOptions> {\n *   public constructor(options: NodeOptions) {\n *     super(options);\n *   }\n *\n *   // ...\n * }\n */\nclass Client {\n  /** Options passed to the SDK. */\n\n  /** The client Dsn, if specified in options. Without this Dsn, the SDK will be disabled. */\n\n  /** Array of set up integrations. */\n\n  /** Number of calls being processed */\n\n  /** Holds flushable  */\n\n  // eslint-disable-next-line @typescript-eslint/ban-types\n\n  /**\n   * Initializes this client instance.\n   *\n   * @param options Options for the client.\n   */\n   constructor(options) {\n    this._options = options;\n    this._integrations = {};\n    this._numProcessing = 0;\n    this._outcomes = {};\n    this._hooks = {};\n    this._eventProcessors = [];\n\n    if (options.dsn) {\n      this._dsn = makeDsn(options.dsn);\n    } else {\n      DEBUG_BUILD && logger.warn('No DSN provided, client will not send events.');\n    }\n\n    if (this._dsn) {\n      const url = getEnvelopeEndpointWithUrlEncodedAuth(\n        this._dsn,\n        options.tunnel,\n        options._metadata ? options._metadata.sdk : undefined,\n      );\n      this._transport = options.transport({\n        tunnel: this._options.tunnel,\n        recordDroppedEvent: this.recordDroppedEvent.bind(this),\n        ...options.transportOptions,\n        url,\n      });\n    }\n  }\n\n  /**\n   * Captures an exception event and sends it to Sentry.\n   *\n   * Unlike `captureException` exported from every SDK, this method requires that you pass it the current scope.\n   */\n   captureException(exception, hint, scope) {\n    const eventId = uuid4();\n\n    // ensure we haven't captured this very object before\n    if (checkOrSetAlreadyCaught(exception)) {\n      DEBUG_BUILD && logger.log(ALREADY_SEEN_ERROR);\n      return eventId;\n    }\n\n    const hintWithEventId = {\n      event_id: eventId,\n      ...hint,\n    };\n\n    this._process(\n      this.eventFromException(exception, hintWithEventId).then(event =>\n        this._captureEvent(event, hintWithEventId, scope),\n      ),\n    );\n\n    return hintWithEventId.event_id;\n  }\n\n  /**\n   * Captures a message event and sends it to Sentry.\n   *\n   * Unlike `captureMessage` exported from every SDK, this method requires that you pass it the current scope.\n   */\n   captureMessage(\n    message,\n    level,\n    hint,\n    currentScope,\n  ) {\n    const hintWithEventId = {\n      event_id: uuid4(),\n      ...hint,\n    };\n\n    const eventMessage = isParameterizedString(message) ? message : String(message);\n\n    const promisedEvent = isPrimitive(message)\n      ? this.eventFromMessage(eventMessage, level, hintWithEventId)\n      : this.eventFromException(message, hintWithEventId);\n\n    this._process(promisedEvent.then(event => this._captureEvent(event, hintWithEventId, currentScope)));\n\n    return hintWithEventId.event_id;\n  }\n\n  /**\n   * Captures a manually created event and sends it to Sentry.\n   *\n   * Unlike `captureEvent` exported from every SDK, this method requires that you pass it the current scope.\n   */\n   captureEvent(event, hint, currentScope) {\n    const eventId = uuid4();\n\n    // ensure we haven't captured this very object before\n    if (hint?.originalException && checkOrSetAlreadyCaught(hint.originalException)) {\n      DEBUG_BUILD && logger.log(ALREADY_SEEN_ERROR);\n      return eventId;\n    }\n\n    const hintWithEventId = {\n      event_id: eventId,\n      ...hint,\n    };\n\n    const sdkProcessingMetadata = event.sdkProcessingMetadata || {};\n    const capturedSpanScope = sdkProcessingMetadata.capturedSpanScope;\n    const capturedSpanIsolationScope = sdkProcessingMetadata.capturedSpanIsolationScope;\n\n    this._process(\n      this._captureEvent(event, hintWithEventId, capturedSpanScope || currentScope, capturedSpanIsolationScope),\n    );\n\n    return hintWithEventId.event_id;\n  }\n\n  /**\n   * Captures a session.\n   */\n   captureSession(session) {\n    this.sendSession(session);\n    // After sending, we set init false to indicate it's not the first occurrence\n    updateSession(session, { init: false });\n  }\n\n  /**\n   * Create a cron monitor check in and send it to Sentry. This method is not available on all clients.\n   *\n   * @param checkIn An object that describes a check in.\n   * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n   * to create a monitor automatically when sending a check in.\n   * @param scope An optional scope containing event metadata.\n   * @returns A string representing the id of the check in.\n   */\n\n  /**\n   * Get the current Dsn.\n   */\n   getDsn() {\n    return this._dsn;\n  }\n\n  /**\n   * Get the current options.\n   */\n   getOptions() {\n    return this._options;\n  }\n\n  /**\n   * Get the SDK metadata.\n   * @see SdkMetadata\n   */\n   getSdkMetadata() {\n    return this._options._metadata;\n  }\n\n  /**\n   * Returns the transport that is used by the client.\n   * Please note that the transport gets lazy initialized so it will only be there once the first event has been sent.\n   */\n   getTransport() {\n    return this._transport;\n  }\n\n  /**\n   * Wait for all events to be sent or the timeout to expire, whichever comes first.\n   *\n   * @param timeout Maximum time in ms the client should wait for events to be flushed. Omitting this parameter will\n   *   cause the client to wait until all events are sent before resolving the promise.\n   * @returns A promise that will resolve with `true` if all events are sent before the timeout, or `false` if there are\n   * still events in the queue when the timeout is reached.\n   */\n   flush(timeout) {\n    const transport = this._transport;\n    if (transport) {\n      this.emit('flush');\n      return this._isClientDoneProcessing(timeout).then(clientFinished => {\n        return transport.flush(timeout).then(transportFlushed => clientFinished && transportFlushed);\n      });\n    } else {\n      return resolvedSyncPromise(true);\n    }\n  }\n\n  /**\n   * Flush the event queue and set the client to `enabled = false`. See {@link Client.flush}.\n   *\n   * @param {number} timeout Maximum time in ms the client should wait before shutting down. Omitting this parameter will cause\n   *   the client to wait until all events are sent before disabling itself.\n   * @returns {Promise<boolean>} A promise which resolves to `true` if the flush completes successfully before the timeout, or `false` if\n   * it doesn't.\n   */\n   close(timeout) {\n    return this.flush(timeout).then(result => {\n      this.getOptions().enabled = false;\n      this.emit('close');\n      return result;\n    });\n  }\n\n  /**\n   * Get all installed event processors.\n   */\n   getEventProcessors() {\n    return this._eventProcessors;\n  }\n\n  /**\n   * Adds an event processor that applies to any event processed by this client.\n   */\n   addEventProcessor(eventProcessor) {\n    this._eventProcessors.push(eventProcessor);\n  }\n\n  /**\n   * Initialize this client.\n   * Call this after the client was set on a scope.\n   */\n   init() {\n    if (\n      this._isEnabled() ||\n      // Force integrations to be setup even if no DSN was set when we have\n      // Spotlight enabled. This is particularly important for browser as we\n      // don't support the `spotlight` option there and rely on the users\n      // adding the `spotlightBrowserIntegration()` to their integrations which\n      // wouldn't get initialized with the check below when there's no DSN set.\n      this._options.integrations.some(({ name }) => name.startsWith('Spotlight'))\n    ) {\n      this._setupIntegrations();\n    }\n  }\n\n  /**\n   * Gets an installed integration by its name.\n   *\n   * @returns {Integration|undefined} The installed integration or `undefined` if no integration with that `name` was installed.\n   */\n   getIntegrationByName(integrationName) {\n    return this._integrations[integrationName] ;\n  }\n\n  /**\n   * Add an integration to the client.\n   * This can be used to e.g. lazy load integrations.\n   * In most cases, this should not be necessary,\n   * and you're better off just passing the integrations via `integrations: []` at initialization time.\n   * However, if you find the need to conditionally load & add an integration, you can use `addIntegration` to do so.\n   */\n   addIntegration(integration) {\n    const isAlreadyInstalled = this._integrations[integration.name];\n\n    // This hook takes care of only installing if not already installed\n    setupIntegration(this, integration, this._integrations);\n    // Here we need to check manually to make sure to not run this multiple times\n    if (!isAlreadyInstalled) {\n      afterSetupIntegrations(this, [integration]);\n    }\n  }\n\n  /**\n   * Send a fully prepared event to Sentry.\n   */\n   sendEvent(event, hint = {}) {\n    this.emit('beforeSendEvent', event, hint);\n\n    let env = createEventEnvelope(event, this._dsn, this._options._metadata, this._options.tunnel);\n\n    for (const attachment of hint.attachments || []) {\n      env = addItemToEnvelope(env, createAttachmentEnvelopeItem(attachment));\n    }\n\n    const promise = this.sendEnvelope(env);\n    if (promise) {\n      promise.then(sendResponse => this.emit('afterSendEvent', event, sendResponse), null);\n    }\n  }\n\n  /**\n   * Send a session or session aggregrates to Sentry.\n   */\n   sendSession(session) {\n    // Backfill release and environment on session\n    const { release: clientReleaseOption, environment: clientEnvironmentOption = DEFAULT_ENVIRONMENT } = this._options;\n    if ('aggregates' in session) {\n      const sessionAttrs = session.attrs || {};\n      if (!sessionAttrs.release && !clientReleaseOption) {\n        DEBUG_BUILD && logger.warn(MISSING_RELEASE_FOR_SESSION_ERROR);\n        return;\n      }\n      sessionAttrs.release = sessionAttrs.release || clientReleaseOption;\n      sessionAttrs.environment = sessionAttrs.environment || clientEnvironmentOption;\n      session.attrs = sessionAttrs;\n    } else {\n      if (!session.release && !clientReleaseOption) {\n        DEBUG_BUILD && logger.warn(MISSING_RELEASE_FOR_SESSION_ERROR);\n        return;\n      }\n      session.release = session.release || clientReleaseOption;\n      session.environment = session.environment || clientEnvironmentOption;\n    }\n\n    this.emit('beforeSendSession', session);\n\n    const env = createSessionEnvelope(session, this._dsn, this._options._metadata, this._options.tunnel);\n\n    // sendEnvelope should not throw\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.sendEnvelope(env);\n  }\n\n  /**\n   * Record on the client that an event got dropped (ie, an event that will not be sent to Sentry).\n   */\n   recordDroppedEvent(reason, category, count = 1) {\n    if (this._options.sendClientReports) {\n      // We want to track each category (error, transaction, session, replay_event) separately\n      // but still keep the distinction between different type of outcomes.\n      // We could use nested maps, but it's much easier to read and type this way.\n      // A correct type for map-based implementation if we want to go that route\n      // would be `Partial<Record<SentryRequestType, Partial<Record<Outcome, number>>>>`\n      // With typescript 4.1 we could even use template literal types\n      const key = `${reason}:${category}`;\n      DEBUG_BUILD && logger.log(`Recording outcome: \"${key}\"${count > 1 ? ` (${count} times)` : ''}`);\n      this._outcomes[key] = (this._outcomes[key] || 0) + count;\n    }\n  }\n\n  /* eslint-disable @typescript-eslint/unified-signatures */\n  /**\n   * Register a callback for whenever a span is started.\n   * Receives the span as argument.\n   * @returns {() => void} A function that, when executed, removes the registered callback.\n   */\n\n  /**\n   * Register a hook on this client.\n   */\n   on(hook, callback) {\n    const hooks = (this._hooks[hook] = this._hooks[hook] || []);\n\n    // @ts-expect-error We assume the types are correct\n    hooks.push(callback);\n\n    // This function returns a callback execution handler that, when invoked,\n    // deregisters a callback. This is crucial for managing instances where callbacks\n    // need to be unregistered to prevent self-referencing in callback closures,\n    // ensuring proper garbage collection.\n    return () => {\n      // @ts-expect-error We assume the types are correct\n      const cbIndex = hooks.indexOf(callback);\n      if (cbIndex > -1) {\n        hooks.splice(cbIndex, 1);\n      }\n    };\n  }\n\n  /** Fire a hook whenever a span starts. */\n\n  /**\n   * Emit a hook that was previously registered via `on()`.\n   */\n   emit(hook, ...rest) {\n    const callbacks = this._hooks[hook];\n    if (callbacks) {\n      callbacks.forEach(callback => callback(...rest));\n    }\n  }\n\n  /**\n   * Send an envelope to Sentry.\n   */\n   sendEnvelope(envelope) {\n    this.emit('beforeEnvelope', envelope);\n\n    if (this._isEnabled() && this._transport) {\n      return this._transport.send(envelope).then(null, reason => {\n        DEBUG_BUILD && logger.error('Error while sending envelope:', reason);\n        return reason;\n      });\n    }\n\n    DEBUG_BUILD && logger.error('Transport disabled');\n\n    return resolvedSyncPromise({});\n  }\n\n  /* eslint-enable @typescript-eslint/unified-signatures */\n\n  /** Setup integrations for this client. */\n   _setupIntegrations() {\n    const { integrations } = this._options;\n    this._integrations = setupIntegrations(this, integrations);\n    afterSetupIntegrations(this, integrations);\n  }\n\n  /** Updates existing session based on the provided event */\n   _updateSessionFromEvent(session, event) {\n    let crashed = event.level === 'fatal';\n    let errored = false;\n    const exceptions = event.exception?.values;\n\n    if (exceptions) {\n      errored = true;\n\n      for (const ex of exceptions) {\n        const mechanism = ex.mechanism;\n        if (mechanism?.handled === false) {\n          crashed = true;\n          break;\n        }\n      }\n    }\n\n    // A session is updated and that session update is sent in only one of the two following scenarios:\n    // 1. Session with non terminal status and 0 errors + an error occurred -> Will set error count to 1 and send update\n    // 2. Session with non terminal status and 1 error + a crash occurred -> Will set status crashed and send update\n    const sessionNonTerminal = session.status === 'ok';\n    const shouldUpdateAndSend = (sessionNonTerminal && session.errors === 0) || (sessionNonTerminal && crashed);\n\n    if (shouldUpdateAndSend) {\n      updateSession(session, {\n        ...(crashed && { status: 'crashed' }),\n        errors: session.errors || Number(errored || crashed),\n      });\n      this.captureSession(session);\n    }\n  }\n\n  /**\n   * Determine if the client is finished processing. Returns a promise because it will wait `timeout` ms before saying\n   * \"no\" (resolving to `false`) in order to give the client a chance to potentially finish first.\n   *\n   * @param timeout The time, in ms, after which to resolve to `false` if the client is still busy. Passing `0` (or not\n   * passing anything) will make the promise wait as long as it takes for processing to finish before resolving to\n   * `true`.\n   * @returns A promise which will resolve to `true` if processing is already done or finishes before the timeout, and\n   * `false` otherwise\n   */\n   _isClientDoneProcessing(timeout) {\n    return new SyncPromise(resolve => {\n      let ticked = 0;\n      const tick = 1;\n\n      const interval = setInterval(() => {\n        if (this._numProcessing == 0) {\n          clearInterval(interval);\n          resolve(true);\n        } else {\n          ticked += tick;\n          if (timeout && ticked >= timeout) {\n            clearInterval(interval);\n            resolve(false);\n          }\n        }\n      }, tick);\n    });\n  }\n\n  /** Determines whether this SDK is enabled and a transport is present. */\n   _isEnabled() {\n    return this.getOptions().enabled !== false && this._transport !== undefined;\n  }\n\n  /**\n   * Adds common information to events.\n   *\n   * The information includes release and environment from `options`,\n   * breadcrumbs and context (extra, tags and user) from the scope.\n   *\n   * Information that is already present in the event is never overwritten. For\n   * nested objects, such as the context, keys are merged.\n   *\n   * @param event The original event.\n   * @param hint May contain additional information about the original exception.\n   * @param currentScope A scope containing event metadata.\n   * @returns A new event with more information.\n   */\n   _prepareEvent(\n    event,\n    hint,\n    currentScope,\n    isolationScope,\n  ) {\n    const options = this.getOptions();\n    const integrations = Object.keys(this._integrations);\n    if (!hint.integrations && integrations?.length) {\n      hint.integrations = integrations;\n    }\n\n    this.emit('preprocessEvent', event, hint);\n\n    if (!event.type) {\n      isolationScope.setLastEventId(event.event_id || hint.event_id);\n    }\n\n    return prepareEvent(options, event, hint, currentScope, this, isolationScope).then(evt => {\n      if (evt === null) {\n        return evt;\n      }\n\n      this.emit('postprocessEvent', evt, hint);\n\n      evt.contexts = {\n        trace: getTraceContextFromScope(currentScope),\n        ...evt.contexts,\n      };\n\n      const dynamicSamplingContext = getDynamicSamplingContextFromScope(this, currentScope);\n\n      evt.sdkProcessingMetadata = {\n        dynamicSamplingContext,\n        ...evt.sdkProcessingMetadata,\n      };\n\n      return evt;\n    });\n  }\n\n  /**\n   * Processes the event and logs an error in case of rejection\n   * @param event\n   * @param hint\n   * @param scope\n   */\n   _captureEvent(\n    event,\n    hint = {},\n    currentScope = getCurrentScope(),\n    isolationScope = getIsolationScope(),\n  ) {\n    if (DEBUG_BUILD && isErrorEvent(event)) {\n      logger.log(`Captured error event \\`${getPossibleEventMessages(event)[0] || '<unknown>'}\\``);\n    }\n\n    return this._processEvent(event, hint, currentScope, isolationScope).then(\n      finalEvent => {\n        return finalEvent.event_id;\n      },\n      reason => {\n        if (DEBUG_BUILD) {\n          if (_isDoNotSendEventError(reason)) {\n            logger.log(reason.message);\n          } else if (_isInternalError(reason)) {\n            logger.warn(reason.message);\n          } else {\n            logger.warn(reason);\n          }\n        }\n        return undefined;\n      },\n    );\n  }\n\n  /**\n   * Processes an event (either error or message) and sends it to Sentry.\n   *\n   * This also adds breadcrumbs and context information to the event. However,\n   * platform specific meta data (such as the User's IP address) must be added\n   * by the SDK implementor.\n   *\n   *\n   * @param event The event to send to Sentry.\n   * @param hint May contain additional information about the original exception.\n   * @param currentScope A scope containing event metadata.\n   * @returns A SyncPromise that resolves with the event or rejects in case event was/will not be send.\n   */\n   _processEvent(\n    event,\n    hint,\n    currentScope,\n    isolationScope,\n  ) {\n    const options = this.getOptions();\n    const { sampleRate } = options;\n\n    const isTransaction = isTransactionEvent(event);\n    const isError = isErrorEvent(event);\n    const eventType = event.type || 'error';\n    const beforeSendLabel = `before send for type \\`${eventType}\\``;\n\n    // 1.0 === 100% events are sent\n    // 0.0 === 0% events are sent\n    // Sampling for transaction happens somewhere else\n    const parsedSampleRate = typeof sampleRate === 'undefined' ? undefined : parseSampleRate(sampleRate);\n    if (isError && typeof parsedSampleRate === 'number' && Math.random() > parsedSampleRate) {\n      this.recordDroppedEvent('sample_rate', 'error');\n      return rejectedSyncPromise(\n        _makeDoNotSendEventError(\n          `Discarding event because it's not included in the random sample (sampling rate = ${sampleRate})`,\n        ),\n      );\n    }\n\n    const dataCategory = (eventType === 'replay_event' ? 'replay' : eventType) ;\n\n    return this._prepareEvent(event, hint, currentScope, isolationScope)\n      .then(prepared => {\n        if (prepared === null) {\n          this.recordDroppedEvent('event_processor', dataCategory);\n          throw _makeDoNotSendEventError('An event processor returned `null`, will not send event.');\n        }\n\n        const isInternalException = hint.data && (hint.data ).__sentry__ === true;\n        if (isInternalException) {\n          return prepared;\n        }\n\n        const result = processBeforeSend(this, options, prepared, hint);\n        return _validateBeforeSendResult(result, beforeSendLabel);\n      })\n      .then(processedEvent => {\n        if (processedEvent === null) {\n          this.recordDroppedEvent('before_send', dataCategory);\n          if (isTransaction) {\n            const spans = event.spans || [];\n            // the transaction itself counts as one span, plus all the child spans that are added\n            const spanCount = 1 + spans.length;\n            this.recordDroppedEvent('before_send', 'span', spanCount);\n          }\n          throw _makeDoNotSendEventError(`${beforeSendLabel} returned \\`null\\`, will not send event.`);\n        }\n\n        const session = currentScope.getSession() || isolationScope.getSession();\n        if (isError && session) {\n          this._updateSessionFromEvent(session, processedEvent);\n        }\n\n        if (isTransaction) {\n          const spanCountBefore = processedEvent.sdkProcessingMetadata?.spanCountBeforeProcessing || 0;\n          const spanCountAfter = processedEvent.spans ? processedEvent.spans.length : 0;\n\n          const droppedSpanCount = spanCountBefore - spanCountAfter;\n          if (droppedSpanCount > 0) {\n            this.recordDroppedEvent('before_send', 'span', droppedSpanCount);\n          }\n        }\n\n        // None of the Sentry built event processor will update transaction name,\n        // so if the transaction name has been changed by an event processor, we know\n        // it has to come from custom event processor added by a user\n        const transactionInfo = processedEvent.transaction_info;\n        if (isTransaction && transactionInfo && processedEvent.transaction !== event.transaction) {\n          const source = 'custom';\n          processedEvent.transaction_info = {\n            ...transactionInfo,\n            source,\n          };\n        }\n\n        this.sendEvent(processedEvent, hint);\n        return processedEvent;\n      })\n      .then(null, reason => {\n        if (_isDoNotSendEventError(reason) || _isInternalError(reason)) {\n          throw reason;\n        }\n\n        this.captureException(reason, {\n          data: {\n            __sentry__: true,\n          },\n          originalException: reason,\n        });\n        throw _makeInternalError(\n          `Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\\nReason: ${reason}`,\n        );\n      });\n  }\n\n  /**\n   * Occupies the client with processing and event\n   */\n   _process(promise) {\n    this._numProcessing++;\n    void promise.then(\n      value => {\n        this._numProcessing--;\n        return value;\n      },\n      reason => {\n        this._numProcessing--;\n        return reason;\n      },\n    );\n  }\n\n  /**\n   * Clears outcomes on this client and returns them.\n   */\n   _clearOutcomes() {\n    const outcomes = this._outcomes;\n    this._outcomes = {};\n    return Object.entries(outcomes).map(([key, quantity]) => {\n      const [reason, category] = key.split(':') ;\n      return {\n        reason,\n        category,\n        quantity,\n      };\n    });\n  }\n\n  /**\n   * Sends client reports as an envelope.\n   */\n   _flushOutcomes() {\n    DEBUG_BUILD && logger.log('Flushing outcomes...');\n\n    const outcomes = this._clearOutcomes();\n\n    if (outcomes.length === 0) {\n      DEBUG_BUILD && logger.log('No outcomes to send');\n      return;\n    }\n\n    // This is really the only place where we want to check for a DSN and only send outcomes then\n    if (!this._dsn) {\n      DEBUG_BUILD && logger.log('No dsn provided, will not send outcomes');\n      return;\n    }\n\n    DEBUG_BUILD && logger.log('Sending outcomes:', outcomes);\n\n    const envelope = createClientReportEnvelope(outcomes, this._options.tunnel && dsnToString(this._dsn));\n\n    // sendEnvelope should not throw\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.sendEnvelope(envelope);\n  }\n\n  /**\n   * Creates an {@link Event} from all inputs to `captureException` and non-primitive inputs to `captureMessage`.\n   */\n\n}\n\n/**\n * @deprecated Use `Client` instead. This alias may be removed in a future major version.\n */\n// TODO(v10): Remove\n\n/**\n * @deprecated Use `Client` instead. This alias may be removed in a future major version.\n */\n// TODO(v10): Remove\nconst BaseClient = Client;\n\n/**\n * Verifies that return value of configured `beforeSend` or `beforeSendTransaction` is of expected type, and returns the value if so.\n */\nfunction _validateBeforeSendResult(\n  beforeSendResult,\n  beforeSendLabel,\n) {\n  const invalidValueError = `${beforeSendLabel} must return \\`null\\` or a valid event.`;\n  if (isThenable(beforeSendResult)) {\n    return beforeSendResult.then(\n      event => {\n        if (!isPlainObject(event) && event !== null) {\n          throw _makeInternalError(invalidValueError);\n        }\n        return event;\n      },\n      e => {\n        throw _makeInternalError(`${beforeSendLabel} rejected with ${e}`);\n      },\n    );\n  } else if (!isPlainObject(beforeSendResult) && beforeSendResult !== null) {\n    throw _makeInternalError(invalidValueError);\n  }\n  return beforeSendResult;\n}\n\n/**\n * Process the matching `beforeSendXXX` callback.\n */\nfunction processBeforeSend(\n  client,\n  options,\n  event,\n  hint,\n) {\n  const { beforeSend, beforeSendTransaction, beforeSendSpan } = options;\n  let processedEvent = event;\n\n  if (isErrorEvent(processedEvent) && beforeSend) {\n    return beforeSend(processedEvent, hint);\n  }\n\n  if (isTransactionEvent(processedEvent)) {\n    if (beforeSendSpan) {\n      // process root span\n      const processedRootSpanJson = beforeSendSpan(convertTransactionEventToSpanJson(processedEvent));\n      if (!processedRootSpanJson) {\n        showSpanDropWarning();\n      } else {\n        // update event with processed root span values\n        processedEvent = merge(event, convertSpanJsonToTransactionEvent(processedRootSpanJson));\n      }\n\n      // process child spans\n      if (processedEvent.spans) {\n        const processedSpans = [];\n        for (const span of processedEvent.spans) {\n          const processedSpan = beforeSendSpan(span);\n          if (!processedSpan) {\n            showSpanDropWarning();\n            processedSpans.push(span);\n          } else {\n            processedSpans.push(processedSpan);\n          }\n        }\n        processedEvent.spans = processedSpans;\n      }\n    }\n\n    if (beforeSendTransaction) {\n      if (processedEvent.spans) {\n        // We store the # of spans before processing in SDK metadata,\n        // so we can compare it afterwards to determine how many spans were dropped\n        const spanCountBefore = processedEvent.spans.length;\n        processedEvent.sdkProcessingMetadata = {\n          ...event.sdkProcessingMetadata,\n          spanCountBeforeProcessing: spanCountBefore,\n        };\n      }\n      return beforeSendTransaction(processedEvent , hint);\n    }\n  }\n\n  return processedEvent;\n}\n\nfunction isErrorEvent(event) {\n  return event.type === undefined;\n}\n\nfunction isTransactionEvent(event) {\n  return event.type === 'transaction';\n}\n\n/** Extract trace information from scope */\nfunction _getTraceInfoFromScope(\n  client,\n  scope,\n) {\n  if (!scope) {\n    return [undefined, undefined];\n  }\n\n  const span = _getSpanForScope(scope);\n  const traceContext = span ? spanToTraceContext(span) : getTraceContextFromScope(scope);\n  const dynamicSamplingContext = span\n    ? getDynamicSamplingContextFromSpan(span)\n    : getDynamicSamplingContextFromScope(client, scope);\n  return [dynamicSamplingContext, traceContext];\n}\n\nexport { BaseClient, Client, _getTraceInfoFromScope };\n//# sourceMappingURL=client.js.map\n", "import { dsnToString } from '../utils-hoist/dsn.js';\nimport { createEnvelope } from '../utils-hoist/envelope.js';\n\n/**\n * Creates a log container envelope item for a list of logs.\n *\n * @param items - The logs to include in the envelope.\n * @returns The created log container envelope item.\n */\nfunction createLogContainerEnvelopeItem(items) {\n  return [\n    {\n      type: 'log',\n      item_count: items.length,\n      content_type: 'application/vnd.sentry.items.log+json',\n    },\n    {\n      items,\n    },\n  ];\n}\n\n/**\n * Creates an envelope for a list of logs.\n *\n * Logs from multiple traces can be included in the same envelope.\n *\n * @param logs - The logs to include in the envelope.\n * @param metadata - The metadata to include in the envelope.\n * @param tunnel - The tunnel to include in the envelope.\n * @param dsn - The DSN to include in the envelope.\n * @returns The created envelope.\n */\nfunction createLogEnvelope(\n  logs,\n  metadata,\n  tunnel,\n  dsn,\n) {\n  const headers = {};\n\n  if (metadata?.sdk) {\n    headers.sdk = {\n      name: metadata.sdk.name,\n      version: metadata.sdk.version,\n    };\n  }\n\n  if (!!tunnel && !!dsn) {\n    headers.dsn = dsnToString(dsn);\n  }\n\n  return createEnvelope(headers, [createLogContainerEnvelopeItem(logs)]);\n}\n\nexport { createLogContainerEnvelopeItem, createLogEnvelope };\n//# sourceMappingURL=envelope.js.map\n", "import { _getTraceInfoFromScope } from '../client.js';\nimport { getClient, getCurrentScope } from '../currentScopes.js';\nimport { DEBUG_BUILD } from '../debug-build.js';\nimport { _getSpanForScope } from '../utils/spanOnScope.js';\nimport { isParameterizedString } from '../utils-hoist/is.js';\nimport { logger } from '../utils-hoist/logger.js';\nimport { timestampInSeconds } from '../utils-hoist/time.js';\nimport { GLOBAL_OBJ } from '../utils-hoist/worldwide.js';\nimport { SEVERITY_TEXT_TO_SEVERITY_NUMBER } from './constants.js';\nimport { createLogEnvelope } from './envelope.js';\n\nconst MAX_LOG_BUFFER_SIZE = 100;\n\n// The reference to the Client <> LogBuffer map is stored to ensure it's always the same\nGLOBAL_OBJ._sentryClientToLogBufferMap = new WeakMap();\n\n/**\n * Converts a log attribute to a serialized log attribute.\n *\n * @param key - The key of the log attribute.\n * @param value - The value of the log attribute.\n * @returns The serialized log attribute.\n */\nfunction logAttributeToSerializedLogAttribute(value) {\n  switch (typeof value) {\n    case 'number':\n      if (Number.isInteger(value)) {\n        return {\n          value,\n          type: 'integer',\n        };\n      }\n      return {\n        value,\n        type: 'double',\n      };\n    case 'boolean':\n      return {\n        value,\n        type: 'boolean',\n      };\n    case 'string':\n      return {\n        value,\n        type: 'string',\n      };\n    default: {\n      let stringValue = '';\n      try {\n        stringValue = JSON.stringify(value) ?? '';\n      } catch {\n        // Do nothing\n      }\n      return {\n        value: stringValue,\n        type: 'string',\n      };\n    }\n  }\n}\n\n/**\n * Captures a log event and sends it to Sentry.\n *\n * @param log - The log event to capture.\n * @param scope - A scope. Uses the current scope if not provided.\n * @param client - A client. Uses the current client if not provided.\n *\n * @experimental This method will experience breaking changes. This is not yet part of\n * the stable Sentry SDK API and can be changed or removed without warning.\n */\nfunction _INTERNAL_captureLog(\n  beforeLog,\n  client = getClient(),\n  scope = getCurrentScope(),\n) {\n  if (!client) {\n    DEBUG_BUILD && logger.warn('No client available to capture log.');\n    return;\n  }\n\n  const { _experiments, release, environment } = client.getOptions();\n  const { enableLogs = false, beforeSendLog } = _experiments ?? {};\n  if (!enableLogs) {\n    DEBUG_BUILD && logger.warn('logging option not enabled, log will not be captured.');\n    return;\n  }\n\n  const [, traceContext] = _getTraceInfoFromScope(client, scope);\n\n  const processedLogAttributes = {\n    ...beforeLog.attributes,\n  };\n\n  if (release) {\n    processedLogAttributes['sentry.release'] = release;\n  }\n\n  if (environment) {\n    processedLogAttributes['sentry.environment'] = environment;\n  }\n\n  const { sdk } = client.getSdkMetadata() ?? {};\n  if (sdk) {\n    processedLogAttributes['sentry.sdk.name'] = sdk.name;\n    processedLogAttributes['sentry.sdk.version'] = sdk.version;\n  }\n\n  const beforeLogMessage = beforeLog.message;\n  if (isParameterizedString(beforeLogMessage)) {\n    const { __sentry_template_string__, __sentry_template_values__ = [] } = beforeLogMessage;\n    processedLogAttributes['sentry.message.template'] = __sentry_template_string__;\n    __sentry_template_values__.forEach((param, index) => {\n      processedLogAttributes[`sentry.message.parameter.${index}`] = param;\n    });\n  }\n\n  const span = _getSpanForScope(scope);\n  if (span) {\n    // Add the parent span ID to the log attributes for trace context\n    processedLogAttributes['sentry.trace.parent_span_id'] = span.spanContext().spanId;\n  }\n\n  const processedLog = { ...beforeLog, attributes: processedLogAttributes };\n\n  client.emit('beforeCaptureLog', processedLog);\n\n  const log = beforeSendLog ? beforeSendLog(processedLog) : processedLog;\n  if (!log) {\n    client.recordDroppedEvent('before_send', 'log_item', 1);\n    DEBUG_BUILD && logger.warn('beforeSendLog returned null, log will not be captured.');\n    return;\n  }\n\n  const { level, message, attributes = {}, severityNumber } = log;\n\n  const serializedLog = {\n    timestamp: timestampInSeconds(),\n    level,\n    body: message,\n    trace_id: traceContext?.trace_id,\n    severity_number: severityNumber ?? SEVERITY_TEXT_TO_SEVERITY_NUMBER[level],\n    attributes: Object.keys(attributes).reduce(\n      (acc, key) => {\n        acc[key] = logAttributeToSerializedLogAttribute(attributes[key]);\n        return acc;\n      },\n      {} ,\n    ),\n  };\n\n  const logBuffer = _INTERNAL_getLogBuffer(client);\n  if (logBuffer === undefined) {\n    GLOBAL_OBJ._sentryClientToLogBufferMap?.set(client, [serializedLog]);\n  } else {\n    GLOBAL_OBJ._sentryClientToLogBufferMap?.set(client, [...logBuffer, serializedLog]);\n    if (logBuffer.length >= MAX_LOG_BUFFER_SIZE) {\n      _INTERNAL_flushLogsBuffer(client, logBuffer);\n    }\n  }\n\n  client.emit('afterCaptureLog', log);\n}\n\n/**\n * Flushes the logs buffer to Sentry.\n *\n * @param client - A client.\n * @param maybeLogBuffer - A log buffer. Uses the log buffer for the given client if not provided.\n *\n * @experimental This method will experience breaking changes. This is not yet part of\n * the stable Sentry SDK API and can be changed or removed without warning.\n */\nfunction _INTERNAL_flushLogsBuffer(client, maybeLogBuffer) {\n  const logBuffer = maybeLogBuffer ?? _INTERNAL_getLogBuffer(client) ?? [];\n  if (logBuffer.length === 0) {\n    return;\n  }\n\n  const clientOptions = client.getOptions();\n  const envelope = createLogEnvelope(logBuffer, clientOptions._metadata, clientOptions.tunnel, client.getDsn());\n\n  // Clear the log buffer after envelopes have been constructed.\n  GLOBAL_OBJ._sentryClientToLogBufferMap?.set(client, []);\n\n  client.emit('flushLogs');\n\n  // sendEnvelope should not throw\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  client.sendEnvelope(envelope);\n}\n\n/**\n * Returns the log buffer for a given client.\n *\n * Exported for testing purposes.\n *\n * @param client - The client to get the log buffer for.\n * @returns The log buffer for the given client.\n */\nfunction _INTERNAL_getLogBuffer(client) {\n  return GLOBAL_OBJ._sentryClientToLogBufferMap?.get(client);\n}\n\nexport { _INTERNAL_captureLog, _INTERNAL_flushLogsBuffer, _INTERNAL_getLogBuffer, logAttributeToSerializedLogAttribute };\n//# sourceMappingURL=exports.js.map\n", "import { getCurrentScope } from './currentScopes.js';\nimport { DEBUG_BUILD } from './debug-build.js';\nimport { logger, consoleSandbox } from './utils-hoist/logger.js';\n\n/** A class object that can instantiate Client objects. */\n\n/**\n * Internal function to create a new SDK client instance. The client is\n * installed and then bound to the current scope.\n *\n * @param clientClass The client class to instantiate.\n * @param options Options to pass to the client.\n */\nfunction initAndBind(\n  clientClass,\n  options,\n) {\n  if (options.debug === true) {\n    if (DEBUG_BUILD) {\n      logger.enable();\n    } else {\n      // use `console.warn` rather than `logger.warn` since by non-debug bundles have all `logger.x` statements stripped\n      consoleSandbox(() => {\n        // eslint-disable-next-line no-console\n        console.warn('[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.');\n      });\n    }\n  }\n  const scope = getCurrentScope();\n  scope.update(options.initialScope);\n\n  const client = new clientClass(options);\n  setCurrentClient(client);\n  client.init();\n  return client;\n}\n\n/**\n * Make the given client the current client.\n */\nfunction setCurrentClient(client) {\n  getCurrentScope().setClient(client);\n}\n\nexport { initAndBind, setCurrentClient };\n//# sourceMappingURL=sdk.js.map\n", "import { SyncPromise, resolvedSyncPromise, rejectedSyncPromise } from './syncpromise.js';\n\nconst SENTRY_BUFFER_FULL_ERROR = Symbol.for('SentryBufferFullError');\n\n/**\n * Creates an new PromiseBuffer object with the specified limit\n * @param limit max number of promises that can be stored in the buffer\n */\nfunction makePromiseBuffer(limit) {\n  const buffer = [];\n\n  function isReady() {\n    return limit === undefined || buffer.length < limit;\n  }\n\n  /**\n   * Remove a promise from the queue.\n   *\n   * @param task Can be any PromiseLike<T>\n   * @returns Removed promise.\n   */\n  function remove(task) {\n    return buffer.splice(buffer.indexOf(task), 1)[0] || Promise.resolve(undefined);\n  }\n\n  /**\n   * Add a promise (representing an in-flight action) to the queue, and set it to remove itself on fulfillment.\n   *\n   * @param taskProducer A function producing any PromiseLike<T>; In previous versions this used to be `task:\n   *        PromiseLike<T>`, but under that model, Promises were instantly created on the call-site and their executor\n   *        functions therefore ran immediately. Thus, even if the buffer was full, the action still happened. By\n   *        requiring the promise to be wrapped in a function, we can defer promise creation until after the buffer\n   *        limit check.\n   * @returns The original promise.\n   */\n  function add(taskProducer) {\n    if (!isReady()) {\n      return rejectedSyncPromise(SENTRY_BUFFER_FULL_ERROR);\n    }\n\n    // start the task and add its promise to the queue\n    const task = taskProducer();\n    if (buffer.indexOf(task) === -1) {\n      buffer.push(task);\n    }\n    void task\n      .then(() => remove(task))\n      // Use `then(null, rejectionHandler)` rather than `catch(rejectionHandler)` so that we can use `PromiseLike`\n      // rather than `Promise`. `PromiseLike` doesn't have a `.catch` method, making its polyfill smaller. (ES5 didn't\n      // have promises, so TS has to polyfill when down-compiling.)\n      .then(null, () =>\n        remove(task).then(null, () => {\n          // We have to add another catch here because `remove()` starts a new promise chain.\n        }),\n      );\n    return task;\n  }\n\n  /**\n   * Wait for all promises in the queue to resolve or for timeout to expire, whichever comes first.\n   *\n   * @param timeout The time, in ms, after which to resolve to `false` if the queue is still non-empty. Passing `0` (or\n   * not passing anything) will make the promise wait as long as it takes for the queue to drain before resolving to\n   * `true`.\n   * @returns A promise which will resolve to `true` if the queue is already empty or drains before the timeout, and\n   * `false` otherwise\n   */\n  function drain(timeout) {\n    return new SyncPromise((resolve, reject) => {\n      let counter = buffer.length;\n\n      if (!counter) {\n        return resolve(true);\n      }\n\n      // wait for `timeout` ms and then resolve to `false` (if not cancelled first)\n      const capturedSetTimeout = setTimeout(() => {\n        if (timeout && timeout > 0) {\n          resolve(false);\n        }\n      }, timeout);\n\n      // if all promises resolve in time, cancel the timer and resolve to `true`\n      buffer.forEach(item => {\n        void resolvedSyncPromise(item).then(() => {\n          if (!--counter) {\n            clearTimeout(capturedSetTimeout);\n            resolve(true);\n          }\n        }, reject);\n      });\n    });\n  }\n\n  return {\n    $: buffer,\n    add,\n    drain,\n  };\n}\n\nexport { SENTRY_BUFFER_FULL_ERROR, makePromiseBuffer };\n//# sourceMappingURL=promisebuffer.js.map\n", "// Intentionally keeping the key broad, as we don't know for sure what rate limit headers get returned from backend\n\nconst DEFAULT_RETRY_AFTER = 60 * 1000; // 60 seconds\n\n/**\n * Extracts Retry-After value from the request header or returns default value\n * @param header string representation of 'Retry-After' header\n * @param now current unix timestamp\n *\n */\nfunction parseRetryAfterHeader(header, now = Date.now()) {\n  const headerDelay = parseInt(`${header}`, 10);\n  if (!isNaN(headerDelay)) {\n    return headerDelay * 1000;\n  }\n\n  const headerDate = Date.parse(`${header}`);\n  if (!isNaN(headerDate)) {\n    return headerDate - now;\n  }\n\n  return DEFAULT_RETRY_AFTER;\n}\n\n/**\n * Gets the time that the given category is disabled until for rate limiting.\n * In case no category-specific limit is set but a general rate limit across all categories is active,\n * that time is returned.\n *\n * @return the time in ms that the category is disabled until or 0 if there's no active rate limit.\n */\nfunction disabledUntil(limits, dataCategory) {\n  return limits[dataCategory] || limits.all || 0;\n}\n\n/**\n * Checks if a category is rate limited\n */\nfunction isRateLimited(limits, dataCategory, now = Date.now()) {\n  return disabledUntil(limits, dataCategory) > now;\n}\n\n/**\n * Update ratelimits from incoming headers.\n *\n * @return the updated RateLimits object.\n */\nfunction updateRateLimits(\n  limits,\n  { statusCode, headers },\n  now = Date.now(),\n) {\n  const updatedRateLimits = {\n    ...limits,\n  };\n\n  // \"The name is case-insensitive.\"\n  // https://developer.mozilla.org/en-US/docs/Web/API/Headers/get\n  const rateLimitHeader = headers?.['x-sentry-rate-limits'];\n  const retryAfterHeader = headers?.['retry-after'];\n\n  if (rateLimitHeader) {\n    /**\n     * rate limit headers are of the form\n     *     <header>,<header>,..\n     * where each <header> is of the form\n     *     <retry_after>: <categories>: <scope>: <reason_code>: <namespaces>\n     * where\n     *     <retry_after> is a delay in seconds\n     *     <categories> is the event type(s) (error, transaction, etc) being rate limited and is of the form\n     *         <category>;<category>;...\n     *     <scope> is what's being limited (org, project, or key) - ignored by SDK\n     *     <reason_code> is an arbitrary string like \"org_quota\" - ignored by SDK\n     *     <namespaces> Semicolon-separated list of metric namespace identifiers. Defines which namespace(s) will be affected.\n     *         Only present if rate limit applies to the metric_bucket data category.\n     */\n    for (const limit of rateLimitHeader.trim().split(',')) {\n      const [retryAfter, categories, , , namespaces] = limit.split(':', 5) ;\n      const headerDelay = parseInt(retryAfter, 10);\n      const delay = (!isNaN(headerDelay) ? headerDelay : 60) * 1000; // 60sec default\n      if (!categories) {\n        updatedRateLimits.all = now + delay;\n      } else {\n        for (const category of categories.split(';')) {\n          if (category === 'metric_bucket') {\n            // namespaces will be present when category === 'metric_bucket'\n            if (!namespaces || namespaces.split(';').includes('custom')) {\n              updatedRateLimits[category] = now + delay;\n            }\n          } else {\n            updatedRateLimits[category] = now + delay;\n          }\n        }\n      }\n    }\n  } else if (retryAfterHeader) {\n    updatedRateLimits.all = now + parseRetryAfterHeader(retryAfterHeader, now);\n  } else if (statusCode === 429) {\n    updatedRateLimits.all = now + 60 * 1000;\n  }\n\n  return updatedRateLimits;\n}\n\nexport { DEFAULT_RETRY_AFTER, disabledUntil, isRateLimited, parseRetryAfterHeader, updateRateLimits };\n//# sourceMappingURL=ratelimit.js.map\n", "import { DEBUG_BUILD } from '../debug-build.js';\nimport { forEachEnvelopeItem, envelopeItemTypeToDataCategory, createEnvelope, serializeEnvelope } from '../utils-hoist/envelope.js';\nimport { logger } from '../utils-hoist/logger.js';\nimport { makePromiseBuffer, SENTRY_BUFFER_FULL_ERROR } from '../utils-hoist/promisebuffer.js';\nimport { isRateLimited, updateRateLimits } from '../utils-hoist/ratelimit.js';\nimport { resolvedSyncPromise } from '../utils-hoist/syncpromise.js';\n\nconst DEFAULT_TRANSPORT_BUFFER_SIZE = 64;\n\n/**\n * Creates an instance of a Sentry `Transport`\n *\n * @param options\n * @param makeRequest\n */\nfunction createTransport(\n  options,\n  makeRequest,\n  buffer = makePromiseBuffer(\n    options.bufferSize || DEFAULT_TRANSPORT_BUFFER_SIZE,\n  ),\n) {\n  let rateLimits = {};\n  const flush = (timeout) => buffer.drain(timeout);\n\n  function send(envelope) {\n    const filteredEnvelopeItems = [];\n\n    // Drop rate limited items from envelope\n    forEachEnvelopeItem(envelope, (item, type) => {\n      const dataCategory = envelopeItemTypeToDataCategory(type);\n      if (isRateLimited(rateLimits, dataCategory)) {\n        options.recordDroppedEvent('ratelimit_backoff', dataCategory);\n      } else {\n        filteredEnvelopeItems.push(item);\n      }\n    });\n\n    // Skip sending if envelope is empty after filtering out rate limited events\n    if (filteredEnvelopeItems.length === 0) {\n      return resolvedSyncPromise({});\n    }\n\n    const filteredEnvelope = createEnvelope(envelope[0], filteredEnvelopeItems );\n\n    // Creates client report for each item in an envelope\n    const recordEnvelopeLoss = (reason) => {\n      forEachEnvelopeItem(filteredEnvelope, (item, type) => {\n        options.recordDroppedEvent(reason, envelopeItemTypeToDataCategory(type));\n      });\n    };\n\n    const requestTask = () =>\n      makeRequest({ body: serializeEnvelope(filteredEnvelope) }).then(\n        response => {\n          // We don't want to throw on NOK responses, but we want to at least log them\n          if (response.statusCode !== undefined && (response.statusCode < 200 || response.statusCode >= 300)) {\n            DEBUG_BUILD && logger.warn(`Sentry responded with status code ${response.statusCode} to sent event.`);\n          }\n\n          rateLimits = updateRateLimits(rateLimits, response);\n          return response;\n        },\n        error => {\n          recordEnvelopeLoss('network_error');\n          DEBUG_BUILD && logger.error('Encountered error running transport request:', error);\n          throw error;\n        },\n      );\n\n    return buffer.add(requestTask).then(\n      result => result,\n      error => {\n        if (error === SENTRY_BUFFER_FULL_ERROR) {\n          DEBUG_BUILD && logger.error('Skipped sending event because buffer is full.');\n          recordEnvelopeLoss('queue_overflow');\n          return resolvedSyncPromise({});\n        } else {\n          throw error;\n        }\n      },\n    );\n  }\n\n  return {\n    send,\n    flush,\n  };\n}\n\nexport { DEFAULT_TRANSPORT_BUFFER_SIZE, createTransport };\n//# sourceMappingURL=base.js.map\n", "// By default, we want to infer the IP address, unless this is explicitly set to `null`\n// We do this after all other processing is done\n// If `ip_address` is explicitly set to `null` or a value, we leave it as is\n\n/**\n * @internal\n */\nfunction addAutoIpAddressToUser(objWithMaybeUser) {\n  if (objWithMaybeUser.user?.ip_address === undefined) {\n    objWithMaybeUser.user = {\n      ...objWithMaybeUser.user,\n      ip_address: '{{auto}}',\n    };\n  }\n}\n\n/**\n * @internal\n */\nfunction addAutoIpAddressToSession(session) {\n  if ('aggregates' in session) {\n    if (session.attrs?.['ip_address'] === undefined) {\n      session.attrs = {\n        ...session.attrs,\n        ip_address: '{{auto}}',\n      };\n    }\n  } else {\n    if (session.ipAddress === undefined) {\n      session.ipAddress = '{{auto}}';\n    }\n  }\n}\n\nexport { addAutoIpAddressToSession, addAutoIpAddressToUser };\n//# sourceMappingURL=ipAddress.js.map\n", "import { SDK_VERSION } from '../utils-hoist/version.js';\n\n/**\n * A builder for the SDK metadata in the options for the SDK initialization.\n *\n * Note: This function is identical to `buildMetadata` in Remix and NextJS and SvelteKit.\n * We don't extract it for bundle size reasons.\n * @see https://github.com/getsentry/sentry-javascript/pull/7404\n * @see https://github.com/getsentry/sentry-javascript/pull/4196\n *\n * If you make changes to this function consider updating the others as well.\n *\n * @param options SDK options object that gets mutated\n * @param names list of package names\n */\nfunction applySdkMetadata(options, name, names = [name], source = 'npm') {\n  const metadata = options._metadata || {};\n\n  if (!metadata.sdk) {\n    metadata.sdk = {\n      name: `sentry.javascript.${name}`,\n      packages: names.map(name => ({\n        name: `${source}:@sentry/${name}`,\n        version: SDK_VERSION,\n      })),\n      version: SDK_VERSION,\n    };\n  }\n\n  options._metadata = metadata;\n}\n\nexport { applySdkMetadata };\n//# sourceMappingURL=sdkMetadata.js.map\n", "import { getClient, getIsolationScope } from './currentScopes.js';\nimport { consoleSandbox } from './utils-hoist/logger.js';\nimport { dateTimestampInSeconds } from './utils-hoist/time.js';\n\n/**\n * Default maximum number of breadcrumbs added to an event. Can be overwritten\n * with {@link Options.maxBreadcrumbs}.\n */\nconst DEFAULT_BREADCRUMBS = 100;\n\n/**\n * Records a new breadcrumb which will be attached to future events.\n *\n * Breadcrumbs will be added to subsequent events to provide more context on\n * user's actions prior to an error or crash.\n */\nfunction addBreadcrumb(breadcrumb, hint) {\n  const client = getClient();\n  const isolationScope = getIsolationScope();\n\n  if (!client) return;\n\n  const { beforeBreadcrumb = null, maxBreadcrumbs = DEFAULT_BREADCRUMBS } = client.getOptions();\n\n  if (maxBreadcrumbs <= 0) return;\n\n  const timestamp = dateTimestampInSeconds();\n  const mergedBreadcrumb = { timestamp, ...breadcrumb };\n  const finalBreadcrumb = beforeBreadcrumb\n    ? (consoleSandbox(() => beforeBreadcrumb(mergedBreadcrumb, hint)) )\n    : mergedBreadcrumb;\n\n  if (finalBreadcrumb === null) return;\n\n  if (client.emit) {\n    client.emit('beforeAddBreadcrumb', finalBreadcrumb, hint);\n  }\n\n  isolationScope.addBreadcrumb(finalBreadcrumb, maxBreadcrumbs);\n}\n\nexport { addBreadcrumb };\n//# sourceMappingURL=breadcrumbs.js.map\n", "import { getClient } from '../currentScopes.js';\nimport { defineIntegration } from '../integration.js';\nimport { getOriginalFunction } from '../utils-hoist/object.js';\n\nlet originalFunctionToString;\n\nconst INTEGRATION_NAME = 'FunctionToString';\n\nconst SETUP_CLIENTS = new WeakMap();\n\nconst _functionToStringIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      originalFunctionToString = Function.prototype.toString;\n\n      // intrinsics (like Function.prototype) might be immutable in some environments\n      // e.g. Node with --frozen-intrinsics, XS (an embedded JavaScript engine) or SES (a JavaScript proposal)\n      try {\n        Function.prototype.toString = function ( ...args) {\n          const originalFunction = getOriginalFunction(this);\n          const context =\n            SETUP_CLIENTS.has(getClient() ) && originalFunction !== undefined ? originalFunction : this;\n          return originalFunctionToString.apply(context, args);\n        };\n      } catch {\n        // ignore errors here, just don't patch this\n      }\n    },\n    setup(client) {\n      SETUP_CLIENTS.set(client, true);\n    },\n  };\n}) ;\n\n/**\n * Patch toString calls to return proper name for wrapped functions.\n *\n * ```js\n * Sentry.init({\n *   integrations: [\n *     functionToStringIntegration(),\n *   ],\n * });\n * ```\n */\nconst functionToStringIntegration = defineIntegration(_functionToStringIntegration);\n\nexport { functionToStringIntegration };\n//# sourceMappingURL=functiontostring.js.map\n", "import { DEBUG_BUILD } from '../debug-build.js';\nimport { defineIntegration } from '../integration.js';\nimport { getPossibleEventMessages } from '../utils/eventUtils.js';\nimport { logger } from '../utils-hoist/logger.js';\nimport { getEventDescription } from '../utils-hoist/misc.js';\nimport { stringMatchesSomePattern } from '../utils-hoist/string.js';\n\n// \"Script error.\" is hard coded into browsers for errors that it can't read.\n// this is the result of a script being pulled in from an external domain and CORS.\nconst DEFAULT_IGNORE_ERRORS = [\n  /^Script error\\.?$/,\n  /^Javascript error: Script error\\.? on line 0$/,\n  /^ResizeObserver loop completed with undelivered notifications.$/, // The browser logs this when a ResizeObserver handler takes a bit longer. Usually this is not an actual issue though. It indicates slowness.\n  /^Cannot redefine property: googletag$/, // This is thrown when google tag manager is used in combination with an ad blocker\n  /^Can't find variable: gmo$/, // Error from Google Search App https://issuetracker.google.com/issues/396043331\n  /^undefined is not an object \\(evaluating 'a\\.[A-Z]'\\)$/, // Random error that happens but not actionable or noticeable to end-users.\n  'can\\'t redefine non-configurable property \"solana\"', // Probably a browser extension or custom browser (Brave) throwing this error\n  \"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)\", // Error thrown by GTM, seemingly not affecting end-users\n  \"Can't find variable: _AutofillCallbackHandler\", // Unactionable error in instagram webview https://developers.facebook.com/community/threads/320013549791141/\n  /^Non-Error promise rejection captured with value: Object Not Found Matching Id:\\d+, MethodName:simulateEvent, ParamCount:\\d+$/, // unactionable error from CEFSharp, a .NET library that embeds chromium in .NET apps\n  /^Java exception was raised during method invocation$/, // error from Facebook Mobile browser (https://github.com/getsentry/sentry-javascript/issues/15065)\n];\n\n/** Options for the EventFilters integration */\n\nconst INTEGRATION_NAME = 'EventFilters';\n\n/**\n * An integration that filters out events (errors and transactions) based on:\n *\n * - (Errors) A curated list of known low-value or irrelevant errors (see {@link DEFAULT_IGNORE_ERRORS})\n * - (Errors) A list of error messages or urls/filenames passed in via\n *   - Top level Sentry.init options (`ignoreErrors`, `denyUrls`, `allowUrls`)\n *   - The same options passed to the integration directly via @param options\n * - (Transactions/Spans) A list of root span (transaction) names passed in via\n *   - Top level Sentry.init option (`ignoreTransactions`)\n *   - The same option passed to the integration directly via @param options\n *\n * Events filtered by this integration will not be sent to Sentry.\n */\nconst eventFiltersIntegration = defineIntegration((options = {}) => {\n  let mergedOptions;\n  return {\n    name: INTEGRATION_NAME,\n    setup(client) {\n      const clientOptions = client.getOptions();\n      mergedOptions = _mergeOptions(options, clientOptions);\n    },\n    processEvent(event, _hint, client) {\n      if (!mergedOptions) {\n        const clientOptions = client.getOptions();\n        mergedOptions = _mergeOptions(options, clientOptions);\n      }\n      return _shouldDropEvent(event, mergedOptions) ? null : event;\n    },\n  };\n});\n\n/**\n * An integration that filters out events (errors and transactions) based on:\n *\n * - (Errors) A curated list of known low-value or irrelevant errors (see {@link DEFAULT_IGNORE_ERRORS})\n * - (Errors) A list of error messages or urls/filenames passed in via\n *   - Top level Sentry.init options (`ignoreErrors`, `denyUrls`, `allowUrls`)\n *   - The same options passed to the integration directly via @param options\n * - (Transactions/Spans) A list of root span (transaction) names passed in via\n *   - Top level Sentry.init option (`ignoreTransactions`)\n *   - The same option passed to the integration directly via @param options\n *\n * Events filtered by this integration will not be sent to Sentry.\n *\n * @deprecated this integration was renamed and will be removed in a future major version.\n * Use `eventFiltersIntegration` instead.\n */\nconst inboundFiltersIntegration = defineIntegration(((options = {}) => {\n  return {\n    ...eventFiltersIntegration(options),\n    name: 'InboundFilters',\n  };\n}) );\n\nfunction _mergeOptions(\n  internalOptions = {},\n  clientOptions = {},\n) {\n  return {\n    allowUrls: [...(internalOptions.allowUrls || []), ...(clientOptions.allowUrls || [])],\n    denyUrls: [...(internalOptions.denyUrls || []), ...(clientOptions.denyUrls || [])],\n    ignoreErrors: [\n      ...(internalOptions.ignoreErrors || []),\n      ...(clientOptions.ignoreErrors || []),\n      ...(internalOptions.disableErrorDefaults ? [] : DEFAULT_IGNORE_ERRORS),\n    ],\n    ignoreTransactions: [...(internalOptions.ignoreTransactions || []), ...(clientOptions.ignoreTransactions || [])],\n  };\n}\n\nfunction _shouldDropEvent(event, options) {\n  if (!event.type) {\n    // Filter errors\n    if (_isIgnoredError(event, options.ignoreErrors)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Event dropped due to being matched by \\`ignoreErrors\\` option.\\nEvent: ${getEventDescription(event)}`,\n        );\n      return true;\n    }\n    if (_isUselessError(event)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Event dropped due to not having an error message, error type or stacktrace.\\nEvent: ${getEventDescription(\n            event,\n          )}`,\n        );\n      return true;\n    }\n    if (_isDeniedUrl(event, options.denyUrls)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Event dropped due to being matched by \\`denyUrls\\` option.\\nEvent: ${getEventDescription(\n            event,\n          )}.\\nUrl: ${_getEventFilterUrl(event)}`,\n        );\n      return true;\n    }\n    if (!_isAllowedUrl(event, options.allowUrls)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Event dropped due to not being matched by \\`allowUrls\\` option.\\nEvent: ${getEventDescription(\n            event,\n          )}.\\nUrl: ${_getEventFilterUrl(event)}`,\n        );\n      return true;\n    }\n  } else if (event.type === 'transaction') {\n    // Filter transactions\n\n    if (_isIgnoredTransaction(event, options.ignoreTransactions)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Event dropped due to being matched by \\`ignoreTransactions\\` option.\\nEvent: ${getEventDescription(event)}`,\n        );\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction _isIgnoredError(event, ignoreErrors) {\n  if (!ignoreErrors?.length) {\n    return false;\n  }\n\n  return getPossibleEventMessages(event).some(message => stringMatchesSomePattern(message, ignoreErrors));\n}\n\nfunction _isIgnoredTransaction(event, ignoreTransactions) {\n  if (!ignoreTransactions?.length) {\n    return false;\n  }\n\n  const name = event.transaction;\n  return name ? stringMatchesSomePattern(name, ignoreTransactions) : false;\n}\n\nfunction _isDeniedUrl(event, denyUrls) {\n  if (!denyUrls?.length) {\n    return false;\n  }\n  const url = _getEventFilterUrl(event);\n  return !url ? false : stringMatchesSomePattern(url, denyUrls);\n}\n\nfunction _isAllowedUrl(event, allowUrls) {\n  if (!allowUrls?.length) {\n    return true;\n  }\n  const url = _getEventFilterUrl(event);\n  return !url ? true : stringMatchesSomePattern(url, allowUrls);\n}\n\nfunction _getLastValidUrl(frames = []) {\n  for (let i = frames.length - 1; i >= 0; i--) {\n    const frame = frames[i];\n\n    if (frame && frame.filename !== '<anonymous>' && frame.filename !== '[native code]') {\n      return frame.filename || null;\n    }\n  }\n\n  return null;\n}\n\nfunction _getEventFilterUrl(event) {\n  try {\n    // If there are linked exceptions or exception aggregates we only want to match against the top frame of the \"root\" (the main exception)\n    // The root always comes last in linked exceptions\n    const rootException = [...(event.exception?.values ?? [])]\n      .reverse()\n      .find(value => value.mechanism?.parent_id === undefined && value.stacktrace?.frames?.length);\n    const frames = rootException?.stacktrace?.frames;\n    return frames ? _getLastValidUrl(frames) : null;\n  } catch (oO) {\n    DEBUG_BUILD && logger.error(`Cannot extract url for event ${getEventDescription(event)}`);\n    return null;\n  }\n}\n\nfunction _isUselessError(event) {\n  // We only want to consider events for dropping that actually have recorded exception values.\n  if (!event.exception?.values?.length) {\n    return false;\n  }\n\n  return (\n    // No top-level message\n    !event.message &&\n    // There are no exception values that have a stacktrace, a non-generic-Error type or value\n    !event.exception.values.some(value => value.stacktrace || (value.type && value.type !== 'Error') || value.value)\n  );\n}\n\nexport { eventFiltersIntegration, inboundFiltersIntegration };\n//# sourceMappingURL=eventFilters.js.map\n", "import { isInstanceOf } from './is.js';\n\n/**\n * Creates exceptions inside `event.exception.values` for errors that are nested on properties based on the `key` parameter.\n */\nfunction applyAggregateErrorsToEvent(\n  exceptionFromErrorImplementation,\n  parser,\n  key,\n  limit,\n  event,\n  hint,\n) {\n  if (!event.exception?.values || !hint || !isInstanceOf(hint.originalException, Error)) {\n    return;\n  }\n\n  // Generally speaking the last item in `event.exception.values` is the exception originating from the original Error\n  const originalException =\n    event.exception.values.length > 0 ? event.exception.values[event.exception.values.length - 1] : undefined;\n\n  // We only create exception grouping if there is an exception in the event.\n  if (originalException) {\n    event.exception.values = aggregateExceptionsFromError(\n      exceptionFromErrorImplementation,\n      parser,\n      limit,\n      hint.originalException ,\n      key,\n      event.exception.values,\n      originalException,\n      0,\n    );\n  }\n}\n\nfunction aggregateExceptionsFromError(\n  exceptionFromErrorImplementation,\n  parser,\n  limit,\n  error,\n  key,\n  prevExceptions,\n  exception,\n  exceptionId,\n) {\n  if (prevExceptions.length >= limit + 1) {\n    return prevExceptions;\n  }\n\n  let newExceptions = [...prevExceptions];\n\n  // Recursively call this function in order to walk down a chain of errors\n  if (isInstanceOf(error[key], Error)) {\n    applyExceptionGroupFieldsForParentException(exception, exceptionId);\n    const newException = exceptionFromErrorImplementation(parser, error[key]);\n    const newExceptionId = newExceptions.length;\n    applyExceptionGroupFieldsForChildException(newException, key, newExceptionId, exceptionId);\n    newExceptions = aggregateExceptionsFromError(\n      exceptionFromErrorImplementation,\n      parser,\n      limit,\n      error[key],\n      key,\n      [newException, ...newExceptions],\n      newException,\n      newExceptionId,\n    );\n  }\n\n  // This will create exception grouping for AggregateErrors\n  // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/AggregateError\n  if (Array.isArray(error.errors)) {\n    error.errors.forEach((childError, i) => {\n      if (isInstanceOf(childError, Error)) {\n        applyExceptionGroupFieldsForParentException(exception, exceptionId);\n        const newException = exceptionFromErrorImplementation(parser, childError);\n        const newExceptionId = newExceptions.length;\n        applyExceptionGroupFieldsForChildException(newException, `errors[${i}]`, newExceptionId, exceptionId);\n        newExceptions = aggregateExceptionsFromError(\n          exceptionFromErrorImplementation,\n          parser,\n          limit,\n          childError,\n          key,\n          [newException, ...newExceptions],\n          newException,\n          newExceptionId,\n        );\n      }\n    });\n  }\n\n  return newExceptions;\n}\n\nfunction applyExceptionGroupFieldsForParentException(exception, exceptionId) {\n  // Don't know if this default makes sense. The protocol requires us to set these values so we pick *some* default.\n  exception.mechanism = exception.mechanism || { type: 'generic', handled: true };\n\n  exception.mechanism = {\n    ...exception.mechanism,\n    ...(exception.type === 'AggregateError' && { is_exception_group: true }),\n    exception_id: exceptionId,\n  };\n}\n\nfunction applyExceptionGroupFieldsForChildException(\n  exception,\n  source,\n  exceptionId,\n  parentId,\n) {\n  // Don't know if this default makes sense. The protocol requires us to set these values so we pick *some* default.\n  exception.mechanism = exception.mechanism || { type: 'generic', handled: true };\n\n  exception.mechanism = {\n    ...exception.mechanism,\n    type: 'chained',\n    source,\n    exception_id: exceptionId,\n    parent_id: parentId,\n  };\n}\n\nexport { applyAggregateErrorsToEvent };\n//# sourceMappingURL=aggregate-errors.js.map\n", "import { CONSOLE_LEVELS, originalConsoleMethods } from '../logger.js';\nimport { fill } from '../object.js';\nimport { GLOBAL_OBJ } from '../worldwide.js';\nimport { addHandler, maybeInstrument, triggerHandlers } from './handlers.js';\n\n/**\n * Add an instrumentation handler for when a console.xxx method is called.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nfunction addConsoleInstrumentationHandler(handler) {\n  const type = 'console';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentConsole);\n}\n\nfunction instrumentConsole() {\n  if (!('console' in GLOBAL_OBJ)) {\n    return;\n  }\n\n  CONSOLE_LEVELS.forEach(function (level) {\n    if (!(level in GLOBAL_OBJ.console)) {\n      return;\n    }\n\n    fill(GLOBAL_OBJ.console, level, function (originalConsoleMethod) {\n      originalConsoleMethods[level] = originalConsoleMethod;\n\n      return function (...args) {\n        const handlerData = { args, level };\n        triggerHandlers('console', handlerData);\n\n        const log = originalConsoleMethods[level];\n        log?.apply(GLOBAL_OBJ.console, args);\n      };\n    });\n  });\n}\n\nexport { addConsoleInstrumentationHandler };\n//# sourceMappingURL=console.js.map\n", "/**\n * Converts a string-based level into a `SeverityLevel`, normalizing it along the way.\n *\n * @param level String representation of desired `SeverityLevel`.\n * @returns The `SeverityLevel` corresponding to the given string, or 'log' if the string isn't a valid level.\n */\nfunction severityLevelFromString(level) {\n  return (\n    level === 'warn' ? 'warning' : ['fatal', 'error', 'warning', 'log', 'info', 'debug'].includes(level) ? level : 'log'\n  ) ;\n}\n\nexport { severityLevelFromString };\n//# sourceMappingURL=severity.js.map\n", "import { DEBUG_BUILD } from '../debug-build.js';\nimport { defineIntegration } from '../integration.js';\nimport { logger } from '../utils-hoist/logger.js';\nimport { getFramesFromEvent } from '../utils-hoist/stacktrace.js';\n\nconst INTEGRATION_NAME = 'Dedupe';\n\nconst _dedupeIntegration = (() => {\n  let previousEvent;\n\n  return {\n    name: INTEGRATION_NAME,\n    processEvent(currentEvent) {\n      // We want to ignore any non-error type events, e.g. transactions or replays\n      // These should never be deduped, and also not be compared against as _previousEvent.\n      if (currentEvent.type) {\n        return currentEvent;\n      }\n\n      // Juuust in case something goes wrong\n      try {\n        if (_shouldDropEvent(currentEvent, previousEvent)) {\n          DEBUG_BUILD && logger.warn('Event dropped due to being a duplicate of previously captured event.');\n          return null;\n        }\n      } catch (_oO) {} // eslint-disable-line no-empty\n\n      return (previousEvent = currentEvent);\n    },\n  };\n}) ;\n\n/**\n * Deduplication filter.\n */\nconst dedupeIntegration = defineIntegration(_dedupeIntegration);\n\n/** only exported for tests. */\nfunction _shouldDropEvent(currentEvent, previousEvent) {\n  if (!previousEvent) {\n    return false;\n  }\n\n  if (_isSameMessageEvent(currentEvent, previousEvent)) {\n    return true;\n  }\n\n  if (_isSameExceptionEvent(currentEvent, previousEvent)) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction _isSameMessageEvent(currentEvent, previousEvent) {\n  const currentMessage = currentEvent.message;\n  const previousMessage = previousEvent.message;\n\n  // If neither event has a message property, they were both exceptions, so bail out\n  if (!currentMessage && !previousMessage) {\n    return false;\n  }\n\n  // If only one event has a stacktrace, but not the other one, they are not the same\n  if ((currentMessage && !previousMessage) || (!currentMessage && previousMessage)) {\n    return false;\n  }\n\n  if (currentMessage !== previousMessage) {\n    return false;\n  }\n\n  if (!_isSameFingerprint(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  if (!_isSameStacktrace(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction _isSameExceptionEvent(currentEvent, previousEvent) {\n  const previousException = _getExceptionFromEvent(previousEvent);\n  const currentException = _getExceptionFromEvent(currentEvent);\n\n  if (!previousException || !currentException) {\n    return false;\n  }\n\n  if (previousException.type !== currentException.type || previousException.value !== currentException.value) {\n    return false;\n  }\n\n  if (!_isSameFingerprint(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  if (!_isSameStacktrace(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction _isSameStacktrace(currentEvent, previousEvent) {\n  let currentFrames = getFramesFromEvent(currentEvent);\n  let previousFrames = getFramesFromEvent(previousEvent);\n\n  // If neither event has a stacktrace, they are assumed to be the same\n  if (!currentFrames && !previousFrames) {\n    return true;\n  }\n\n  // If only one event has a stacktrace, but not the other one, they are not the same\n  if ((currentFrames && !previousFrames) || (!currentFrames && previousFrames)) {\n    return false;\n  }\n\n  currentFrames = currentFrames ;\n  previousFrames = previousFrames ;\n\n  // If number of frames differ, they are not the same\n  if (previousFrames.length !== currentFrames.length) {\n    return false;\n  }\n\n  // Otherwise, compare the two\n  for (let i = 0; i < previousFrames.length; i++) {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const frameA = previousFrames[i];\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const frameB = currentFrames[i];\n\n    if (\n      frameA.filename !== frameB.filename ||\n      frameA.lineno !== frameB.lineno ||\n      frameA.colno !== frameB.colno ||\n      frameA.function !== frameB.function\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction _isSameFingerprint(currentEvent, previousEvent) {\n  let currentFingerprint = currentEvent.fingerprint;\n  let previousFingerprint = previousEvent.fingerprint;\n\n  // If neither event has a fingerprint, they are assumed to be the same\n  if (!currentFingerprint && !previousFingerprint) {\n    return true;\n  }\n\n  // If only one event has a fingerprint, but not the other one, they are not the same\n  if ((currentFingerprint && !previousFingerprint) || (!currentFingerprint && previousFingerprint)) {\n    return false;\n  }\n\n  currentFingerprint = currentFingerprint ;\n  previousFingerprint = previousFingerprint ;\n\n  // Otherwise, compare the two\n  try {\n    return !!(currentFingerprint.join('') === previousFingerprint.join(''));\n  } catch (_oO) {\n    return false;\n  }\n}\n\nfunction _getExceptionFromEvent(event) {\n  return event.exception?.values && event.exception.values[0];\n}\n\nexport { _shouldDropEvent, dedupeIntegration };\n//# sourceMappingURL=dedupe.js.map\n", "import { SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD, SEMANTIC_ATTRIBUTE_URL_FULL, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN } from '../semanticAttributes.js';\n\n// Curious about `thismessage:/`? See: https://www.rfc-editor.org/rfc/rfc2557.html\n//  > When the methods above do not yield an absolute URI, a base URL\n//  > of \"thismessage:/\" MUST be employed. This base URL has been\n//  > defined for the sole purpose of resolving relative references\n//  > within a multipart/related structure when no other base URI is\n//  > specified.\n//\n// We need to provide a base URL to `parseStringToURLObject` because the fetch API gives us a\n// relative URL sometimes.\n//\n// This is the only case where we need to provide a base URL to `parseStringToURLObject`\n// because the relative URL is not valid on its own.\nconst DEFAULT_BASE_URL = 'thismessage:/';\n\n/**\n * Checks if the URL object is relative\n *\n * @param url - The URL object to check\n * @returns True if the URL object is relative, false otherwise\n */\nfunction isURLObjectRelative(url) {\n  return 'isRelative' in url;\n}\n\n/**\n * Parses string to a URL object\n *\n * @param url - The URL to parse\n * @returns The parsed URL object or undefined if the URL is invalid\n */\nfunction parseStringToURLObject(url, urlBase) {\n  const isRelative = url.indexOf('://') <= 0 && url.indexOf('//') !== 0;\n  const base = urlBase ?? (isRelative ? DEFAULT_BASE_URL : undefined);\n  try {\n    // Use `canParse` to short-circuit the URL constructor if it's not a valid URL\n    // This is faster than trying to construct the URL and catching the error\n    // Node 20+, Chrome 120+, Firefox 115+, Safari 17+\n    if ('canParse' in URL && !(URL ).canParse(url, base)) {\n      return undefined;\n    }\n\n    const fullUrlObject = new URL(url, base);\n    if (isRelative) {\n      // Because we used a fake base URL, we need to return a relative URL object.\n      // We cannot return anything about the origin, host, etc. because it will refer to the fake base URL.\n      return {\n        isRelative,\n        pathname: fullUrlObject.pathname,\n        search: fullUrlObject.search,\n        hash: fullUrlObject.hash,\n      };\n    }\n    return fullUrlObject;\n  } catch {\n    // empty body\n  }\n\n  return undefined;\n}\n\n/**\n * Takes a URL object and returns a sanitized string which is safe to use as span name\n * see: https://develop.sentry.dev/sdk/data-handling/#structuring-data\n */\nfunction getSanitizedUrlStringFromUrlObject(url) {\n  if (isURLObjectRelative(url)) {\n    return url.pathname;\n  }\n\n  const newUrl = new URL(url);\n  newUrl.search = '';\n  newUrl.hash = '';\n  if (['80', '443'].includes(newUrl.port)) {\n    newUrl.port = '';\n  }\n  if (newUrl.password) {\n    newUrl.password = '%filtered%';\n  }\n  if (newUrl.username) {\n    newUrl.username = '%filtered%';\n  }\n\n  return newUrl.toString();\n}\n\nfunction getHttpSpanNameFromUrlObject(\n  urlObject,\n  kind,\n  request,\n  routeName,\n) {\n  const method = request?.method?.toUpperCase() ?? 'GET';\n  const route = routeName\n    ? routeName\n    : urlObject\n      ? kind === 'client'\n        ? getSanitizedUrlStringFromUrlObject(urlObject)\n        : urlObject.pathname\n      : '/';\n\n  return `${method} ${route}`;\n}\n\n/**\n * Takes a parsed URL object and returns a set of attributes for the span\n * that represents the HTTP request for that url. This is used for both server\n * and client http spans.\n *\n * Follows https://opentelemetry.io/docs/specs/semconv/http/.\n *\n * @param urlObject - see {@link parseStringToURLObject}\n * @param kind - The type of HTTP operation (server or client)\n * @param spanOrigin - The origin of the span\n * @param request - The request object, see {@link PartialRequest}\n * @param routeName - The name of the route, must be low cardinality\n * @returns The span name and attributes for the HTTP operation\n */\nfunction getHttpSpanDetailsFromUrlObject(\n  urlObject,\n  kind,\n  spanOrigin,\n  request,\n  routeName,\n) {\n  const attributes = {\n    [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: spanOrigin,\n    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n  };\n\n  if (routeName) {\n    // This is based on https://opentelemetry.io/docs/specs/semconv/http/http-spans/#name\n    attributes[kind === 'server' ? 'http.route' : 'url.template'] = routeName;\n    attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] = 'route';\n  }\n\n  if (request?.method) {\n    attributes[SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD] = request.method.toUpperCase();\n  }\n\n  if (urlObject) {\n    if (urlObject.search) {\n      attributes['url.query'] = urlObject.search;\n    }\n    if (urlObject.hash) {\n      attributes['url.fragment'] = urlObject.hash;\n    }\n    if (urlObject.pathname) {\n      attributes['url.path'] = urlObject.pathname;\n      if (urlObject.pathname === '/') {\n        attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE] = 'route';\n      }\n    }\n\n    if (!isURLObjectRelative(urlObject)) {\n      attributes[SEMANTIC_ATTRIBUTE_URL_FULL] = urlObject.href;\n      if (urlObject.port) {\n        attributes['url.port'] = urlObject.port;\n      }\n      if (urlObject.protocol) {\n        attributes['url.scheme'] = urlObject.protocol;\n      }\n      if (urlObject.hostname) {\n        attributes[kind === 'server' ? 'server.address' : 'url.domain'] = urlObject.hostname;\n      }\n    }\n  }\n\n  return [getHttpSpanNameFromUrlObject(urlObject, kind, request, routeName), attributes];\n}\n\n/**\n * Parses string form of URL into an object\n * // borrowed from https://tools.ietf.org/html/rfc3986#appendix-B\n * // intentionally using regex and not <a/> href parsing trick because React Native and other\n * // environments where DOM might not be available\n * @returns parsed URL object\n */\nfunction parseUrl(url) {\n  if (!url) {\n    return {};\n  }\n\n  const match = url.match(/^(([^:/?#]+):)?(\\/\\/([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?$/);\n\n  if (!match) {\n    return {};\n  }\n\n  // coerce to undefined values to empty string so we don't get 'undefined'\n  const query = match[6] || '';\n  const fragment = match[8] || '';\n  return {\n    host: match[4],\n    path: match[5],\n    protocol: match[2],\n    search: query,\n    hash: fragment,\n    relative: match[5] + query + fragment, // everything minus origin\n  };\n}\n\n/**\n * Strip the query string and fragment off of a given URL or path (if present)\n *\n * @param urlPath Full URL or path, including possible query string and/or fragment\n * @returns URL or path without query string or fragment\n */\nfunction stripUrlQueryAndFragment(urlPath) {\n  return (urlPath.split(/[?#]/, 1) )[0];\n}\n\n/**\n * Takes a URL object and returns a sanitized string which is safe to use as span name\n * see: https://develop.sentry.dev/sdk/data-handling/#structuring-data\n */\nfunction getSanitizedUrlString(url) {\n  const { protocol, host, path } = url;\n\n  const filteredHost =\n    host\n      // Always filter out authority\n      ?.replace(/^.*@/, '[filtered]:[filtered]@')\n      // Don't show standard :80 (http) and :443 (https) ports to reduce the noise\n      // TODO: Use new URL global if it exists\n      .replace(/(:80)$/, '')\n      .replace(/(:443)$/, '') || '';\n\n  return `${protocol ? `${protocol}://` : ''}${filteredHost}${path}`;\n}\n\nexport { getHttpSpanDetailsFromUrlObject, getSanitizedUrlString, getSanitizedUrlStringFromUrlObject, isURLObjectRelative, parseStringToURLObject, parseUrl, stripUrlQueryAndFragment };\n//# sourceMappingURL=url.js.map\n", "/**\n * Determine a breadcrumb's log level (only `warning` or `error`) based on an HTTP status code.\n */\nfunction getBreadcrumbLogLevelFromHttpStatusCode(statusCode) {\n  // NOTE: undefined defaults to 'info' in Sentry\n  if (statusCode === undefined) {\n    return undefined;\n  } else if (statusCode >= 400 && statusCode < 500) {\n    return 'warning';\n  } else if (statusCode >= 500) {\n    return 'error';\n  } else {\n    return undefined;\n  }\n}\n\nexport { getBreadcrumbLogLevelFromHttpStatusCode };\n//# sourceMappingURL=breadcrumb-log-level.js.map\n", "import { DEBU<PERSON>_BUILD } from '../debug-build.js';\nimport { logger } from './logger.js';\nimport { GLOBAL_OBJ } from './worldwide.js';\n\nconst WINDOW = GLOBAL_OBJ ;\n\n/**\n * Tells whether current environment supports ErrorEvent objects\n * {@link supportsErrorEvent}.\n *\n * @returns Answer to the given question.\n */\nfunction supportsErrorEvent() {\n  try {\n    new ErrorEvent('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports DOMError objects\n * {@link supportsDOMError}.\n *\n * @returns Answer to the given question.\n */\nfunction supportsDOMError() {\n  try {\n    // Chrome: VM89:1 Uncaught TypeError: Failed to construct 'DOMError':\n    // 1 argument required, but only 0 present.\n    // @ts-expect-error It really needs 1 argument, not 0.\n    new DOMError('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports DOMException objects\n * {@link supportsDOMException}.\n *\n * @returns Answer to the given question.\n */\nfunction supportsDOMException() {\n  try {\n    new DOMException('');\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * Tells whether current environment supports History API\n * {@link supportsHistory}.\n *\n * @returns Answer to the given question.\n */\nfunction supportsHistory() {\n  return 'history' in WINDOW && !!WINDOW.history;\n}\n\n/**\n * Tells whether current environment supports Fetch API\n * {@link supportsFetch}.\n *\n * @returns Answer to the given question.\n * @deprecated This is no longer used and will be removed in a future major version.\n */\nconst supportsFetch = _isFetchSupported;\n\nfunction _isFetchSupported() {\n  if (!('fetch' in WINDOW)) {\n    return false;\n  }\n\n  try {\n    new Headers();\n    new Request('http://www.example.com');\n    new Response();\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\n/**\n * isNative checks if the given function is a native implementation\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction isNativeFunction(func) {\n  return func && /^function\\s+\\w+\\(\\)\\s+\\{\\s+\\[native code\\]\\s+\\}$/.test(func.toString());\n}\n\n/**\n * Tells whether current environment supports Fetch API natively\n * {@link supportsNativeFetch}.\n *\n * @returns true if `window.fetch` is natively implemented, false otherwise\n */\nfunction supportsNativeFetch() {\n  if (typeof EdgeRuntime === 'string') {\n    return true;\n  }\n\n  if (!_isFetchSupported()) {\n    return false;\n  }\n\n  // Fast path to avoid DOM I/O\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  if (isNativeFunction(WINDOW.fetch)) {\n    return true;\n  }\n\n  // window.fetch is implemented, but is polyfilled or already wrapped (e.g: by a chrome extension)\n  // so create a \"pure\" iframe to see if that has native fetch\n  let result = false;\n  const doc = WINDOW.document;\n  // eslint-disable-next-line deprecation/deprecation\n  if (doc && typeof (doc.createElement ) === 'function') {\n    try {\n      const sandbox = doc.createElement('iframe');\n      sandbox.hidden = true;\n      doc.head.appendChild(sandbox);\n      if (sandbox.contentWindow?.fetch) {\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        result = isNativeFunction(sandbox.contentWindow.fetch);\n      }\n      doc.head.removeChild(sandbox);\n    } catch (err) {\n      DEBUG_BUILD &&\n        logger.warn('Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ', err);\n    }\n  }\n\n  return result;\n}\n\n/**\n * Tells whether current environment supports ReportingObserver API\n * {@link supportsReportingObserver}.\n *\n * @returns Answer to the given question.\n */\nfunction supportsReportingObserver() {\n  return 'ReportingObserver' in WINDOW;\n}\n\n/**\n * Tells whether current environment supports Referrer Policy API\n * {@link supportsReferrerPolicy}.\n *\n * @returns Answer to the given question.\n * @deprecated This is no longer used and will be removed in a future major version.\n */\nfunction supportsReferrerPolicy() {\n  // Despite all stars in the sky saying that Edge supports old draft syntax, aka 'never', 'always', 'origin' and 'default'\n  // (see https://caniuse.com/#feat=referrer-policy),\n  // it doesn't. And it throws an exception instead of ignoring this parameter...\n  // REF: https://github.com/getsentry/raven-js/issues/1233\n\n  if (!_isFetchSupported()) {\n    return false;\n  }\n\n  try {\n    new Request('_', {\n      referrerPolicy: 'origin' ,\n    });\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport { isNativeFunction, supportsDOMError, supportsDOMException, supportsErrorEvent, supportsFetch, supportsHistory, supportsNativeFetch, supportsReferrerPolicy, supportsReportingObserver };\n//# sourceMappingURL=supports.js.map\n", "import { isError, isRequest } from '../is.js';\nimport { fill, addNonEnumerableProperty } from '../object.js';\nimport { supportsNativeFetch } from '../supports.js';\nimport { timestampInSeconds } from '../time.js';\nimport { GLOBAL_OBJ } from '../worldwide.js';\nimport { addHandler, maybeInstrument, triggerHandlers } from './handlers.js';\n\n/**\n * Add an instrumentation handler for when a fetch request happens.\n * The handler function is called once when the request starts and once when it ends,\n * which can be identified by checking if it has an `endTimestamp`.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nfunction addFetchInstrumentationHandler(\n  handler,\n  skipNativeFetchCheck,\n) {\n  const type = 'fetch';\n  addHandler(type, handler);\n  maybeInstrument(type, () => instrumentFetch(undefined, skipNativeFetchCheck));\n}\n\n/**\n * Add an instrumentation handler for long-lived fetch requests, like consuming server-sent events (SSE) via fetch.\n * The handler will resolve the request body and emit the actual `endTimestamp`, so that the\n * span can be updated accordingly.\n *\n * Only used internally\n * @hidden\n */\nfunction addFetchEndInstrumentationHandler(handler) {\n  const type = 'fetch-body-resolved';\n  addHandler(type, handler);\n  maybeInstrument(type, () => instrumentFetch(streamHandler));\n}\n\nfunction instrumentFetch(onFetchResolved, skipNativeFetchCheck = false) {\n  if (skipNativeFetchCheck && !supportsNativeFetch()) {\n    return;\n  }\n\n  fill(GLOBAL_OBJ, 'fetch', function (originalFetch) {\n    return function (...args) {\n      // We capture the error right here and not in the Promise error callback because Safari (and probably other\n      // browsers too) will wipe the stack trace up to this point, only leaving us with this file which is useless.\n\n      // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n      //       it means the error, that was caused by your fetch call did not\n      //       have a stack trace, so the SDK backfilled the stack trace so\n      //       you can see which fetch call failed.\n      const virtualError = new Error();\n\n      const { method, url } = parseFetchArgs(args);\n      const handlerData = {\n        args,\n        fetchData: {\n          method,\n          url,\n        },\n        startTimestamp: timestampInSeconds() * 1000,\n        // // Adding the error to be able to fingerprint the failed fetch event in HttpClient instrumentation\n        virtualError,\n        headers: getHeadersFromFetchArgs(args),\n      };\n\n      // if there is no callback, fetch is instrumented directly\n      if (!onFetchResolved) {\n        triggerHandlers('fetch', {\n          ...handlerData,\n        });\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      return originalFetch.apply(GLOBAL_OBJ, args).then(\n        async (response) => {\n          if (onFetchResolved) {\n            onFetchResolved(response);\n          } else {\n            triggerHandlers('fetch', {\n              ...handlerData,\n              endTimestamp: timestampInSeconds() * 1000,\n              response,\n            });\n          }\n\n          return response;\n        },\n        (error) => {\n          triggerHandlers('fetch', {\n            ...handlerData,\n            endTimestamp: timestampInSeconds() * 1000,\n            error,\n          });\n\n          if (isError(error) && error.stack === undefined) {\n            // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n            //       it means the error, that was caused by your fetch call did not\n            //       have a stack trace, so the SDK backfilled the stack trace so\n            //       you can see which fetch call failed.\n            error.stack = virtualError.stack;\n            addNonEnumerableProperty(error, 'framesToPop', 1);\n          }\n\n          // We enhance the not-so-helpful \"Failed to fetch\" error messages with the host\n          // Possible messages we handle here:\n          // * \"Failed to fetch\" (chromium)\n          // * \"Load failed\" (webkit)\n          // * \"NetworkError when attempting to fetch resource.\" (firefox)\n          if (\n            error instanceof TypeError &&\n            (error.message === 'Failed to fetch' ||\n              error.message === 'Load failed' ||\n              error.message === 'NetworkError when attempting to fetch resource.')\n          ) {\n            try {\n              const url = new URL(handlerData.fetchData.url);\n              error.message = `${error.message} (${url.host})`;\n            } catch {\n              // ignore it if errors happen here\n            }\n          }\n\n          // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n          //       it means the sentry.javascript SDK caught an error invoking your application code.\n          //       This is expected behavior and NOT indicative of a bug with sentry.javascript.\n          throw error;\n        },\n      );\n    };\n  });\n}\n\nasync function resolveResponse(res, onFinishedResolving) {\n  if (res?.body) {\n    const body = res.body;\n    const responseReader = body.getReader();\n\n    // Define a maximum duration after which we just cancel\n    const maxFetchDurationTimeout = setTimeout(\n      () => {\n        body.cancel().then(null, () => {\n          // noop\n        });\n      },\n      90 * 1000, // 90s\n    );\n\n    let readingActive = true;\n    while (readingActive) {\n      let chunkTimeout;\n      try {\n        // abort reading if read op takes more than 5s\n        chunkTimeout = setTimeout(() => {\n          body.cancel().then(null, () => {\n            // noop on error\n          });\n        }, 5000);\n\n        // This .read() call will reject/throw when we abort due to timeouts through `body.cancel()`\n        const { done } = await responseReader.read();\n\n        clearTimeout(chunkTimeout);\n\n        if (done) {\n          onFinishedResolving();\n          readingActive = false;\n        }\n      } catch (error) {\n        readingActive = false;\n      } finally {\n        clearTimeout(chunkTimeout);\n      }\n    }\n\n    clearTimeout(maxFetchDurationTimeout);\n\n    responseReader.releaseLock();\n    body.cancel().then(null, () => {\n      // noop on error\n    });\n  }\n}\n\nfunction streamHandler(response) {\n  // clone response for awaiting stream\n  let clonedResponseForResolving;\n  try {\n    clonedResponseForResolving = response.clone();\n  } catch {\n    return;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  resolveResponse(clonedResponseForResolving, () => {\n    triggerHandlers('fetch-body-resolved', {\n      endTimestamp: timestampInSeconds() * 1000,\n      response,\n    });\n  });\n}\n\nfunction hasProp(obj, prop) {\n  return !!obj && typeof obj === 'object' && !!(obj )[prop];\n}\n\nfunction getUrlFromResource(resource) {\n  if (typeof resource === 'string') {\n    return resource;\n  }\n\n  if (!resource) {\n    return '';\n  }\n\n  if (hasProp(resource, 'url')) {\n    return resource.url;\n  }\n\n  if (resource.toString) {\n    return resource.toString();\n  }\n\n  return '';\n}\n\n/**\n * Parses the fetch arguments to find the used Http method and the url of the request.\n * Exported for tests only.\n */\nfunction parseFetchArgs(fetchArgs) {\n  if (fetchArgs.length === 0) {\n    return { method: 'GET', url: '' };\n  }\n\n  if (fetchArgs.length === 2) {\n    const [url, options] = fetchArgs ;\n\n    return {\n      url: getUrlFromResource(url),\n      method: hasProp(options, 'method') ? String(options.method).toUpperCase() : 'GET',\n    };\n  }\n\n  const arg = fetchArgs[0];\n  return {\n    url: getUrlFromResource(arg ),\n    method: hasProp(arg, 'method') ? String(arg.method).toUpperCase() : 'GET',\n  };\n}\n\nfunction getHeadersFromFetchArgs(fetchArgs) {\n  const [requestArgument, optionsArgument] = fetchArgs;\n\n  try {\n    if (\n      typeof optionsArgument === 'object' &&\n      optionsArgument !== null &&\n      'headers' in optionsArgument &&\n      optionsArgument.headers\n    ) {\n      return new Headers(optionsArgument.headers );\n    }\n\n    if (isRequest(requestArgument)) {\n      return new Headers(requestArgument.headers);\n    }\n  } catch {\n    // noop\n  }\n\n  return;\n}\n\nexport { addFetchEndInstrumentationHandler, addFetchInstrumentationHandler, parseFetchArgs };\n//# sourceMappingURL=fetch.js.map\n", "/*\n * This module exists for optimizations in the build process through rollup and terser.  We define some global\n * constants, which can be overridden during build. By guarding certain pieces of code with functions that return these\n * constants, we can control whether or not they appear in the final bundle. (Any code guarded by a false condition will\n * never run, and will hence be dropped during treeshaking.) The two primary uses for this are stripping out calls to\n * `logger` and preventing node-related code from appearing in browser bundles.\n *\n * Attention:\n * This file should not be used to define constants/flags that are intended to be used for tree-shaking conducted by\n * users. These flags should live in their respective packages, as we identified user tooling (specifically webpack)\n * having issues tree-shaking these constants across package boundaries.\n * An example for this is the __SENTRY_DEBUG__ constant. It is declared in each package individually because we want\n * users to be able to shake away expressions that it guards.\n */\n\n/**\n * Figures out if we're building a browser bundle.\n *\n * @returns true if this is a browser bundle build.\n */\nfunction isBrowserBundle() {\n  return typeof __SENTRY_BROWSER_BUNDLE__ !== 'undefined' && !!__SENTRY_BROWSER_BUNDLE__;\n}\n\n/**\n * Get source of SDK.\n */\nfunction getSDKSource() {\n  // This comment is used to identify this line in the CDN bundle build step and replace this with \"return 'cdn';\"\n  /* __SENTRY_SDK_SOURCE__ */ return 'npm';\n}\n\nexport { getSDKSource, isBrowserBundle };\n//# sourceMappingURL=env.js.map\n", "import { GLO<PERSON>L_OBJ, getOriginalFunction, markFunctionWrapped, addNonEnumerableProperty, withScope, addExceptionTypeValue, addExceptionMechanism, captureException, getLocationHref } from '@sentry/core';\n\nconst WINDOW = GLOBAL_OBJ ;\n\nlet ignoreOnError = 0;\n\n/**\n * @hidden\n */\nfunction shouldIgnoreOnError() {\n  return ignoreOnError > 0;\n}\n\n/**\n * @hidden\n */\nfunction ignoreNextOnError() {\n  // onerror should trigger before setTimeout\n  ignoreOnError++;\n  setTimeout(() => {\n    ignoreOnError--;\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\n\n/**\n * Instruments the given function and sends an event to Sentry every time the\n * function throws an exception.\n *\n * @param fn A function to wrap. It is generally safe to pass an unbound function, because the returned wrapper always\n * has a correct `this` context.\n * @returns The wrapped function.\n * @hidden\n */\nfunction wrap(\n  fn,\n  options\n\n = {},\n) {\n  // for future readers what this does is wrap a function and then create\n  // a bi-directional wrapping between them.\n  //\n  // example: wrapped = wrap(original);\n  //  original.__sentry_wrapped__ -> wrapped\n  //  wrapped.__sentry_original__ -> original\n\n  function isFunction(fn) {\n    return typeof fn === 'function';\n  }\n\n  if (!isFunction(fn)) {\n    return fn;\n  }\n\n  try {\n    // if we're dealing with a function that was previously wrapped, return\n    // the original wrapper.\n    const wrapper = (fn ).__sentry_wrapped__;\n    if (wrapper) {\n      if (typeof wrapper === 'function') {\n        return wrapper;\n      } else {\n        // If we find that the `__sentry_wrapped__` function is not a function at the time of accessing it, it means\n        // that something messed with it. In that case we want to return the originally passed function.\n        return fn;\n      }\n    }\n\n    // We don't wanna wrap it twice\n    if (getOriginalFunction(fn)) {\n      return fn;\n    }\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    // Bail on wrapping and return the function as-is (defers to window.onerror).\n    return fn;\n  }\n\n  // Wrap the function itself\n  // It is important that `sentryWrapped` is not an arrow function to preserve the context of `this`\n  const sentryWrapped = function ( ...args) {\n    try {\n      // Also wrap arguments that are themselves functions\n      const wrappedArguments = args.map(arg => wrap(arg, options));\n\n      // Attempt to invoke user-land function\n      // NOTE: If you are a Sentry user, and you are seeing this stack frame, it\n      //       means the sentry.javascript SDK caught an error invoking your application code. This\n      //       is expected behavior and NOT indicative of a bug with sentry.javascript.\n      return fn.apply(this, wrappedArguments);\n    } catch (ex) {\n      ignoreNextOnError();\n\n      withScope(scope => {\n        scope.addEventProcessor(event => {\n          if (options.mechanism) {\n            addExceptionTypeValue(event, undefined, undefined);\n            addExceptionMechanism(event, options.mechanism);\n          }\n\n          event.extra = {\n            ...event.extra,\n            arguments: args,\n          };\n\n          return event;\n        });\n\n        captureException(ex);\n      });\n\n      throw ex;\n    }\n  } ;\n\n  // Wrap the wrapped function in a proxy, to ensure any other properties of the original function remain available\n  try {\n    for (const property in fn) {\n      if (Object.prototype.hasOwnProperty.call(fn, property)) {\n        sentryWrapped[property ] = fn[property ];\n      }\n    }\n  } catch {\n    // Accessing some objects may throw\n    // ref: https://github.com/getsentry/sentry-javascript/issues/1168\n  }\n\n  // Signal that this function has been wrapped/filled already\n  // for both debugging and to prevent it to being wrapped/filled twice\n  markFunctionWrapped(sentryWrapped, fn);\n\n  addNonEnumerableProperty(fn, '__sentry_wrapped__', sentryWrapped);\n\n  // Restore original function name (not all browsers allow that)\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const descriptor = Object.getOwnPropertyDescriptor(sentryWrapped, 'name');\n    if (descriptor.configurable) {\n      Object.defineProperty(sentryWrapped, 'name', {\n        get() {\n          return fn.name;\n        },\n      });\n    }\n  } catch {\n    // This may throw if e.g. the descriptor does not exist, or a browser does not allow redefining `name`.\n    // to save some bytes we simply try-catch this\n  }\n\n  return sentryWrapped;\n}\n\n/**\n * Get HTTP request data from the current page.\n */\nfunction getHttpRequestData() {\n  // grab as much info as exists and add it to the event\n  const url = getLocationHref();\n  const { referrer } = WINDOW.document || {};\n  const { userAgent } = WINDOW.navigator || {};\n\n  const headers = {\n    ...(referrer && { Referer: referrer }),\n    ...(userAgent && { 'User-Agent': userAgent }),\n  };\n  const request = {\n    url,\n    headers,\n  };\n\n  return request;\n}\n\nexport { WINDOW, getHttpRequestData, ignoreNextOnError, shouldIgnoreOnError, wrap };\n//# sourceMappingURL=helpers.js.map\n", "import { isError<PERSON>vent, isDOM<PERSON>rror, isDOMException, addExceptionTypeValue, isError, isPlainObject, isEvent, addExceptionMechanism, isParameterizedString, getClient, normalizeToSize, extractExceptionKeysForMessage, resolvedSyncPromise } from '@sentry/core';\n\n/**\n * This function creates an exception from a JavaScript Error\n */\nfunction exceptionFromError(stackParser, ex) {\n  // Get the frames first since Opera can lose the stack if we touch anything else first\n  const frames = parseStackFrames(stackParser, ex);\n\n  const exception = {\n    type: extractType(ex),\n    value: extractMessage(ex),\n  };\n\n  if (frames.length) {\n    exception.stacktrace = { frames };\n  }\n\n  if (exception.type === undefined && exception.value === '') {\n    exception.value = 'Unrecoverable error caught';\n  }\n\n  return exception;\n}\n\nfunction eventFromPlainObject(\n  stackParser,\n  exception,\n  syntheticException,\n  isUnhandledRejection,\n) {\n  const client = getClient();\n  const normalizeDepth = client?.getOptions().normalizeDepth;\n\n  // If we can, we extract an exception from the object properties\n  const errorFromProp = getErrorPropertyFromObject(exception);\n\n  const extra = {\n    __serialized__: normalizeToSize(exception, normalizeDepth),\n  };\n\n  if (errorFromProp) {\n    return {\n      exception: {\n        values: [exceptionFromError(stackParser, errorFromProp)],\n      },\n      extra,\n    };\n  }\n\n  const event = {\n    exception: {\n      values: [\n        {\n          type: isEvent(exception) ? exception.constructor.name : isUnhandledRejection ? 'UnhandledRejection' : 'Error',\n          value: getNonErrorObjectExceptionValue(exception, { isUnhandledRejection }),\n        } ,\n      ],\n    },\n    extra,\n  } ;\n\n  if (syntheticException) {\n    const frames = parseStackFrames(stackParser, syntheticException);\n    if (frames.length) {\n      // event.exception.values[0] has been set above\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      event.exception.values[0].stacktrace = { frames };\n    }\n  }\n\n  return event;\n}\n\nfunction eventFromError(stackParser, ex) {\n  return {\n    exception: {\n      values: [exceptionFromError(stackParser, ex)],\n    },\n  };\n}\n\n/** Parses stack frames from an error */\nfunction parseStackFrames(\n  stackParser,\n  ex,\n) {\n  // Access and store the stacktrace property before doing ANYTHING\n  // else to it because Opera is not very good at providing it\n  // reliably in other circumstances.\n  const stacktrace = ex.stacktrace || ex.stack || '';\n\n  const skipLines = getSkipFirstStackStringLines(ex);\n  const framesToPop = getPopFirstTopFrames(ex);\n\n  try {\n    return stackParser(stacktrace, skipLines, framesToPop);\n  } catch (e) {\n    // no-empty\n  }\n\n  return [];\n}\n\n// Based on our own mapping pattern - https://github.com/getsentry/sentry/blob/9f08305e09866c8bd6d0c24f5b0aabdd7dd6c59c/src/sentry/lang/javascript/errormapping.py#L83-L108\nconst reactMinifiedRegexp = /Minified React error #\\d+;/i;\n\n/**\n * Certain known React errors contain links that would be falsely\n * parsed as frames. This function check for these errors and\n * returns number of the stack string lines to skip.\n */\nfunction getSkipFirstStackStringLines(ex) {\n  if (ex && reactMinifiedRegexp.test(ex.message)) {\n    return 1;\n  }\n\n  return 0;\n}\n\n/**\n * If error has `framesToPop` property, it means that the\n * creator tells us the first x frames will be useless\n * and should be discarded. Typically error from wrapper function\n * which don't point to the actual location in the developer's code.\n *\n * Example: https://github.com/zertosh/invariant/blob/master/invariant.js#L46\n */\nfunction getPopFirstTopFrames(ex) {\n  if (typeof ex.framesToPop === 'number') {\n    return ex.framesToPop;\n  }\n\n  return 0;\n}\n\n// https://developer.mozilla.org/en-US/docs/WebAssembly/JavaScript_interface/Exception\n// @ts-expect-error - WebAssembly.Exception is a valid class\nfunction isWebAssemblyException(exception) {\n  // Check for support\n  // @ts-expect-error - WebAssembly.Exception is a valid class\n  if (typeof WebAssembly !== 'undefined' && typeof WebAssembly.Exception !== 'undefined') {\n    // @ts-expect-error - WebAssembly.Exception is a valid class\n    return exception instanceof WebAssembly.Exception;\n  } else {\n    return false;\n  }\n}\n\n/**\n * Extracts from errors what we use as the exception `type` in error events.\n *\n * Usually, this is the `name` property on Error objects but WASM errors need to be treated differently.\n */\nfunction extractType(ex) {\n  const name = ex?.name;\n\n  // The name for WebAssembly.Exception Errors needs to be extracted differently.\n  // Context: https://github.com/getsentry/sentry-javascript/issues/13787\n  if (!name && isWebAssemblyException(ex)) {\n    // Emscripten sets array[type, message] to the \"message\" property on the WebAssembly.Exception object\n    const hasTypeInMessage = ex.message && Array.isArray(ex.message) && ex.message.length == 2;\n    return hasTypeInMessage ? ex.message[0] : 'WebAssembly.Exception';\n  }\n\n  return name;\n}\n\n/**\n * There are cases where stacktrace.message is an Event object\n * https://github.com/getsentry/sentry-javascript/issues/1949\n * In this specific case we try to extract stacktrace.message.error.message\n */\nfunction extractMessage(ex) {\n  const message = ex?.message;\n\n  if (isWebAssemblyException(ex)) {\n    // For Node 18, Emscripten sets array[type, message] to the \"message\" property on the WebAssembly.Exception object\n    if (Array.isArray(ex.message) && ex.message.length == 2) {\n      return ex.message[1];\n    }\n    return 'wasm exception';\n  }\n\n  if (!message) {\n    return 'No error message';\n  }\n\n  if (message.error && typeof message.error.message === 'string') {\n    return message.error.message;\n  }\n\n  return message;\n}\n\n/**\n * Creates an {@link Event} from all inputs to `captureException` and non-primitive inputs to `captureMessage`.\n * @hidden\n */\nfunction eventFromException(\n  stackParser,\n  exception,\n  hint,\n  attachStacktrace,\n) {\n  const syntheticException = hint?.syntheticException || undefined;\n  const event = eventFromUnknownInput(stackParser, exception, syntheticException, attachStacktrace);\n  addExceptionMechanism(event); // defaults to { type: 'generic', handled: true }\n  event.level = 'error';\n  if (hint?.event_id) {\n    event.event_id = hint.event_id;\n  }\n  return resolvedSyncPromise(event);\n}\n\n/**\n * Builds and Event from a Message\n * @hidden\n */\nfunction eventFromMessage(\n  stackParser,\n  message,\n  level = 'info',\n  hint,\n  attachStacktrace,\n) {\n  const syntheticException = hint?.syntheticException || undefined;\n  const event = eventFromString(stackParser, message, syntheticException, attachStacktrace);\n  event.level = level;\n  if (hint?.event_id) {\n    event.event_id = hint.event_id;\n  }\n  return resolvedSyncPromise(event);\n}\n\n/**\n * @hidden\n */\nfunction eventFromUnknownInput(\n  stackParser,\n  exception,\n  syntheticException,\n  attachStacktrace,\n  isUnhandledRejection,\n) {\n  let event;\n\n  if (isErrorEvent(exception ) && (exception ).error) {\n    // If it is an ErrorEvent with `error` property, extract it to get actual Error\n    const errorEvent = exception ;\n    return eventFromError(stackParser, errorEvent.error );\n  }\n\n  // If it is a `DOMError` (which is a legacy API, but still supported in some browsers) then we just extract the name\n  // and message, as it doesn't provide anything else. According to the spec, all `DOMExceptions` should also be\n  // `Error`s, but that's not the case in IE11, so in that case we treat it the same as we do a `DOMError`.\n  //\n  // https://developer.mozilla.org/en-US/docs/Web/API/DOMError\n  // https://developer.mozilla.org/en-US/docs/Web/API/DOMException\n  // https://webidl.spec.whatwg.org/#es-DOMException-specialness\n  if (isDOMError(exception) || isDOMException(exception )) {\n    const domException = exception ;\n\n    if ('stack' in (exception )) {\n      event = eventFromError(stackParser, exception );\n    } else {\n      const name = domException.name || (isDOMError(domException) ? 'DOMError' : 'DOMException');\n      const message = domException.message ? `${name}: ${domException.message}` : name;\n      event = eventFromString(stackParser, message, syntheticException, attachStacktrace);\n      addExceptionTypeValue(event, message);\n    }\n    if ('code' in domException) {\n      // eslint-disable-next-line deprecation/deprecation\n      event.tags = { ...event.tags, 'DOMException.code': `${domException.code}` };\n    }\n\n    return event;\n  }\n  if (isError(exception)) {\n    // we have a real Error object, do nothing\n    return eventFromError(stackParser, exception);\n  }\n  if (isPlainObject(exception) || isEvent(exception)) {\n    // If it's a plain object or an instance of `Event` (the built-in JS kind, not this SDK's `Event` type), serialize\n    // it manually. This will allow us to group events based on top-level keys which is much better than creating a new\n    // group on any key/value change.\n    const objectException = exception ;\n    event = eventFromPlainObject(stackParser, objectException, syntheticException, isUnhandledRejection);\n    addExceptionMechanism(event, {\n      synthetic: true,\n    });\n    return event;\n  }\n\n  // If none of previous checks were valid, then it means that it's not:\n  // - an instance of DOMError\n  // - an instance of DOMException\n  // - an instance of Event\n  // - an instance of Error\n  // - a valid ErrorEvent (one with an error property)\n  // - a plain Object\n  //\n  // So bail out and capture it as a simple message:\n  event = eventFromString(stackParser, exception , syntheticException, attachStacktrace);\n  addExceptionTypeValue(event, `${exception}`, undefined);\n  addExceptionMechanism(event, {\n    synthetic: true,\n  });\n\n  return event;\n}\n\nfunction eventFromString(\n  stackParser,\n  message,\n  syntheticException,\n  attachStacktrace,\n) {\n  const event = {};\n\n  if (attachStacktrace && syntheticException) {\n    const frames = parseStackFrames(stackParser, syntheticException);\n    if (frames.length) {\n      event.exception = {\n        values: [{ value: message, stacktrace: { frames } }],\n      };\n    }\n    addExceptionMechanism(event, { synthetic: true });\n  }\n\n  if (isParameterizedString(message)) {\n    const { __sentry_template_string__, __sentry_template_values__ } = message;\n\n    event.logentry = {\n      message: __sentry_template_string__,\n      params: __sentry_template_values__,\n    };\n    return event;\n  }\n\n  event.message = message;\n  return event;\n}\n\nfunction getNonErrorObjectExceptionValue(\n  exception,\n  { isUnhandledRejection },\n) {\n  const keys = extractExceptionKeysForMessage(exception);\n  const captureType = isUnhandledRejection ? 'promise rejection' : 'exception';\n\n  // Some ErrorEvent instances do not have an `error` property, which is why they are not handled before\n  // We still want to try to get a decent message for these cases\n  if (isErrorEvent(exception)) {\n    return `Event \\`ErrorEvent\\` captured as ${captureType} with message \\`${exception.message}\\``;\n  }\n\n  if (isEvent(exception)) {\n    const className = getObjectClassName(exception);\n    return `Event \\`${className}\\` (type=${exception.type}) captured as ${captureType}`;\n  }\n\n  return `Object captured as ${captureType} with keys: ${keys}`;\n}\n\nfunction getObjectClassName(obj) {\n  try {\n    const prototype = Object.getPrototypeOf(obj);\n    return prototype ? prototype.constructor.name : undefined;\n  } catch (e) {\n    // ignore errors here\n  }\n}\n\n/** If a plain object has a property that is an `Error`, return this error. */\nfunction getErrorPropertyFromObject(obj) {\n  for (const prop in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n      const value = obj[prop];\n      if (value instanceof Error) {\n        return value;\n      }\n    }\n  }\n\n  return undefined;\n}\n\nexport { eventFromException, eventFromMessage, eventFromUnknownInput, exceptionFromError, extractMessage, extractType };\n//# sourceMappingURL=eventbuilder.js.map\n", "import { Client, getSDKSource, applySdkMetadata, _INTERNAL_flushLogsBuffer, addAutoIpAddressToUser, addAutoIpAddressToSession } from '@sentry/core';\nimport { eventFromException, eventFromMessage } from './eventbuilder.js';\nimport { WINDOW } from './helpers.js';\n\nconst DEFAULT_FLUSH_INTERVAL = 5000;\n\n/**\n * Configuration options for the Sentry Browser SDK.\n * @see @sentry/core Options for more information.\n */\n\n/**\n * The Sentry Browser SDK Client.\n *\n * @see BrowserOptions for documentation on configuration options.\n * @see SentryClient for usage documentation.\n */\nclass BrowserClient extends Client {\n\n  /**\n   * Creates a new Browser SDK instance.\n   *\n   * @param options Configuration options for this SDK.\n   */\n   constructor(options) {\n    const opts = {\n      // We default this to true, as it is the safer scenario\n      parentSpanIsAlwaysRootSpan: true,\n      ...options,\n    };\n    const sdkSource = WINDOW.SENTRY_SDK_SOURCE || getSDKSource();\n    applySdkMetadata(opts, 'browser', ['browser'], sdkSource);\n\n    super(opts);\n\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const client = this;\n    const { sendDefaultPii, _experiments } = client._options;\n    const enableLogs = _experiments?.enableLogs;\n\n    if (opts.sendClientReports && WINDOW.document) {\n      WINDOW.document.addEventListener('visibilitychange', () => {\n        if (WINDOW.document.visibilityState === 'hidden') {\n          this._flushOutcomes();\n          if (enableLogs) {\n            _INTERNAL_flushLogsBuffer(client);\n          }\n        }\n      });\n    }\n\n    if (enableLogs) {\n      client.on('flush', () => {\n        _INTERNAL_flushLogsBuffer(client);\n      });\n\n      client.on('afterCaptureLog', () => {\n        if (client._logFlushIdleTimeout) {\n          clearTimeout(client._logFlushIdleTimeout);\n        }\n\n        client._logFlushIdleTimeout = setTimeout(() => {\n          _INTERNAL_flushLogsBuffer(client);\n        }, DEFAULT_FLUSH_INTERVAL);\n      });\n    }\n\n    if (sendDefaultPii) {\n      client.on('postprocessEvent', addAutoIpAddressToUser);\n      client.on('beforeSendSession', addAutoIpAddressToSession);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n   eventFromException(exception, hint) {\n    return eventFromException(this._options.stackParser, exception, hint, this._options.attachStacktrace);\n  }\n\n  /**\n   * @inheritDoc\n   */\n   eventFromMessage(\n    message,\n    level = 'info',\n    hint,\n  ) {\n    return eventFromMessage(this._options.stackParser, message, level, hint, this._options.attachStacktrace);\n  }\n\n  /**\n   * @inheritDoc\n   */\n   _prepareEvent(\n    event,\n    hint,\n    currentScope,\n    isolationScope,\n  ) {\n    event.platform = event.platform || 'javascript';\n\n    return super._prepareEvent(event, hint, currentScope, isolationScope);\n  }\n}\n\nexport { BrowserClient };\n//# sourceMappingURL=client.js.map\n", "/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nconst DEBUG_BUILD = (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__);\n\nexport { DEBUG_BUILD };\n//# sourceMappingURL=debug-build.js.map\n", "import { GL<PERSON><PERSON>L_OBJ } from '@sentry/core';\n\nconst WINDOW = GLOBAL_OBJ\n\n;\n\nexport { WINDOW };\n//# sourceMappingURL=types.js.map\n", "import { addHandler, maybeInstrument, triggerHandlers, fill, addNonEnumerableProperty, uuid4 } from '@sentry/core';\nimport { WINDOW } from '../types.js';\n\nconst DEBOUNCE_DURATION = 1000;\n\nlet debounceTimerID;\nlet lastCapturedEventType;\nlet lastCapturedEventTargetId;\n\n/**\n * Add an instrumentation handler for when a click or a keypress happens.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nfunction addClickKeypressInstrumentationHandler(handler) {\n  const type = 'dom';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentDOM);\n}\n\n/** Exported for tests only. */\nfunction instrumentDOM() {\n  if (!WINDOW.document) {\n    return;\n  }\n\n  // Make it so that any click or keypress that is unhandled / bubbled up all the way to the document triggers our dom\n  // handlers. (Normally we have only one, which captures a breadcrumb for each click or keypress.) Do this before\n  // we instrument `addEventListener` so that we don't end up attaching this handler twice.\n  const triggerDOMHandler = triggerHandlers.bind(null, 'dom');\n  const globalDOMEventHandler = makeDOMEventHandler(triggerDOMHandler, true);\n  WINDOW.document.addEventListener('click', globalDOMEventHandler, false);\n  WINDOW.document.addEventListener('keypress', globalDOMEventHandler, false);\n\n  // After hooking into click and keypress events bubbled up to `document`, we also hook into user-handled\n  // clicks & keypresses, by adding an event listener of our own to any element to which they add a listener. That\n  // way, whenever one of their handlers is triggered, ours will be, too. (This is needed because their handler\n  // could potentially prevent the event from bubbling up to our global listeners. This way, our handler are still\n  // guaranteed to fire at least once.)\n  ['EventTarget', 'Node'].forEach((target) => {\n    const globalObject = WINDOW ;\n    const proto = globalObject[target]?.prototype;\n\n    // eslint-disable-next-line no-prototype-builtins\n    if (!proto?.hasOwnProperty?.('addEventListener')) {\n      return;\n    }\n\n    fill(proto, 'addEventListener', function (originalAddEventListener) {\n      return function ( type, listener, options) {\n        if (type === 'click' || type == 'keypress') {\n          try {\n            const handlers = (this.__sentry_instrumentation_handlers__ =\n              this.__sentry_instrumentation_handlers__ || {});\n            const handlerForType = (handlers[type] = handlers[type] || { refCount: 0 });\n\n            if (!handlerForType.handler) {\n              const handler = makeDOMEventHandler(triggerDOMHandler);\n              handlerForType.handler = handler;\n              originalAddEventListener.call(this, type, handler, options);\n            }\n\n            handlerForType.refCount++;\n          } catch (e) {\n            // Accessing dom properties is always fragile.\n            // Also allows us to skip `addEventListeners` calls with no proper `this` context.\n          }\n        }\n\n        return originalAddEventListener.call(this, type, listener, options);\n      };\n    });\n\n    fill(\n      proto,\n      'removeEventListener',\n      function (originalRemoveEventListener) {\n        return function ( type, listener, options) {\n          if (type === 'click' || type == 'keypress') {\n            try {\n              const handlers = this.__sentry_instrumentation_handlers__ || {};\n              const handlerForType = handlers[type];\n\n              if (handlerForType) {\n                handlerForType.refCount--;\n                // If there are no longer any custom handlers of the current type on this element, we can remove ours, too.\n                if (handlerForType.refCount <= 0) {\n                  originalRemoveEventListener.call(this, type, handlerForType.handler, options);\n                  handlerForType.handler = undefined;\n                  delete handlers[type]; // eslint-disable-line @typescript-eslint/no-dynamic-delete\n                }\n\n                // If there are no longer any custom handlers of any type on this element, cleanup everything.\n                if (Object.keys(handlers).length === 0) {\n                  delete this.__sentry_instrumentation_handlers__;\n                }\n              }\n            } catch (e) {\n              // Accessing dom properties is always fragile.\n              // Also allows us to skip `addEventListeners` calls with no proper `this` context.\n            }\n          }\n\n          return originalRemoveEventListener.call(this, type, listener, options);\n        };\n      },\n    );\n  });\n}\n\n/**\n * Check whether the event is similar to the last captured one. For example, two click events on the same button.\n */\nfunction isSimilarToLastCapturedEvent(event) {\n  // If both events have different type, then user definitely performed two separate actions. e.g. click + keypress.\n  if (event.type !== lastCapturedEventType) {\n    return false;\n  }\n\n  try {\n    // If both events have the same type, it's still possible that actions were performed on different targets.\n    // e.g. 2 clicks on different buttons.\n    if (!event.target || (event.target )._sentryId !== lastCapturedEventTargetId) {\n      return false;\n    }\n  } catch (e) {\n    // just accessing `target` property can throw an exception in some rare circumstances\n    // see: https://github.com/getsentry/sentry-javascript/issues/838\n  }\n\n  // If both events have the same type _and_ same `target` (an element which triggered an event, _not necessarily_\n  // to which an event listener was attached), we treat them as the same action, as we want to capture\n  // only one breadcrumb. e.g. multiple clicks on the same button, or typing inside a user input box.\n  return true;\n}\n\n/**\n * Decide whether an event should be captured.\n * @param event event to be captured\n */\nfunction shouldSkipDOMEvent(eventType, target) {\n  // We are only interested in filtering `keypress` events for now.\n  if (eventType !== 'keypress') {\n    return false;\n  }\n\n  if (!target?.tagName) {\n    return true;\n  }\n\n  // Only consider keypress events on actual input elements. This will disregard keypresses targeting body\n  // e.g.tabbing through elements, hotkeys, etc.\n  if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Wraps addEventListener to capture UI breadcrumbs\n */\nfunction makeDOMEventHandler(\n  handler,\n  globalListener = false,\n) {\n  return (event) => {\n    // It's possible this handler might trigger multiple times for the same\n    // event (e.g. event propagation through node ancestors).\n    // Ignore if we've already captured that event.\n    if (!event || event['_sentryCaptured']) {\n      return;\n    }\n\n    const target = getEventTarget(event);\n\n    // We always want to skip _some_ events.\n    if (shouldSkipDOMEvent(event.type, target)) {\n      return;\n    }\n\n    // Mark event as \"seen\"\n    addNonEnumerableProperty(event, '_sentryCaptured', true);\n\n    if (target && !target._sentryId) {\n      // Add UUID to event target so we can identify if\n      addNonEnumerableProperty(target, '_sentryId', uuid4());\n    }\n\n    const name = event.type === 'keypress' ? 'input' : event.type;\n\n    // If there is no last captured event, it means that we can safely capture the new event and store it for future comparisons.\n    // If there is a last captured event, see if the new event is different enough to treat it as a unique one.\n    // If that's the case, emit the previous event and store locally the newly-captured DOM event.\n    if (!isSimilarToLastCapturedEvent(event)) {\n      const handlerData = { event, name, global: globalListener };\n      handler(handlerData);\n      lastCapturedEventType = event.type;\n      lastCapturedEventTargetId = target ? target._sentryId : undefined;\n    }\n\n    // Start a new debounce timer that will prevent us from capturing multiple events that should be grouped together.\n    clearTimeout(debounceTimerID);\n    debounceTimerID = WINDOW.setTimeout(() => {\n      lastCapturedEventTargetId = undefined;\n      lastCapturedEventType = undefined;\n    }, DEBOUNCE_DURATION);\n  };\n}\n\nfunction getEventTarget(event) {\n  try {\n    return event.target ;\n  } catch (e) {\n    // just accessing `target` property can throw an exception in some rare circumstances\n    // see: https://github.com/getsentry/sentry-javascript/issues/838\n    return null;\n  }\n}\n\nexport { addClickKeypressInstrumentationHandler, instrumentDOM };\n//# sourceMappingURL=dom.js.map\n", "import { addHand<PERSON>, maybeInstrument, triggerHandlers, supportsHistory, fill } from '@sentry/core';\nimport { WINDOW } from '../types.js';\n\nlet lastHref;\n\n/**\n * Add an instrumentation handler for when a fetch request happens.\n * The handler function is called once when the request starts and once when it ends,\n * which can be identified by checking if it has an `endTimestamp`.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nfunction addHistoryInstrumentationHandler(handler) {\n  const type = 'history';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentHistory);\n}\n\n/**\n * Exported just for testing\n */\nfunction instrumentHistory() {\n  // The `popstate` event may also be triggered on `pushState`, but it may not always reliably be emitted by the browser\n  // Which is why we also monkey-patch methods below, in addition to this\n  WINDOW.addEventListener('popstate', () => {\n    const to = WINDOW.location.href;\n    // keep track of the current URL state, as we always receive only the updated state\n    const from = lastHref;\n    lastHref = to;\n\n    if (from === to) {\n      return;\n    }\n\n    const handlerData = { from, to } ;\n    triggerHandlers('history', handlerData);\n  });\n\n  // Just guard against this not being available, in weird environments\n  if (!supportsHistory()) {\n    return;\n  }\n\n  function historyReplacementFunction(originalHistoryFunction) {\n    return function ( ...args) {\n      const url = args.length > 2 ? args[2] : undefined;\n      if (url) {\n        const from = lastHref;\n\n        // Ensure the URL is absolute\n        // this can be either a path, then it is relative to the current origin\n        // or a full URL of the current origin - other origins are not allowed\n        // See: https://developer.mozilla.org/en-US/docs/Web/API/History/pushState#url\n        // coerce to string (this is what pushState does)\n        const to = getAbsoluteUrl(String(url));\n\n        // keep track of the current URL state, as we always receive only the updated state\n        lastHref = to;\n\n        if (from === to) {\n          return originalHistoryFunction.apply(this, args);\n        }\n\n        const handlerData = { from, to } ;\n        triggerHandlers('history', handlerData);\n      }\n      return originalHistoryFunction.apply(this, args);\n    };\n  }\n\n  fill(WINDOW.history, 'pushState', historyReplacementFunction);\n  fill(WINDOW.history, 'replaceState', historyReplacementFunction);\n}\n\nfunction getAbsoluteUrl(urlOrPath) {\n  try {\n    const url = new URL(urlOrPath, WINDOW.location.origin);\n    return url.toString();\n  } catch {\n    // fallback, just do nothing\n    return urlOrPath;\n  }\n}\n\nexport { addHistoryInstrumentationHandler, instrumentHistory };\n//# sourceMappingURL=history.js.map\n", "import { addHandler, maybeInstrument, timestampInSeconds, isString, triggerHandlers } from '@sentry/core';\nimport { WINDOW } from '../types.js';\n\nconst SENTRY_XHR_DATA_KEY = '__sentry_xhr_v3__';\n\n/**\n * Add an instrumentation handler for when an XHR request happens.\n * The handler function is called once when the request starts and once when it ends,\n * which can be identified by checking if it has an `endTimestamp`.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nfunction addXhrInstrumentationHandler(handler) {\n  const type = 'xhr';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentXHR);\n}\n\n/** Exported only for tests. */\nfunction instrumentXHR() {\n  if (!(WINDOW ).XMLHttpRequest) {\n    return;\n  }\n\n  const xhrproto = XMLHttpRequest.prototype;\n\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  xhrproto.open = new Proxy(xhrproto.open, {\n    apply(\n      originalOpen,\n      xhrOpenThisArg,\n      xhrOpenArgArray\n\n,\n    ) {\n      // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n      //       it means the error, that was caused by your XHR call did not\n      //       have a stack trace. If you are using HttpClient integration,\n      //       this is the expected behavior, as we are using this virtual error to capture\n      //       the location of your XHR call, and group your HttpClient events accordingly.\n      const virtualError = new Error();\n\n      const startTimestamp = timestampInSeconds() * 1000;\n\n      // open() should always be called with two or more arguments\n      // But to be on the safe side, we actually validate this and bail out if we don't have a method & url\n      const method = isString(xhrOpenArgArray[0]) ? xhrOpenArgArray[0].toUpperCase() : undefined;\n      const url = parseXhrUrlArg(xhrOpenArgArray[1]);\n\n      if (!method || !url) {\n        return originalOpen.apply(xhrOpenThisArg, xhrOpenArgArray);\n      }\n\n      xhrOpenThisArg[SENTRY_XHR_DATA_KEY] = {\n        method,\n        url,\n        request_headers: {},\n      };\n\n      // if Sentry key appears in URL, don't capture it as a request\n      if (method === 'POST' && url.match(/sentry_key/)) {\n        xhrOpenThisArg.__sentry_own_request__ = true;\n      }\n\n      const onreadystatechangeHandler = () => {\n        // For whatever reason, this is not the same instance here as from the outer method\n        const xhrInfo = xhrOpenThisArg[SENTRY_XHR_DATA_KEY];\n\n        if (!xhrInfo) {\n          return;\n        }\n\n        if (xhrOpenThisArg.readyState === 4) {\n          try {\n            // touching statusCode in some platforms throws\n            // an exception\n            xhrInfo.status_code = xhrOpenThisArg.status;\n          } catch (e) {\n            /* do nothing */\n          }\n\n          const handlerData = {\n            endTimestamp: timestampInSeconds() * 1000,\n            startTimestamp,\n            xhr: xhrOpenThisArg,\n            virtualError,\n          };\n          triggerHandlers('xhr', handlerData);\n        }\n      };\n\n      if ('onreadystatechange' in xhrOpenThisArg && typeof xhrOpenThisArg.onreadystatechange === 'function') {\n        xhrOpenThisArg.onreadystatechange = new Proxy(xhrOpenThisArg.onreadystatechange, {\n          apply(originalOnreadystatechange, onreadystatechangeThisArg, onreadystatechangeArgArray) {\n            onreadystatechangeHandler();\n            return originalOnreadystatechange.apply(onreadystatechangeThisArg, onreadystatechangeArgArray);\n          },\n        });\n      } else {\n        xhrOpenThisArg.addEventListener('readystatechange', onreadystatechangeHandler);\n      }\n\n      // Intercepting `setRequestHeader` to access the request headers of XHR instance.\n      // This will only work for user/library defined headers, not for the default/browser-assigned headers.\n      // Request cookies are also unavailable for XHR, as `Cookie` header can't be defined by `setRequestHeader`.\n      xhrOpenThisArg.setRequestHeader = new Proxy(xhrOpenThisArg.setRequestHeader, {\n        apply(\n          originalSetRequestHeader,\n          setRequestHeaderThisArg,\n          setRequestHeaderArgArray,\n        ) {\n          const [header, value] = setRequestHeaderArgArray;\n\n          const xhrInfo = setRequestHeaderThisArg[SENTRY_XHR_DATA_KEY];\n\n          if (xhrInfo && isString(header) && isString(value)) {\n            xhrInfo.request_headers[header.toLowerCase()] = value;\n          }\n\n          return originalSetRequestHeader.apply(setRequestHeaderThisArg, setRequestHeaderArgArray);\n        },\n      });\n\n      return originalOpen.apply(xhrOpenThisArg, xhrOpenArgArray);\n    },\n  });\n\n  // eslint-disable-next-line @typescript-eslint/unbound-method\n  xhrproto.send = new Proxy(xhrproto.send, {\n    apply(originalSend, sendThisArg, sendArgArray) {\n      const sentryXhrData = sendThisArg[SENTRY_XHR_DATA_KEY];\n\n      if (!sentryXhrData) {\n        return originalSend.apply(sendThisArg, sendArgArray);\n      }\n\n      if (sendArgArray[0] !== undefined) {\n        sentryXhrData.body = sendArgArray[0];\n      }\n\n      const handlerData = {\n        startTimestamp: timestampInSeconds() * 1000,\n        xhr: sendThisArg,\n      };\n      triggerHandlers('xhr', handlerData);\n\n      return originalSend.apply(sendThisArg, sendArgArray);\n    },\n  });\n}\n\n/**\n * Parses the URL argument of a XHR method to a string.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/open#url\n * url: A string or any other object with a stringifier — including a URL object — that provides the URL of the resource to send the request to.\n *\n * @param url - The URL argument of an XHR method\n * @returns The parsed URL string or undefined if the URL is invalid\n */\nfunction parseXhrUrlArg(url) {\n  if (isString(url)) {\n    return url;\n  }\n\n  try {\n    // If the passed in argument is not a string, it should have a `toString` method as a stringifier.\n    // If that fails, we just return undefined (like in IE11 where URL is not available)\n    return (url ).toString();\n  } catch {} // eslint-disable-line no-empty\n\n  return undefined;\n}\n\nexport { SENTRY_XHR_DATA_KEY, addXhrInstrumentationHandler, instrumentXHR };\n//# sourceMappingURL=xhr.js.map\n", "import { createTransport, rejectedSyncPromise } from '@sentry/core';\nimport { getNativeImplementation, clearCachedImplementation } from '@sentry-internal/browser-utils';\n\n/**\n * Creates a Transport that uses the Fetch API to send events to Sentry.\n */\nfunction makeFetchTransport(\n  options,\n  nativeFetch = getNativeImplementation('fetch'),\n) {\n  let pendingBodySize = 0;\n  let pendingCount = 0;\n\n  function makeRequest(request) {\n    const requestSize = request.body.length;\n    pendingBodySize += requestSize;\n    pendingCount++;\n\n    const requestOptions = {\n      body: request.body,\n      method: 'POST',\n      referrerPolicy: 'strict-origin',\n      headers: options.headers,\n      // Outgoing requests are usually cancelled when navigating to a different page, causing a \"TypeError: Failed to\n      // fetch\" error and sending a \"network_error\" client-outcome - in Chrome, the request status shows \"(cancelled)\".\n      // The `keepalive` flag keeps outgoing requests alive, even when switching pages. We want this since we're\n      // frequently sending events right before the user is switching pages (eg. when finishing navigation transactions).\n      // Gotchas:\n      // - `keepalive` isn't supported by Firefox\n      // - As per spec (https://fetch.spec.whatwg.org/#http-network-or-cache-fetch):\n      //   If the sum of contentLength and inflightKeepaliveBytes is greater than 64 kibibytes, then return a network error.\n      //   We will therefore only activate the flag when we're below that limit.\n      // There is also a limit of requests that can be open at the same time, so we also limit this to 15\n      // See https://github.com/getsentry/sentry-javascript/pull/7553 for details\n      keepalive: pendingBodySize <= 60000 && pendingCount < 15,\n      ...options.fetchOptions,\n    };\n\n    if (!nativeFetch) {\n      clearCachedImplementation('fetch');\n      return rejectedSyncPromise('No fetch implementation available');\n    }\n\n    try {\n      // TODO: This may need a `suppressTracing` call in the future when we switch the browser SDK to OTEL\n      return nativeFetch(options.url, requestOptions).then(response => {\n        pendingBodySize -= requestSize;\n        pendingCount--;\n        return {\n          statusCode: response.status,\n          headers: {\n            'x-sentry-rate-limits': response.headers.get('X-Sentry-Rate-Limits'),\n            'retry-after': response.headers.get('Retry-After'),\n          },\n        };\n      });\n    } catch (e) {\n      clearCachedImplementation('fetch');\n      pendingBodySize -= requestSize;\n      pendingCount--;\n      return rejectedSyncPromise(e);\n    }\n  }\n\n  return createTransport(options, makeRequest);\n}\n\nexport { makeFetchTransport };\n//# sourceMappingURL=fetch.js.map\n", "import { createSta<PERSON><PERSON><PERSON><PERSON>, UNKNOWN_FUNCTION } from '@sentry/core';\n\nconst OPERA10_PRIORITY = 10;\nconst OPERA11_PRIORITY = 20;\nconst CHROME_PRIORITY = 30;\nconst WINJS_PRIORITY = 40;\nconst GECKO_PRIORITY = 50;\n\nfunction createFrame(filename, func, lineno, colno) {\n  const frame = {\n    filename,\n    function: func === '<anonymous>' ? UNKNOWN_FUNCTION : func,\n    in_app: true, // All browser frames are considered in_app\n  };\n\n  if (lineno !== undefined) {\n    frame.lineno = lineno;\n  }\n\n  if (colno !== undefined) {\n    frame.colno = colno;\n  }\n\n  return frame;\n}\n\n// This regex matches frames that have no function name (ie. are at the top level of a module).\n// For example \"at http://localhost:5000//script.js:1:126\"\n// Frames _with_ function names usually look as follows: \"at commitLayoutEffects (react-dom.development.js:23426:1)\"\nconst chromeRegexNoFnName = /^\\s*at (\\S+?)(?::(\\d+))(?::(\\d+))\\s*$/i;\n\n// This regex matches all the frames that have a function name.\nconst chromeRegex =\n  /^\\s*at (?:(.+?\\)(?: \\[.+\\])?|.*?) ?\\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\\/)?.*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i;\n\nconst chromeEvalRegex = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/;\n\n// Chromium based browsers: Chrome, Brave, new Opera, new Edge\n// We cannot call this variable `chrome` because it can conflict with global `chrome` variable in certain environments\n// See: https://github.com/getsentry/sentry-javascript/issues/6880\nconst chromeStackParserFn = line => {\n  // If the stack line has no function name, we need to parse it differently\n  const noFnParts = chromeRegexNoFnName.exec(line) ;\n\n  if (noFnParts) {\n    const [, filename, line, col] = noFnParts;\n    return createFrame(filename, UNKNOWN_FUNCTION, +line, +col);\n  }\n\n  const parts = chromeRegex.exec(line) ;\n\n  if (parts) {\n    const isEval = parts[2] && parts[2].indexOf('eval') === 0; // start of line\n\n    if (isEval) {\n      const subMatch = chromeEvalRegex.exec(parts[2]) ;\n\n      if (subMatch) {\n        // throw out eval line/column and use top-most line/column number\n        parts[2] = subMatch[1]; // url\n        parts[3] = subMatch[2]; // line\n        parts[4] = subMatch[3]; // column\n      }\n    }\n\n    // Kamil: One more hack won't hurt us right? Understanding and adding more rules on top of these regexps right now\n    // would be way too time consuming. (TODO: Rewrite whole RegExp to be more readable)\n    const [func, filename] = extractSafariExtensionDetails(parts[1] || UNKNOWN_FUNCTION, parts[2]);\n\n    return createFrame(filename, func, parts[3] ? +parts[3] : undefined, parts[4] ? +parts[4] : undefined);\n  }\n\n  return;\n};\n\nconst chromeStackLineParser = [CHROME_PRIORITY, chromeStackParserFn];\n\n// gecko regex: `(?:bundle|\\d+\\.js)`: `bundle` is for react native, `\\d+\\.js` also but specifically for ram bundles because it\n// generates filenames without a prefix like `file://` the filenames in the stacktrace are just 42.js\n// We need this specific case for now because we want no other regex to match.\nconst geckoREgex =\n  /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)?((?:[-a-z]+)?:\\/.*?|\\[native code\\]|[^@]*(?:bundle|\\d+\\.js)|\\/[\\w\\-. /=]+)(?::(\\d+))?(?::(\\d+))?\\s*$/i;\nconst geckoEvalRegex = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i;\n\nconst gecko = line => {\n  const parts = geckoREgex.exec(line) ;\n\n  if (parts) {\n    const isEval = parts[3] && parts[3].indexOf(' > eval') > -1;\n    if (isEval) {\n      const subMatch = geckoEvalRegex.exec(parts[3]) ;\n\n      if (subMatch) {\n        // throw out eval line/column and use top-most line number\n        parts[1] = parts[1] || 'eval';\n        parts[3] = subMatch[1];\n        parts[4] = subMatch[2];\n        parts[5] = ''; // no column when eval\n      }\n    }\n\n    let filename = parts[3];\n    let func = parts[1] || UNKNOWN_FUNCTION;\n    [func, filename] = extractSafariExtensionDetails(func, filename);\n\n    return createFrame(filename, func, parts[4] ? +parts[4] : undefined, parts[5] ? +parts[5] : undefined);\n  }\n\n  return;\n};\n\nconst geckoStackLineParser = [GECKO_PRIORITY, gecko];\n\nconst winjsRegex = /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:[-a-z]+):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\n\nconst winjs = line => {\n  const parts = winjsRegex.exec(line) ;\n\n  return parts\n    ? createFrame(parts[2], parts[1] || UNKNOWN_FUNCTION, +parts[3], parts[4] ? +parts[4] : undefined)\n    : undefined;\n};\n\nconst winjsStackLineParser = [WINJS_PRIORITY, winjs];\n\nconst opera10Regex = / line (\\d+).*script (?:in )?(\\S+)(?:: in function (\\S+))?$/i;\n\nconst opera10 = line => {\n  const parts = opera10Regex.exec(line) ;\n  return parts ? createFrame(parts[2], parts[3] || UNKNOWN_FUNCTION, +parts[1]) : undefined;\n};\n\nconst opera10StackLineParser = [OPERA10_PRIORITY, opera10];\n\nconst opera11Regex =\n  / line (\\d+), column (\\d+)\\s*(?:in (?:<anonymous function: ([^>]+)>|([^)]+))\\(.*\\))? in (.*):\\s*$/i;\n\nconst opera11 = line => {\n  const parts = opera11Regex.exec(line) ;\n  return parts ? createFrame(parts[5], parts[3] || parts[4] || UNKNOWN_FUNCTION, +parts[1], +parts[2]) : undefined;\n};\n\nconst opera11StackLineParser = [OPERA11_PRIORITY, opera11];\n\nconst defaultStackLineParsers = [chromeStackLineParser, geckoStackLineParser];\n\nconst defaultStackParser = createStackParser(...defaultStackLineParsers);\n\n/**\n * Safari web extensions, starting version unknown, can produce \"frames-only\" stacktraces.\n * What it means, is that instead of format like:\n *\n * Error: wat\n *   at function@url:row:col\n *   at function@url:row:col\n *   at function@url:row:col\n *\n * it produces something like:\n *\n *   function@url:row:col\n *   function@url:row:col\n *   function@url:row:col\n *\n * Because of that, it won't be captured by `chrome` RegExp and will fall into `Gecko` branch.\n * This function is extracted so that we can use it in both places without duplicating the logic.\n * Unfortunately \"just\" changing RegExp is too complicated now and making it pass all tests\n * and fix this case seems like an impossible, or at least way too time-consuming task.\n */\nconst extractSafariExtensionDetails = (func, filename) => {\n  const isSafariExtension = func.indexOf('safari-extension') !== -1;\n  const isSafariWebExtension = func.indexOf('safari-web-extension') !== -1;\n\n  return isSafariExtension || isSafariWebExtension\n    ? [\n        func.indexOf('@') !== -1 ? (func.split('@')[0] ) : UNKNOWN_FUNCTION,\n        isSafariExtension ? `safari-extension:${filename}` : `safari-web-extension:${filename}`,\n      ]\n    : [func, filename];\n};\n\nexport { chromeStackLineParser, defaultStackLineParsers, defaultStackParser, geckoStackLineParser, opera10StackLineParser, opera11StackLineParser, winjsStackLineParser };\n//# sourceMappingURL=stack-parsers.js.map\n", "/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nconst DEBUG_BUILD = (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__);\n\nexport { DEBUG_BUILD };\n//# sourceMappingURL=debug-build.js.map\n", "import { defineIntegration, addConsoleInstrumentationHandler, addFetchInstrumentationHandler, getClient, safeJoin, severityLevelFromString, addBreadcrumb, logger, htmlTreeAsString, getComponentName, getBreadcrumbLogLevelFromHttpStatusCode, parseUrl, getEventDescription } from '@sentry/core';\nimport { addClickKeypressInstrumentationHandler, addXhrInstrumentationHandler, addHistoryInstrumentationHandler, SENTRY_XHR_DATA_KEY } from '@sentry-internal/browser-utils';\nimport { DEBUG_BUILD } from '../debug-build.js';\nimport { WINDOW } from '../helpers.js';\n\n/** maxStringLength gets capped to prevent 100 breadcrumbs exceeding 1MB event payload size */\nconst MAX_ALLOWED_STRING_LENGTH = 1024;\n\nconst INTEGRATION_NAME = 'Breadcrumbs';\n\nconst _breadcrumbsIntegration = ((options = {}) => {\n  const _options = {\n    console: true,\n    dom: true,\n    fetch: true,\n    history: true,\n    sentry: true,\n    xhr: true,\n    ...options,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    setup(client) {\n      // TODO(v10): Remove this functionality and use `consoleIntegration` from @sentry/core instead.\n      if (_options.console) {\n        addConsoleInstrumentationHandler(_getConsoleBreadcrumbHandler(client));\n      }\n      if (_options.dom) {\n        addClickKeypressInstrumentationHandler(_getDomBreadcrumbHandler(client, _options.dom));\n      }\n      if (_options.xhr) {\n        addXhrInstrumentationHandler(_getXhrBreadcrumbHandler(client));\n      }\n      if (_options.fetch) {\n        addFetchInstrumentationHandler(_getFetchBreadcrumbHandler(client));\n      }\n      if (_options.history) {\n        addHistoryInstrumentationHandler(_getHistoryBreadcrumbHandler(client));\n      }\n      if (_options.sentry) {\n        client.on('beforeSendEvent', _getSentryBreadcrumbHandler(client));\n      }\n    },\n  };\n}) ;\n\nconst breadcrumbsIntegration = defineIntegration(_breadcrumbsIntegration);\n\n/**\n * Adds a breadcrumb for Sentry events or transactions if this option is enabled.\n */\nfunction _getSentryBreadcrumbHandler(client) {\n  return function addSentryBreadcrumb(event) {\n    if (getClient() !== client) {\n      return;\n    }\n\n    addBreadcrumb(\n      {\n        category: `sentry.${event.type === 'transaction' ? 'transaction' : 'event'}`,\n        event_id: event.event_id,\n        level: event.level,\n        message: getEventDescription(event),\n      },\n      {\n        event,\n      },\n    );\n  };\n}\n\n/**\n * A HOC that creates a function that creates breadcrumbs from DOM API calls.\n * This is a HOC so that we get access to dom options in the closure.\n */\nfunction _getDomBreadcrumbHandler(\n  client,\n  dom,\n) {\n  return function _innerDomBreadcrumb(handlerData) {\n    if (getClient() !== client) {\n      return;\n    }\n\n    let target;\n    let componentName;\n    let keyAttrs = typeof dom === 'object' ? dom.serializeAttribute : undefined;\n\n    let maxStringLength =\n      typeof dom === 'object' && typeof dom.maxStringLength === 'number' ? dom.maxStringLength : undefined;\n    if (maxStringLength && maxStringLength > MAX_ALLOWED_STRING_LENGTH) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `\\`dom.maxStringLength\\` cannot exceed ${MAX_ALLOWED_STRING_LENGTH}, but a value of ${maxStringLength} was configured. Sentry will use ${MAX_ALLOWED_STRING_LENGTH} instead.`,\n        );\n      maxStringLength = MAX_ALLOWED_STRING_LENGTH;\n    }\n\n    if (typeof keyAttrs === 'string') {\n      keyAttrs = [keyAttrs];\n    }\n\n    // Accessing event.target can throw (see getsentry/raven-js#838, #768)\n    try {\n      const event = handlerData.event ;\n      const element = _isEvent(event) ? event.target : event;\n\n      target = htmlTreeAsString(element, { keyAttrs, maxStringLength });\n      componentName = getComponentName(element);\n    } catch (e) {\n      target = '<unknown>';\n    }\n\n    if (target.length === 0) {\n      return;\n    }\n\n    const breadcrumb = {\n      category: `ui.${handlerData.name}`,\n      message: target,\n    };\n\n    if (componentName) {\n      breadcrumb.data = { 'ui.component_name': componentName };\n    }\n\n    addBreadcrumb(breadcrumb, {\n      event: handlerData.event,\n      name: handlerData.name,\n      global: handlerData.global,\n    });\n  };\n}\n\n/**\n * Creates breadcrumbs from console API calls\n */\nfunction _getConsoleBreadcrumbHandler(client) {\n  return function _consoleBreadcrumb(handlerData) {\n    if (getClient() !== client) {\n      return;\n    }\n\n    const breadcrumb = {\n      category: 'console',\n      data: {\n        arguments: handlerData.args,\n        logger: 'console',\n      },\n      level: severityLevelFromString(handlerData.level),\n      message: safeJoin(handlerData.args, ' '),\n    };\n\n    if (handlerData.level === 'assert') {\n      if (handlerData.args[0] === false) {\n        breadcrumb.message = `Assertion failed: ${safeJoin(handlerData.args.slice(1), ' ') || 'console.assert'}`;\n        breadcrumb.data.arguments = handlerData.args.slice(1);\n      } else {\n        // Don't capture a breadcrumb for passed assertions\n        return;\n      }\n    }\n\n    addBreadcrumb(breadcrumb, {\n      input: handlerData.args,\n      level: handlerData.level,\n    });\n  };\n}\n\n/**\n * Creates breadcrumbs from XHR API calls\n */\nfunction _getXhrBreadcrumbHandler(client) {\n  return function _xhrBreadcrumb(handlerData) {\n    if (getClient() !== client) {\n      return;\n    }\n\n    const { startTimestamp, endTimestamp } = handlerData;\n\n    const sentryXhrData = handlerData.xhr[SENTRY_XHR_DATA_KEY];\n\n    // We only capture complete, non-sentry requests\n    if (!startTimestamp || !endTimestamp || !sentryXhrData) {\n      return;\n    }\n\n    const { method, url, status_code, body } = sentryXhrData;\n\n    const data = {\n      method,\n      url,\n      status_code,\n    };\n\n    const hint = {\n      xhr: handlerData.xhr,\n      input: body,\n      startTimestamp,\n      endTimestamp,\n    };\n\n    const breadcrumb = {\n      category: 'xhr',\n      data,\n      type: 'http',\n      level: getBreadcrumbLogLevelFromHttpStatusCode(status_code),\n    };\n\n    client.emit('beforeOutgoingRequestBreadcrumb', breadcrumb, hint );\n\n    addBreadcrumb(breadcrumb, hint);\n  };\n}\n\n/**\n * Creates breadcrumbs from fetch API calls\n */\nfunction _getFetchBreadcrumbHandler(client) {\n  return function _fetchBreadcrumb(handlerData) {\n    if (getClient() !== client) {\n      return;\n    }\n\n    const { startTimestamp, endTimestamp } = handlerData;\n\n    // We only capture complete fetch requests\n    if (!endTimestamp) {\n      return;\n    }\n\n    if (handlerData.fetchData.url.match(/sentry_key/) && handlerData.fetchData.method === 'POST') {\n      // We will not create breadcrumbs for fetch requests that contain `sentry_key` (internal sentry requests)\n      return;\n    }\n\n    ({\n      method: handlerData.fetchData.method,\n      url: handlerData.fetchData.url,\n    });\n\n    if (handlerData.error) {\n      const data = handlerData.fetchData;\n      const hint = {\n        data: handlerData.error,\n        input: handlerData.args,\n        startTimestamp,\n        endTimestamp,\n      };\n\n      const breadcrumb = {\n        category: 'fetch',\n        data,\n        level: 'error',\n        type: 'http',\n      } ;\n\n      client.emit('beforeOutgoingRequestBreadcrumb', breadcrumb, hint );\n\n      addBreadcrumb(breadcrumb, hint);\n    } else {\n      const response = handlerData.response ;\n      const data = {\n        ...handlerData.fetchData,\n        status_code: response?.status,\n      };\n\n      handlerData.fetchData.request_body_size;\n      handlerData.fetchData.response_body_size;\n      response?.status;\n\n      const hint = {\n        input: handlerData.args,\n        response,\n        startTimestamp,\n        endTimestamp,\n      };\n\n      const breadcrumb = {\n        category: 'fetch',\n        data,\n        type: 'http',\n        level: getBreadcrumbLogLevelFromHttpStatusCode(data.status_code),\n      };\n\n      client.emit('beforeOutgoingRequestBreadcrumb', breadcrumb, hint );\n\n      addBreadcrumb(breadcrumb, hint);\n    }\n  };\n}\n\n/**\n * Creates breadcrumbs from history API calls\n */\nfunction _getHistoryBreadcrumbHandler(client) {\n  return function _historyBreadcrumb(handlerData) {\n    if (getClient() !== client) {\n      return;\n    }\n\n    let from = handlerData.from;\n    let to = handlerData.to;\n    const parsedLoc = parseUrl(WINDOW.location.href);\n    let parsedFrom = from ? parseUrl(from) : undefined;\n    const parsedTo = parseUrl(to);\n\n    // Initial pushState doesn't provide `from` information\n    if (!parsedFrom?.path) {\n      parsedFrom = parsedLoc;\n    }\n\n    // Use only the path component of the URL if the URL matches the current\n    // document (almost all the time when using pushState)\n    if (parsedLoc.protocol === parsedTo.protocol && parsedLoc.host === parsedTo.host) {\n      to = parsedTo.relative;\n    }\n    if (parsedLoc.protocol === parsedFrom.protocol && parsedLoc.host === parsedFrom.host) {\n      from = parsedFrom.relative;\n    }\n\n    addBreadcrumb({\n      category: 'navigation',\n      data: {\n        from,\n        to,\n      },\n    });\n  };\n}\n\nfunction _isEvent(event) {\n  return !!event && !!(event ).target;\n}\n\nexport { breadcrumbsIntegration };\n//# sourceMappingURL=breadcrumbs.js.map\n", "import { defineIntegration, fill, getFunctionName, getOriginalFunction } from '@sentry/core';\nimport { WINDOW, wrap } from '../helpers.js';\n\nconst DEFAULT_EVENT_TARGET = [\n  'EventTarget',\n  'Window',\n  'Node',\n  'ApplicationCache',\n  'AudioTrackList',\n  'BroadcastChannel',\n  'ChannelMergerNode',\n  'CryptoOperation',\n  'EventSource',\n  'FileReader',\n  'HTMLUnknownElement',\n  'IDBDatabase',\n  'IDBRequest',\n  'IDBTransaction',\n  'KeyOperation',\n  'MediaController',\n  'MessagePort',\n  'ModalWindow',\n  'Notification',\n  'SVGElementInstance',\n  'Screen',\n  'SharedWorker',\n  'TextTrack',\n  'TextTrackCue',\n  'TextTrackList',\n  'WebSocket',\n  'WebSocketWorker',\n  'Worker',\n  'XMLHttpRequest',\n  'XMLHttpRequestEventTarget',\n  'XMLHttpRequestUpload',\n];\n\nconst INTEGRATION_NAME = 'BrowserApiErrors';\n\nconst _browserApiErrorsIntegration = ((options = {}) => {\n  const _options = {\n    XMLHttpRequest: true,\n    eventTarget: true,\n    requestAnimationFrame: true,\n    setInterval: true,\n    setTimeout: true,\n    ...options,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO: This currently only works for the first client this is setup\n    // We may want to adjust this to check for client etc.\n    setupOnce() {\n      if (_options.setTimeout) {\n        fill(WINDOW, 'setTimeout', _wrapTimeFunction);\n      }\n\n      if (_options.setInterval) {\n        fill(WINDOW, 'setInterval', _wrapTimeFunction);\n      }\n\n      if (_options.requestAnimationFrame) {\n        fill(WINDOW, 'requestAnimationFrame', _wrapRAF);\n      }\n\n      if (_options.XMLHttpRequest && 'XMLHttpRequest' in WINDOW) {\n        fill(XMLHttpRequest.prototype, 'send', _wrapXHR);\n      }\n\n      const eventTargetOption = _options.eventTarget;\n      if (eventTargetOption) {\n        const eventTarget = Array.isArray(eventTargetOption) ? eventTargetOption : DEFAULT_EVENT_TARGET;\n        eventTarget.forEach(_wrapEventTarget);\n      }\n    },\n  };\n}) ;\n\n/**\n * Wrap timer functions and event targets to catch errors and provide better meta data.\n */\nconst browserApiErrorsIntegration = defineIntegration(_browserApiErrorsIntegration);\n\nfunction _wrapTimeFunction(original) {\n  return function ( ...args) {\n    const originalCallback = args[0];\n    args[0] = wrap(originalCallback, {\n      mechanism: {\n        data: { function: getFunctionName(original) },\n        handled: false,\n        type: 'instrument',\n      },\n    });\n    return original.apply(this, args);\n  };\n}\n\nfunction _wrapRAF(original) {\n  return function ( callback) {\n    return original.apply(this, [\n      wrap(callback, {\n        mechanism: {\n          data: {\n            function: 'requestAnimationFrame',\n            handler: getFunctionName(original),\n          },\n          handled: false,\n          type: 'instrument',\n        },\n      }),\n    ]);\n  };\n}\n\nfunction _wrapXHR(originalSend) {\n  return function ( ...args) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const xhr = this;\n    const xmlHttpRequestProps = ['onload', 'onerror', 'onprogress', 'onreadystatechange'];\n\n    xmlHttpRequestProps.forEach(prop => {\n      if (prop in xhr && typeof xhr[prop] === 'function') {\n        fill(xhr, prop, function (original) {\n          const wrapOptions = {\n            mechanism: {\n              data: {\n                function: prop,\n                handler: getFunctionName(original),\n              },\n              handled: false,\n              type: 'instrument',\n            },\n          };\n\n          // If Instrument integration has been called before BrowserApiErrors, get the name of original function\n          const originalFunction = getOriginalFunction(original);\n          if (originalFunction) {\n            wrapOptions.mechanism.data.handler = getFunctionName(originalFunction);\n          }\n\n          // Otherwise wrap directly\n          return wrap(original, wrapOptions);\n        });\n      }\n    });\n\n    return originalSend.apply(this, args);\n  };\n}\n\nfunction _wrapEventTarget(target) {\n  const globalObject = WINDOW ;\n  const proto = globalObject[target]?.prototype;\n\n  // eslint-disable-next-line no-prototype-builtins\n  if (!proto?.hasOwnProperty?.('addEventListener')) {\n    return;\n  }\n\n  fill(proto, 'addEventListener', function (original)\n\n {\n    return function ( eventName, fn, options) {\n      try {\n        if (isEventListenerObject(fn)) {\n          // ESlint disable explanation:\n          //  First, it is generally safe to call `wrap` with an unbound function. Furthermore, using `.bind()` would\n          //  introduce a bug here, because bind returns a new function that doesn't have our\n          //  flags(like __sentry_original__) attached. `wrap` checks for those flags to avoid unnecessary wrapping.\n          //  Without those flags, every call to addEventListener wraps the function again, causing a memory leak.\n          // eslint-disable-next-line @typescript-eslint/unbound-method\n          fn.handleEvent = wrap(fn.handleEvent, {\n            mechanism: {\n              data: {\n                function: 'handleEvent',\n                handler: getFunctionName(fn),\n                target,\n              },\n              handled: false,\n              type: 'instrument',\n            },\n          });\n        }\n      } catch {\n        // can sometimes get 'Permission denied to access property \"handle Event'\n      }\n\n      return original.apply(this, [\n        eventName,\n        wrap(fn, {\n          mechanism: {\n            data: {\n              function: 'addEventListener',\n              handler: getFunctionName(fn),\n              target,\n            },\n            handled: false,\n            type: 'instrument',\n          },\n        }),\n        options,\n      ]);\n    };\n  });\n\n  fill(proto, 'removeEventListener', function (originalRemoveEventListener)\n\n {\n    return function ( eventName, fn, options) {\n      /**\n       * There are 2 possible scenarios here:\n       *\n       * 1. Someone passes a callback, which was attached prior to Sentry initialization, or by using unmodified\n       * method, eg. `document.addEventListener.call(el, name, handler). In this case, we treat this function\n       * as a pass-through, and call original `removeEventListener` with it.\n       *\n       * 2. Someone passes a callback, which was attached after Sentry was initialized, which means that it was using\n       * our wrapped version of `addEventListener`, which internally calls `wrap` helper.\n       * This helper \"wraps\" whole callback inside a try/catch statement, and attached appropriate metadata to it,\n       * in order for us to make a distinction between wrapped/non-wrapped functions possible.\n       * If a function was wrapped, it has additional property of `__sentry_wrapped__`, holding the handler.\n       *\n       * When someone adds a handler prior to initialization, and then do it again, but after,\n       * then we have to detach both of them. Otherwise, if we'd detach only wrapped one, it'd be impossible\n       * to get rid of the initial handler and it'd stick there forever.\n       */\n      try {\n        const originalEventHandler = (fn ).__sentry_wrapped__;\n        if (originalEventHandler) {\n          originalRemoveEventListener.call(this, eventName, originalEventHandler, options);\n        }\n      } catch (e) {\n        // ignore, accessing __sentry_wrapped__ will throw in some Selenium environments\n      }\n      return originalRemoveEventListener.call(this, eventName, fn, options);\n    };\n  });\n}\n\nfunction isEventListenerObject(obj) {\n  return typeof (obj ).handleEvent === 'function';\n}\n\nexport { browserApiErrorsIntegration };\n//# sourceMappingURL=browserapierrors.js.map\n", "import { defineIntegration, logger, startSession, captureSession } from '@sentry/core';\nimport { addHistoryInstrumentationHandler } from '@sentry-internal/browser-utils';\nimport { DEBUG_BUILD } from '../debug-build.js';\nimport { WINDOW } from '../helpers.js';\n\n/**\n * When added, automatically creates sessions which allow you to track adoption and crashes (crash free rate) in your Releases in Sentry.\n * More information: https://docs.sentry.io/product/releases/health/\n *\n * Note: In order for session tracking to work, you need to set up Releases: https://docs.sentry.io/product/releases/\n */\nconst browserSessionIntegration = defineIntegration(() => {\n  return {\n    name: 'BrowserSession',\n    setupOnce() {\n      if (typeof WINDOW.document === 'undefined') {\n        DEBUG_BUILD &&\n          logger.warn('Using the `browserSessionIntegration` in non-browser environments is not supported.');\n        return;\n      }\n\n      // The session duration for browser sessions does not track a meaningful\n      // concept that can be used as a metric.\n      // Automatically captured sessions are akin to page views, and thus we\n      // discard their duration.\n      startSession({ ignoreDuration: true });\n      captureSession();\n\n      // We want to create a session for every navigation as well\n      addHistoryInstrumentationHandler(({ from, to }) => {\n        // Don't create an additional session for the initial route or if the location did not change\n        if (from !== undefined && from !== to) {\n          startSession({ ignoreDuration: true });\n          captureSession();\n        }\n      });\n    },\n  };\n});\n\nexport { browserSessionIntegration };\n//# sourceMappingURL=browsersession.js.map\n", "import { defineIntegration, addGlobalErrorInstrumentationHandler, getClient, captureEvent, logger, addGlobalUnhandledRejectionInstrumentationHandler, isPrimitive, isString, getLocationHref, UNKNOWN_FUNCTION } from '@sentry/core';\nimport { DEBUG_BUILD } from '../debug-build.js';\nimport { eventFromUnknownInput } from '../eventbuilder.js';\nimport { shouldIgnoreOnError } from '../helpers.js';\n\nconst INTEGRATION_NAME = 'GlobalHandlers';\n\nconst _globalHandlersIntegration = ((options = {}) => {\n  const _options = {\n    onerror: true,\n    onunhandledrejection: true,\n    ...options,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      Error.stackTraceLimit = 50;\n    },\n    setup(client) {\n      if (_options.onerror) {\n        _installGlobalOnErrorHandler(client);\n        globalHandlerLog('onerror');\n      }\n      if (_options.onunhandledrejection) {\n        _installGlobalOnUnhandledRejectionHandler(client);\n        globalHandlerLog('onunhandledrejection');\n      }\n    },\n  };\n}) ;\n\nconst globalHandlersIntegration = defineIntegration(_globalHandlersIntegration);\n\nfunction _installGlobalOnErrorHandler(client) {\n  addGlobalErrorInstrumentationHandler(data => {\n    const { stackParser, attachStacktrace } = getOptions();\n\n    if (getClient() !== client || shouldIgnoreOnError()) {\n      return;\n    }\n\n    const { msg, url, line, column, error } = data;\n\n    const event = _enhanceEventWithInitialFrame(\n      eventFromUnknownInput(stackParser, error || msg, undefined, attachStacktrace, false),\n      url,\n      line,\n      column,\n    );\n\n    event.level = 'error';\n\n    captureEvent(event, {\n      originalException: error,\n      mechanism: {\n        handled: false,\n        type: 'onerror',\n      },\n    });\n  });\n}\n\nfunction _installGlobalOnUnhandledRejectionHandler(client) {\n  addGlobalUnhandledRejectionInstrumentationHandler(e => {\n    const { stackParser, attachStacktrace } = getOptions();\n\n    if (getClient() !== client || shouldIgnoreOnError()) {\n      return;\n    }\n\n    const error = _getUnhandledRejectionError(e );\n\n    const event = isPrimitive(error)\n      ? _eventFromRejectionWithPrimitive(error)\n      : eventFromUnknownInput(stackParser, error, undefined, attachStacktrace, true);\n\n    event.level = 'error';\n\n    captureEvent(event, {\n      originalException: error,\n      mechanism: {\n        handled: false,\n        type: 'onunhandledrejection',\n      },\n    });\n  });\n}\n\nfunction _getUnhandledRejectionError(error) {\n  if (isPrimitive(error)) {\n    return error;\n  }\n\n  // dig the object of the rejection out of known event types\n  try {\n\n    // PromiseRejectionEvents store the object of the rejection under 'reason'\n    // see https://developer.mozilla.org/en-US/docs/Web/API/PromiseRejectionEvent\n    if ('reason' in (error )) {\n      return (error ).reason;\n    }\n\n    // something, somewhere, (likely a browser extension) effectively casts PromiseRejectionEvents\n    // to CustomEvents, moving the `promise` and `reason` attributes of the PRE into\n    // the CustomEvent's `detail` attribute, since they're not part of CustomEvent's spec\n    // see https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent and\n    // https://github.com/getsentry/sentry-javascript/issues/2380\n    if ('detail' in (error ) && 'reason' in (error ).detail) {\n      return (error ).detail.reason;\n    }\n  } catch {} // eslint-disable-line no-empty\n\n  return error;\n}\n\n/**\n * Create an event from a promise rejection where the `reason` is a primitive.\n *\n * @param reason: The `reason` property of the promise rejection\n * @returns An Event object with an appropriate `exception` value\n */\nfunction _eventFromRejectionWithPrimitive(reason) {\n  return {\n    exception: {\n      values: [\n        {\n          type: 'UnhandledRejection',\n          // String() is needed because the Primitive type includes symbols (which can't be automatically stringified)\n          value: `Non-Error promise rejection captured with value: ${String(reason)}`,\n        },\n      ],\n    },\n  };\n}\n\nfunction _enhanceEventWithInitialFrame(\n  event,\n  url,\n  line,\n  column,\n) {\n  // event.exception\n  const e = (event.exception = event.exception || {});\n  // event.exception.values\n  const ev = (e.values = e.values || []);\n  // event.exception.values[0]\n  const ev0 = (ev[0] = ev[0] || {});\n  // event.exception.values[0].stacktrace\n  const ev0s = (ev0.stacktrace = ev0.stacktrace || {});\n  // event.exception.values[0].stacktrace.frames\n  const ev0sf = (ev0s.frames = ev0s.frames || []);\n\n  const colno = column;\n  const lineno = line;\n  const filename = isString(url) && url.length > 0 ? url : getLocationHref();\n\n  // event.exception.values[0].stacktrace.frames\n  if (ev0sf.length === 0) {\n    ev0sf.push({\n      colno,\n      filename,\n      function: UNKNOWN_FUNCTION,\n      in_app: true,\n      lineno,\n    });\n  }\n\n  return event;\n}\n\nfunction globalHandlerLog(type) {\n  DEBUG_BUILD && logger.log(`Global Handler attached: ${type}`);\n}\n\nfunction getOptions() {\n  const client = getClient();\n  const options = client?.getOptions() || {\n    stackParser: () => [],\n    attachStacktrace: false,\n  };\n  return options;\n}\n\nexport { globalHandlersIntegration };\n//# sourceMappingURL=globalhandlers.js.map\n", "import { defineIntegration } from '@sentry/core';\nimport { WINDOW, getHttpRequestData } from '../helpers.js';\n\n/**\n * Collects information about HTTP request headers and\n * attaches them to the event.\n */\nconst httpContextIntegration = defineIntegration(() => {\n  return {\n    name: 'HttpContext',\n    preprocessEvent(event) {\n      // if none of the information we want exists, don't bother\n      if (!WINDOW.navigator && !WINDOW.location && !WINDOW.document) {\n        return;\n      }\n\n      const reqData = getHttpRequestData();\n      const headers = {\n        ...reqData.headers,\n        ...event.request?.headers,\n      };\n\n      event.request = {\n        ...reqData,\n        ...event.request,\n        headers,\n      };\n    },\n  };\n});\n\nexport { httpContextIntegration };\n//# sourceMappingURL=httpcontext.js.map\n", "import { defineIntegration, applyAggregateErrorsToEvent } from '@sentry/core';\nimport { exceptionFromError } from '../eventbuilder.js';\n\nconst DEFAULT_KEY = 'cause';\nconst DEFAULT_LIMIT = 5;\n\nconst INTEGRATION_NAME = 'LinkedErrors';\n\nconst _linkedErrorsIntegration = ((options = {}) => {\n  const limit = options.limit || DEFAULT_LIMIT;\n  const key = options.key || DEFAULT_KEY;\n\n  return {\n    name: INTEGRATION_NAME,\n    preprocessEvent(event, hint, client) {\n      const options = client.getOptions();\n\n      applyAggregateErrorsToEvent(\n        // This differs from the LinkedErrors integration in core by using a different exceptionFromError function\n        exceptionFromError,\n        options.stackParser,\n        key,\n        limit,\n        event,\n        hint,\n      );\n    },\n  };\n}) ;\n\n/**\n * Aggregrate linked errors in an event.\n */\nconst linkedErrorsIntegration = defineIntegration(_linkedErrorsIntegration);\n\nexport { linkedErrorsIntegration };\n//# sourceMappingURL=linkederrors.js.map\n", "import { inboundFiltersIntegration, functionToStringIntegration, dedupeIntegration, getIntegrationsToSetup, stackParserFromStackParserOptions, initAndBind, consoleSandbox, getLocationHref } from '@sentry/core';\nimport { BrowserClient } from './client.js';\nimport { DEBUG_BUILD } from './debug-build.js';\nimport { WINDOW } from './helpers.js';\nimport { breadcrumbsIntegration } from './integrations/breadcrumbs.js';\nimport { browserApiErrorsIntegration } from './integrations/browserapierrors.js';\nimport { browserSessionIntegration } from './integrations/browsersession.js';\nimport { globalHandlersIntegration } from './integrations/globalhandlers.js';\nimport { httpContextIntegration } from './integrations/httpcontext.js';\nimport { linkedErrorsIntegration } from './integrations/linkederrors.js';\nimport { defaultStackParser } from './stack-parsers.js';\nimport { makeFetchTransport } from './transports/fetch.js';\n\n/** Get the default integrations for the browser SDK. */\nfunction getDefaultIntegrations(_options) {\n  /**\n   * Note: Please make sure this stays in sync with Angular SDK, which re-exports\n   * `getDefaultIntegrations` but with an adjusted set of integrations.\n   */\n  return [\n    // TODO(v10): Replace with `eventFiltersIntegration` once we remove the deprecated `inboundFiltersIntegration`\n    // eslint-disable-next-line deprecation/deprecation\n    inboundFiltersIntegration(),\n    functionToStringIntegration(),\n    browserApiErrorsIntegration(),\n    breadcrumbsIntegration(),\n    globalHandlersIntegration(),\n    linkedErrorsIntegration(),\n    dedupeIntegration(),\n    httpContextIntegration(),\n    browserSessionIntegration(),\n  ];\n}\n\n/** Exported only for tests. */\nfunction applyDefaultOptions(optionsArg = {}) {\n  const defaultOptions = {\n    defaultIntegrations: getDefaultIntegrations(),\n    release:\n      typeof __SENTRY_RELEASE__ === 'string' // This allows build tooling to find-and-replace __SENTRY_RELEASE__ to inject a release value\n        ? __SENTRY_RELEASE__\n        : WINDOW.SENTRY_RELEASE?.id, // This supports the variable that sentry-webpack-plugin injects\n    sendClientReports: true,\n  };\n\n  return {\n    ...defaultOptions,\n    ...dropTopLevelUndefinedKeys(optionsArg),\n  };\n}\n\n/**\n * In contrast to the regular `dropUndefinedKeys` method,\n * this one does not deep-drop keys, but only on the top level.\n */\nfunction dropTopLevelUndefinedKeys(obj) {\n  const mutatetedObj = {};\n\n  for (const k of Object.getOwnPropertyNames(obj)) {\n    const key = k ;\n    if (obj[key] !== undefined) {\n      mutatetedObj[key] = obj[key];\n    }\n  }\n\n  return mutatetedObj;\n}\n\n/**\n * The Sentry Browser SDK Client.\n *\n * To use this SDK, call the {@link init} function as early as possible when\n * loading the web page. To set context information or send manual events, use\n * the provided methods.\n *\n * @example\n *\n * ```\n *\n * import { init } from '@sentry/browser';\n *\n * init({\n *   dsn: '__DSN__',\n *   // ...\n * });\n * ```\n *\n * @example\n * ```\n *\n * import { addBreadcrumb } from '@sentry/browser';\n * addBreadcrumb({\n *   message: 'My Breadcrumb',\n *   // ...\n * });\n * ```\n *\n * @example\n *\n * ```\n *\n * import * as Sentry from '@sentry/browser';\n * Sentry.captureMessage('Hello, world!');\n * Sentry.captureException(new Error('Good bye'));\n * Sentry.captureEvent({\n *   message: 'Manual',\n *   stacktrace: [\n *     // ...\n *   ],\n * });\n * ```\n *\n * @see {@link BrowserOptions} for documentation on configuration options.\n */\nfunction init(browserOptions = {}) {\n  if (!browserOptions.skipBrowserExtensionCheck && _checkForBrowserExtension()) {\n    return;\n  }\n\n  const options = applyDefaultOptions(browserOptions);\n  const clientOptions = {\n    ...options,\n    stackParser: stackParserFromStackParserOptions(options.stackParser || defaultStackParser),\n    integrations: getIntegrationsToSetup(options),\n    transport: options.transport || makeFetchTransport,\n  };\n\n  return initAndBind(BrowserClient, clientOptions);\n}\n\n/**\n * This function is here to be API compatible with the loader.\n * @hidden\n */\nfunction forceLoad() {\n  // Noop\n}\n\n/**\n * This function is here to be API compatible with the loader.\n * @hidden\n */\nfunction onLoad(callback) {\n  callback();\n}\n\nfunction _isEmbeddedBrowserExtension() {\n  if (typeof WINDOW.window === 'undefined') {\n    // No need to show the error if we're not in a browser window environment (e.g. service workers)\n    return false;\n  }\n\n  const _window = WINDOW ;\n\n  // Running the SDK in NW.js, which appears like a browser extension but isn't, is also fine\n  // see: https://github.com/getsentry/sentry-javascript/issues/12668\n  if (_window.nw) {\n    return false;\n  }\n\n  const extensionObject = _window['chrome'] || _window['browser'];\n\n  if (!extensionObject?.runtime?.id) {\n    return false;\n  }\n\n  const href = getLocationHref();\n  const extensionProtocols = ['chrome-extension', 'moz-extension', 'ms-browser-extension', 'safari-web-extension'];\n\n  // Running the SDK in a dedicated extension page and calling Sentry.init is fine; no risk of data leakage\n  const isDedicatedExtensionPage =\n    WINDOW === WINDOW.top && extensionProtocols.some(protocol => href.startsWith(`${protocol}://`));\n\n  return !isDedicatedExtensionPage;\n}\n\nfunction _checkForBrowserExtension() {\n  if (_isEmbeddedBrowserExtension()) {\n    if (DEBUG_BUILD) {\n      consoleSandbox(() => {\n        // eslint-disable-next-line no-console\n        console.error(\n          '[Sentry] You cannot use Sentry.init() in a browser extension, see: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/',\n        );\n      });\n    }\n\n    return true;\n  }\n}\n\nexport { applyDefaultOptions, forceLoad, getDefaultIntegrations, init, onLoad };\n//# sourceMappingURL=sdk.js.map\n", "import * as Sentry from \"@sentry/browser\";\r\n\r\nexport default async function() {\r\n    return await new Promise((resolve) => {\r\n        Sentry.init({\r\n            dsn: \"https://<EMAIL>/9\",\r\n            integrations: [],\r\n            beforeSend(event) {\r\n                if (event.exception && event.exception.values) {\r\n                    const errorValue = event.exception.values[0];\r\n                    if (errorValue.stacktrace && errorValue.stacktrace.frames) {\r\n                        const isFromSdk = errorValue.stacktrace.frames.some(\r\n                            frame => frame.filename && frame.filename.includes('pcm-')\r\n                        );\r\n                        if (!isFromSdk) {\r\n                            return null;\r\n                        }\r\n                    }\r\n                }\r\n                return event;\r\n            },\r\n            tracesSampleRate: 0.1,\r\n        });\r\n        resolve({});\r\n    });\r\n}\r\n\r\n", "import appGlobalScript from 'D:/code/agents-sdk/packages/pcm-agents/src/utils/init.ts';\nexport const globalScripts = appGlobalScript;\n", "export { setNonce } from '@stencil/core';\nimport { bootstrapLazy } from '@stencil/core';\nimport { globalScripts } from '@stencil/core/internal/app-globals';\nexport const defineCustomElements = async (win, options) => {\n  if (typeof window === 'undefined') return undefined;\n  await globalScripts();\n  return bootstrapLazy([/*!__STENCIL_LAZY_DATA__*/], options);\n};\n", "(function(){if(\"undefined\"!==typeof window&&void 0!==window.Reflect&&void 0!==window.customElements){var a=HTMLElement;window.HTMLElement=function(){return Reflect.construct(a,[],this.constructor)};HTMLElement.prototype=a.prototype;HTMLElement.prototype.constructor=HTMLElement;Object.setPrototypeOf(HTMLElement,a)}})();\nexport * from '../dist/esm/loader.js';"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAAS,gBAAgB,YAAY;AACnC,MAAI,OAAO,eAAe,WAAW;AACnC,WAAO,OAAO,UAAU;EAC5B;AAEE,QAAM,OAAO,OAAO,eAAe,WAAW,WAAW,UAAU,IAAI;AACvE,MAAI,OAAO,SAAS,YAAY,MAAM,IAAI,KAAK,OAAO,KAAK,OAAO,GAAG;AACnE,WAAO;EACX;AAEE,SAAO;AACT;ACbA,IAAM,WAAW,CAAA;AACjB,IAAM,eAAe,CAAA;AAGrB,SAAS,WAAW,MAAM,SAAS;AACjC,WAAS,IAAI,IAAI,SAAS,IAAI,KAAK,CAAA;AACnC,EAAC,SAAS,IAAI,EAAI,KAAK,OAAO;AAChC;AAaA,SAAS,gBAAgB,MAAM,cAAc;AAC3C,MAAI,CAAC,aAAa,IAAI,GAAG;AACvB,iBAAa,IAAI,IAAI;AACrB,QAAI;AACF,mBAAY;IAClB,SAAa,GAAG;AACVA,qBAAe,OAAO,MAAM,6BAA6B,IAAI,IAAI,CAAC;IACxE;EACA;AACA;AAGA,SAAS,gBAAgB,MAAM,MAAM;AACnC,QAAM,eAAe,QAAQ,SAAS,IAAI;AAC1C,MAAI,CAAC,cAAc;AACjB;EACJ;AAEE,aAAW,WAAW,cAAc;AAClC,QAAI;AACF,cAAQ,IAAI;IAClB,SAAa,GAAG;AACVA,qBACE,OAAO;QACL;QAA0D,IAAI;QAAW,gBAAgB,OAAO,CAAC;;QACjG;MACV;IACA;EACA;AACA;ACnDA,IAAI,qBAAqB;AAQzB,SAAS,qCAAqC,SAAS;AACrD,QAAM,OAAO;AACb,aAAW,MAAM,OAAO;AACxB,kBAAgB,MAAM,eAAe;AACvC;AAEA,SAAS,kBAAkB;AACzB,uBAAqB,WAAW;AAIhC,aAAW,UAAU,SACnB,KACA,KACA,MACA,QACA,OACA;AACA,UAAM,cAAc;MAClB;MACA;MACA;MACA;MACA;IACN;AACI,oBAAgB,SAAS,WAAW;AAEpC,QAAI,oBAAoB;AAEtB,aAAO,mBAAmB,MAAM,MAAM,SAAS;IACrD;AAEI,WAAO;EACX;AAEE,aAAW,QAAQ,0BAA0B;AAC/C;AC5CA,IAAI,kCAAkC;AAQtC,SAAS,kDACP,SACA;AACA,QAAM,OAAO;AACb,aAAW,MAAM,OAAO;AACxB,kBAAgB,MAAM,4BAA4B;AACpD;AAEA,SAAS,+BAA+B;AACtC,oCAAkC,WAAW;AAI7C,aAAW,uBAAuB,SAAU,GAAG;AAC7C,UAAM,cAAc;AACpB,oBAAgB,sBAAsB,WAAW;AAEjD,QAAI,iCAAiC;AAEnC,aAAO,gCAAgC,MAAM,MAAM,SAAS;IAClE;AAEI,WAAO;EACX;AAEE,aAAW,qBAAqB,0BAA0B;AAC5D;AC3BA,SAAS,eAAe,SAAS,QAAQ,CAAA,GAAI;AAC3C,SAAO,CAAC,SAAS,KAAK;AACxB;AAOA,SAAS,kBAAkB,UAAU,SAAS;AAC5C,QAAM,CAAC,SAAS,KAAK,IAAI;AACzB,SAAO,CAAC,SAAS,CAAC,GAAG,OAAO,OAAO,CAAC;AACtC;AAQA,SAAS,oBACP,UACA,UACA;AACA,QAAM,gBAAgB,SAAS,CAAC;AAEhC,aAAW,gBAAgB,eAAe;AACxC,UAAM,mBAAmB,aAAa,CAAC,EAAE;AACzC,UAAM,SAAS,SAAS,cAAc,gBAAgB;AAEtD,QAAI,QAAQ;AACV,aAAO;IACb;EACA;AAEE,SAAO;AACT;AAYA,SAAS,WAAW,OAAO;AACzB,QAAM,UAAU,iBAAiB,UAAU;AAC3C,SAAO,QAAQ,iBAAiB,QAAQ,eAAe,KAAK,IAAI,IAAI,YAAW,EAAG,OAAO,KAAK;AAChG;AAaA,SAAS,kBAAkB,UAAU;AACnC,QAAM,CAAC,YAAY,KAAK,IAAI;AAE5B,MAAI,QAAQ,KAAK,UAAU,UAAU;AAErC,WAAS,OAAO,MAAM;AACpB,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,OAAO,SAAS,WAAW,QAAQ,OAAO,CAAC,WAAW,KAAK,GAAG,IAAI;IAChF,OAAW;AACL,YAAM,KAAK,OAAO,SAAS,WAAW,WAAW,IAAI,IAAI,IAAI;IACnE;EACA;AAEE,aAAW,QAAQ,OAAO;AACxB,UAAM,CAAC,aAAa,OAAO,IAAI;AAE/B,WAAO;EAAK,KAAK,UAAU,WAAW,CAAC;CAAI;AAE3C,QAAI,OAAO,YAAY,YAAY,mBAAmB,YAAY;AAChE,aAAO,OAAO;IACpB,OAAW;AACL,UAAI;AACJ,UAAI;AACF,6BAAqB,KAAK,UAAU,OAAO;MACnD,SAAe,GAAG;AAIV,6BAAqB,KAAK,UAAU,UAAU,OAAO,CAAC;MAC9D;AACM,aAAO,kBAAkB;IAC/B;EACA;AAEE,SAAO,OAAO,UAAU,WAAW,QAAQ,cAAc,KAAK;AAChE;AAEA,SAAS,cAAc,SAAS;AAC9B,QAAM,cAAc,QAAQ,OAAO,CAAC,KAAK,QAAQ,MAAM,IAAI,QAAQ,CAAC;AAEpE,QAAM,SAAS,IAAI,WAAW,WAAW;AACzC,MAAI,SAAS;AACb,aAAW,UAAU,SAAS;AAC5B,WAAO,IAAI,QAAQ,MAAM;AACzB,cAAU,OAAO;EACrB;AAEE,SAAO;AACT;AAqDA,SAAS,6BAA6B,YAAY;AAChD,QAAM,SAAS,OAAO,WAAW,SAAS,WAAW,WAAW,WAAW,IAAI,IAAI,WAAW;AAE9F,SAAO;IACL;MACE,MAAM;MACN,QAAQ,OAAO;MACf,UAAU,WAAW;MACrB,cAAc,WAAW;MACzB,iBAAiB,WAAW;IAClC;IACI;EACJ;AACA;AAEA,IAAM,iCAAiC;EACrC,SAAS;EACT,UAAU;EACV,YAAY;EACZ,aAAa;EACb,OAAO;EACP,eAAe;EACf,aAAa;EACb,SAAS;EACT,eAAe;EACf,cAAc;EACd,kBAAkB;EAClB,UAAU;EACV,UAAU;EACV,MAAM;EACN,cAAc;EACd,KAAK;AACP;AAKA,SAAS,+BAA+B,MAAM;AAC5C,SAAO,+BAA+B,IAAI;AAC5C;AAGA,SAAS,gCAAgC,iBAAiB;AACxD,MAAI,EAAC,mDAAiB,MAAK;AACzB;EACJ;AACE,QAAM,EAAE,MAAM,QAAO,IAAK,gBAAgB;AAC1C,SAAO,EAAE,MAAM,QAAO;AACxB;AAMA,SAAS,2BACP,OACA,SACA,QACA,KACA;;AACA,QAAM,0BAAyB,WAAM,0BAAN,mBAA6B;AAC5D,SAAO;IACL,UAAU,MAAM;IAChB,UAAS,oBAAI,KAAI,GAAG,YAAW;IAC/B,GAAI,WAAW,EAAE,KAAK,QAAO;IAC7B,GAAI,CAAC,CAAC,UAAU,OAAO,EAAE,KAAK,YAAY,GAAG,EAAC;IAC9C,GAAI,0BAA0B;MAC5B,OAAO;IACb;EACA;AACA;AC1OA,SAAS,wBAAwB,OAAO,SAAS;AAC/C,MAAI,CAAC,SAAS;AACZ,WAAO;EACX;AACE,QAAM,MAAM,MAAM,OAAO,CAAA;AACzB,QAAM,IAAI,OAAO,MAAM,IAAI,QAAQ,QAAQ;AAC3C,QAAM,IAAI,UAAU,MAAM,IAAI,WAAW,QAAQ;AACjD,QAAM,IAAI,eAAe,CAAC,GAAI,MAAM,IAAI,gBAAgB,CAAA,GAAK,GAAI,QAAQ,gBAAgB,CAAA,CAAG;AAC5F,QAAM,IAAI,WAAW,CAAC,GAAI,MAAM,IAAI,YAAY,CAAA,GAAK,GAAI,QAAQ,YAAY,CAAA,CAAG;AAChF,SAAO;AACT;AAGA,SAAS,sBACP,SACA,KACA,UACA,QACA;AACA,QAAM,UAAU,gCAAgC,QAAQ;AACxD,QAAM,kBAAkB;IACtB,UAAS,oBAAI,KAAI,GAAG,YAAW;IAC/B,GAAI,WAAW,EAAE,KAAK,QAAO;IAC7B,GAAI,CAAC,CAAC,UAAU,OAAO,EAAE,KAAK,YAAY,GAAG,EAAC;EAClD;AAEE,QAAM,eACJ,gBAAgB,UAAU,CAAC,EAAE,MAAM,WAAU,GAAI,OAAO,IAAI,CAAC,EAAE,MAAM,UAAS,GAAI,QAAQ,OAAM,CAAE;AAEpG,SAAO,eAAe,iBAAiB,CAAC,YAAY,CAAC;AACvD;AAKA,SAAS,oBACP,OACA,KACA,UACA,QACA;AACA,QAAM,UAAU,gCAAgC,QAAQ;AASxD,QAAM,YAAY,MAAM,QAAQ,MAAM,SAAS,iBAAiB,MAAM,OAAO;AAE7E,0BAAwB,OAAO,qCAAU,GAAG;AAE5C,QAAM,kBAAkB,2BAA2B,OAAO,SAAS,QAAQ,GAAG;AAM9E,SAAO,MAAM;AAEb,QAAM,YAAY,CAAC,EAAE,MAAM,UAAS,GAAI,KAAK;AAC7C,SAAO,eAAe,iBAAiB,CAAC,SAAS,CAAC;AACpD;ACzEA,IAAM,qBAAqB;AAG3B,SAAS,mBAAmB,KAAK;AAC/B,QAAM,WAAW,IAAI,WAAW,GAAG,IAAI,QAAQ,MAAM;AACrD,QAAM,OAAO,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK;AACzC,SAAO,GAAG,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,EAAE;AACzE;AAGA,SAAS,mBAAmB,KAAK;AAC/B,SAAO,GAAG,mBAAmB,GAAG,CAAC,GAAG,IAAI,SAAS;AACnD;AAGA,SAAS,aAAa,KAAK,SAAS;AAClC,QAAM,SAAS;IACb,gBAAgB;EACpB;AAEE,MAAI,IAAI,WAAW;AAGjB,WAAO,aAAa,IAAI;EAC5B;AAEE,MAAI,SAAS;AACX,WAAO,gBAAgB,GAAG,QAAQ,IAAI,IAAI,QAAQ,OAAO;EAC7D;AAEE,SAAO,IAAI,gBAAgB,MAAM,EAAE,SAAQ;AAC7C;AAOA,SAAS,sCAAsC,KAAK,QAAQ,SAAS;AACnE,SAAO,SAAS,SAAS,GAAG,mBAAmB,GAAG,CAAC,IAAI,aAAa,KAAK,OAAO,CAAC;AACnF;ACtCA,IAAM,wBAAwB,CAAA;AAU9B,SAAS,iBAAiB,cAAc;AACtC,QAAM,qBAAqB,CAAA;AAE3B,eAAa,QAAQ,CAAC,oBAAoB;AACxC,UAAM,EAAE,KAAI,IAAK;AAEjB,UAAM,mBAAmB,mBAAmB,IAAI;AAIhD,QAAI,oBAAoB,CAAC,iBAAiB,qBAAqB,gBAAgB,mBAAmB;AAChG;IACN;AAEI,uBAAmB,IAAI,IAAI;EAC/B,CAAG;AAED,SAAO,OAAO,OAAO,kBAAkB;AACzC;AAGA,SAAS,uBAAuB,SAAS;AACvC,QAAM,sBAAsB,QAAQ,uBAAuB,CAAA;AAC3D,QAAM,mBAAmB,QAAQ;AAGjC,sBAAoB,QAAQ,CAAC,gBAAgB;AAC3C,gBAAY,oBAAoB;EACpC,CAAG;AAED,MAAI;AAEJ,MAAI,MAAM,QAAQ,gBAAgB,GAAG;AACnC,mBAAe,CAAC,GAAG,qBAAqB,GAAG,gBAAgB;EAC/D,WAAa,OAAO,qBAAqB,YAAY;AACjD,UAAM,2BAA2B,iBAAiB,mBAAmB;AACrE,mBAAe,MAAM,QAAQ,wBAAwB,IAAI,2BAA2B,CAAC,wBAAwB;EACjH,OAAS;AACL,mBAAe;EACnB;AAEE,SAAO,iBAAiB,YAAY;AACtC;AAQA,SAAS,kBAAkB,QAAQ,cAAc;AAC/C,QAAM,mBAAmB,CAAA;AAEzB,eAAa,QAAQ,CAAC,gBAAgB;AAEpC,QAAI,aAAa;AACf,uBAAiB,QAAQ,aAAa,gBAAgB;IAC5D;EACA,CAAG;AAED,SAAO;AACT;AAKA,SAAS,uBAAuB,QAAQ,cAAc;AACpD,aAAW,eAAe,cAAc;AAEtC,QAAI,2CAAa,eAAe;AAC9B,kBAAY,cAAc,MAAM;IACtC;EACA;AACA;AAGA,SAAS,iBAAiB,QAAQ,aAAa,kBAAkB;AAC/D,MAAI,iBAAiB,YAAY,IAAI,GAAG;AACtCA,mBAAe,OAAO,IAAI,yDAAyD,YAAY,IAAI,EAAE;AACrG;EACJ;AACE,mBAAiB,YAAY,IAAI,IAAI;AAGrC,MAAI,sBAAsB,QAAQ,YAAY,IAAI,MAAM,MAAM,OAAO,YAAY,cAAc,YAAY;AACzG,gBAAY,UAAS;AACrB,0BAAsB,KAAK,YAAY,IAAI;EAC/C;AAGE,MAAI,YAAY,SAAS,OAAO,YAAY,UAAU,YAAY;AAChE,gBAAY,MAAM,MAAM;EAC5B;AAEE,MAAI,OAAO,YAAY,oBAAoB,YAAY;AACrD,UAAM,WAAW,YAAY,gBAAgB,KAAK,WAAW;AAC7D,WAAO,GAAG,mBAAmB,CAAC,OAAO,SAAS,SAAS,OAAO,MAAM,MAAM,CAAC;EAC/E;AAEE,MAAI,OAAO,YAAY,iBAAiB,YAAY;AAClD,UAAM,WAAW,YAAY,aAAa,KAAK,WAAW;AAE1D,UAAM,YAAY,OAAO,OAAO,CAAC,OAAO,SAAS,SAAS,OAAO,MAAM,MAAM,GAAG;MAC9E,IAAI,YAAY;IACtB,CAAK;AAED,WAAO,kBAAkB,SAAS;EACtC;AAEEA,iBAAe,OAAO,IAAI,0BAA0B,YAAY,IAAI,EAAE;AACxE;AAkBA,SAAS,kBAAkB,IAAI;AAC7B,SAAO;AACT;AC7IA,SAAS,yBAAyB,OAAO;AACvC,QAAM,mBAAmB,CAAA;AAEzB,MAAI,MAAM,SAAS;AACjB,qBAAiB,KAAK,MAAM,OAAO;EACvC;AAEE,MAAI;AAEF,UAAM,gBAAgB,MAAM,UAAU,OAAO,MAAM,UAAU,OAAO,SAAS,CAAC;AAC9E,QAAI,+CAAe,OAAO;AACxB,uBAAiB,KAAK,cAAc,KAAK;AACzC,UAAI,cAAc,MAAM;AACtB,yBAAiB,KAAK,GAAG,cAAc,IAAI,KAAK,cAAc,KAAK,EAAE;MAC7E;IACA;EACA,SAAW,GAAG;EAEd;AAEE,SAAO;AACT;ACnBA,SAAS,kCAAkC,OAAO;;AAChD,QAAM,EAAE,UAAU,gBAAgB,SAAS,QAAQ,QAAQ,MAAM,GAAE,MAAK,WAAM,aAAN,mBAAgB,UAAS,CAAA;AAEjG,SAAO;IACL,MAAM,QAAQ,CAAA;IACd,aAAa,MAAM;IACnB;IACA;IACA,SAAS,WAAW;IACpB,iBAAiB,MAAM,mBAAmB;IAC1C;IACA,WAAW,MAAM;IACjB,UAAU,YAAY;IACtB;IACA,YAAY,6BAAO;IACnB,gBAAgB,6BAAO;IACvB,cAAc,MAAM;IACpB,YAAY;EAChB;AACA;AAKA,SAAS,kCAAkC,MAAM;AAC/C,SAAO;IACL,MAAM;IACN,WAAW,KAAK;IAChB,iBAAiB,KAAK;IACtB,aAAa,KAAK;IAClB,UAAU;MACR,OAAO;QACL,UAAU,KAAK;QACf,SAAS,KAAK;QACd,gBAAgB,KAAK;QACrB,IAAI,KAAK;QACT,QAAQ,KAAK;QACb,QAAQ,KAAK;QACb,MAAM;UACJ,GAAG,KAAK;UACR,GAAI,KAAK,cAAc,EAAE,CAAC,6BAA6B,GAAG,KAAK,WAAU;UACzE,GAAI,KAAK,kBAAkB,EAAE,CAAC,iCAAiC,GAAG,KAAK,eAAc;QAC/F;MACA;IACA;IACI,cAAc,KAAK;EACvB;AACA;AC5CA,SAAS,2BACP,kBACA,KACA,WACA;AACA,QAAM,mBAAmB;IACvB,EAAE,MAAM,gBAAe;IACvB;MACE,WAAwB,uBAAsB;MAC9C;IACN;EACA;AACE,SAAO,eAAe,MAAM,EAAE,IAAG,IAAK,CAAA,GAAI,CAAC,gBAAgB,CAAC;AAC9D;ACIA,IAAM,qBAAqB;AAC3B,IAAM,oCAAoC;AAE1C,IAAM,wBAAwB,OAAO,IAAI,qBAAqB;AAC9D,IAAM,2BAA2B,OAAO,IAAI,2BAA2B;AAEvE,SAAS,mBAAmB,SAAS;AACnC,SAAO;IACL;IACA,CAAC,qBAAqB,GAAG;EAC7B;AACA;AAEA,SAAS,yBAAyB,SAAS;AACzC,SAAO;IACL;IACA,CAAC,wBAAwB,GAAG;EAChC;AACA;AAEA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,CAAC,CAAC,SAAS,OAAO,UAAU,YAAY,yBAAyB;AAC1E;AAEA,SAAS,uBAAuB,OAAO;AACrC,SAAO,CAAC,CAAC,SAAS,OAAO,UAAU,YAAY,4BAA4B;AAC7E;AAiCA,IAAM,SAAN,MAAa;;;;;;;;;;;;EAkBV,YAAY,SAAS;AACpB,SAAK,WAAW;AAChB,SAAK,gBAAgB,CAAA;AACrB,SAAK,iBAAiB;AACtB,SAAK,YAAY,CAAA;AACjB,SAAK,SAAS,CAAA;AACd,SAAK,mBAAmB,CAAA;AAExB,QAAI,QAAQ,KAAK;AACf,WAAK,OAAO,QAAQ,QAAQ,GAAG;IACrC,OAAW;AACLA,qBAAe,OAAO,KAAK,+CAA+C;IAChF;AAEI,QAAI,KAAK,MAAM;AACb,YAAM,MAAM;QACV,KAAK;QACL,QAAQ;QACR,QAAQ,YAAY,QAAQ,UAAU,MAAM;MACpD;AACM,WAAK,aAAa,QAAQ,UAAU;QAClC,QAAQ,KAAK,SAAS;QACtB,oBAAoB,KAAK,mBAAmB,KAAK,IAAI;QACrD,GAAG,QAAQ;QACX;MACR,CAAO;IACP;EACA;;;;;;EAOG,iBAAiB,WAAW,MAAM,OAAO;AACxC,UAAM,UAAU,MAAK;AAGrB,QAAI,wBAAwB,SAAS,GAAG;AACtCA,qBAAe,OAAO,IAAI,kBAAkB;AAC5C,aAAO;IACb;AAEI,UAAM,kBAAkB;MACtB,UAAU;MACV,GAAG;IACT;AAEI,SAAK;MACH,KAAK,mBAAmB,WAAW,eAAe,EAAE;QAAK,WACvD,KAAK,cAAc,OAAO,iBAAiB,KAAK;MACxD;IACA;AAEI,WAAO,gBAAgB;EAC3B;;;;;;EAOG,eACC,SACA,OACA,MACA,cACA;AACA,UAAM,kBAAkB;MACtB,UAAU,MAAK;MACf,GAAG;IACT;AAEI,UAAM,eAAe,sBAAsB,OAAO,IAAI,UAAU,OAAO,OAAO;AAE9E,UAAM,gBAAgB,YAAY,OAAO,IACrC,KAAK,iBAAiB,cAAc,OAAO,eAAe,IAC1D,KAAK,mBAAmB,SAAS,eAAe;AAEpD,SAAK,SAAS,cAAc,KAAK,WAAS,KAAK,cAAc,OAAO,iBAAiB,YAAY,CAAC,CAAC;AAEnG,WAAO,gBAAgB;EAC3B;;;;;;EAOG,aAAa,OAAO,MAAM,cAAc;AACvC,UAAM,UAAU,MAAK;AAGrB,SAAI,6BAAM,sBAAqB,wBAAwB,KAAK,iBAAiB,GAAG;AAC9EA,qBAAe,OAAO,IAAI,kBAAkB;AAC5C,aAAO;IACb;AAEI,UAAM,kBAAkB;MACtB,UAAU;MACV,GAAG;IACT;AAEI,UAAM,wBAAwB,MAAM,yBAAyB,CAAA;AAC7D,UAAM,oBAAoB,sBAAsB;AAChD,UAAM,6BAA6B,sBAAsB;AAEzD,SAAK;MACH,KAAK,cAAc,OAAO,iBAAiB,qBAAqB,cAAc,0BAA0B;IAC9G;AAEI,WAAO,gBAAgB;EAC3B;;;;EAKG,eAAe,SAAS;AACvB,SAAK,YAAY,OAAO;AAExB,kBAAc,SAAS,EAAE,MAAM,MAAK,CAAE;EAC1C;;;;;;;;;;;;;EAeG,SAAS;AACR,WAAO,KAAK;EAChB;;;;EAKG,aAAa;AACZ,WAAO,KAAK;EAChB;;;;;EAMG,iBAAiB;AAChB,WAAO,KAAK,SAAS;EACzB;;;;;EAMG,eAAe;AACd,WAAO,KAAK;EAChB;;;;;;;;;EAUG,MAAM,SAAS;AACd,UAAM,YAAY,KAAK;AACvB,QAAI,WAAW;AACb,WAAK,KAAK,OAAO;AACjB,aAAO,KAAK,wBAAwB,OAAO,EAAE,KAAK,oBAAkB;AAClE,eAAO,UAAU,MAAM,OAAO,EAAE,KAAK,sBAAoB,kBAAkB,gBAAgB;MACnG,CAAO;IACP,OAAW;AACL,aAAO,oBAAoB,IAAI;IACrC;EACA;;;;;;;;;EAUG,MAAM,SAAS;AACd,WAAO,KAAK,MAAM,OAAO,EAAE,KAAK,YAAU;AACxC,WAAK,WAAU,EAAG,UAAU;AAC5B,WAAK,KAAK,OAAO;AACjB,aAAO;IACb,CAAK;EACL;;;;EAKG,qBAAqB;AACpB,WAAO,KAAK;EAChB;;;;EAKG,kBAAkB,gBAAgB;AACjC,SAAK,iBAAiB,KAAK,cAAc;EAC7C;;;;;EAMG,OAAO;AACN,QACE,KAAK,WAAU;;;;;IAMf,KAAK,SAAS,aAAa,KAAK,CAAC,EAAE,KAAI,MAAO,KAAK,WAAW,WAAW,CAAC,GAC1E;AACA,WAAK,mBAAkB;IAC7B;EACA;;;;;;EAOG,qBAAqB,iBAAiB;AACrC,WAAO,KAAK,cAAc,eAAe;EAC7C;;;;;;;;EASG,eAAe,aAAa;AAC3B,UAAM,qBAAqB,KAAK,cAAc,YAAY,IAAI;AAG9D,qBAAiB,MAAM,aAAa,KAAK,aAAa;AAEtD,QAAI,CAAC,oBAAoB;AACvB,6BAAuB,MAAM,CAAC,WAAW,CAAC;IAChD;EACA;;;;EAKG,UAAU,OAAO,OAAO,CAAA,GAAI;AAC3B,SAAK,KAAK,mBAAmB,OAAO,IAAI;AAExC,QAAI,MAAM,oBAAoB,OAAO,KAAK,MAAM,KAAK,SAAS,WAAW,KAAK,SAAS,MAAM;AAE7F,eAAW,cAAc,KAAK,eAAe,CAAA,GAAI;AAC/C,YAAM,kBAAkB,KAAK,6BAA6B,UAAU,CAAC;IAC3E;AAEI,UAAM,UAAU,KAAK,aAAa,GAAG;AACrC,QAAI,SAAS;AACX,cAAQ,KAAK,kBAAgB,KAAK,KAAK,kBAAkB,OAAO,YAAY,GAAG,IAAI;IACzF;EACA;;;;EAKG,YAAY,SAAS;AAEpB,UAAM,EAAE,SAAS,qBAAqB,aAAa,0BAA0B,oBAAmB,IAAK,KAAK;AAC1G,QAAI,gBAAgB,SAAS;AAC3B,YAAM,eAAe,QAAQ,SAAS,CAAA;AACtC,UAAI,CAAC,aAAa,WAAW,CAAC,qBAAqB;AACjDA,uBAAe,OAAO,KAAK,iCAAiC;AAC5D;MACR;AACM,mBAAa,UAAU,aAAa,WAAW;AAC/C,mBAAa,cAAc,aAAa,eAAe;AACvD,cAAQ,QAAQ;IACtB,OAAW;AACL,UAAI,CAAC,QAAQ,WAAW,CAAC,qBAAqB;AAC5CA,uBAAe,OAAO,KAAK,iCAAiC;AAC5D;MACR;AACM,cAAQ,UAAU,QAAQ,WAAW;AACrC,cAAQ,cAAc,QAAQ,eAAe;IACnD;AAEI,SAAK,KAAK,qBAAqB,OAAO;AAEtC,UAAM,MAAM,sBAAsB,SAAS,KAAK,MAAM,KAAK,SAAS,WAAW,KAAK,SAAS,MAAM;AAInG,SAAK,aAAa,GAAG;EACzB;;;;EAKG,mBAAmB,QAAQ,UAAU,QAAQ,GAAG;AAC/C,QAAI,KAAK,SAAS,mBAAmB;AAOnC,YAAM,MAAM,GAAG,MAAM,IAAI,QAAQ;AACjCA,qBAAe,OAAO,IAAI,uBAAuB,GAAG,IAAI,QAAQ,IAAI,KAAK,KAAK,YAAY,EAAE,EAAE;AAC9F,WAAK,UAAU,GAAG,KAAK,KAAK,UAAU,GAAG,KAAK,KAAK;IACzD;EACA;;;;;;;;;;EAYG,GAAG,MAAM,UAAU;AAClB,UAAM,QAAS,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,IAAI,KAAK,CAAA;AAGxD,UAAM,KAAK,QAAQ;AAMnB,WAAO,MAAM;AAEX,YAAM,UAAU,MAAM,QAAQ,QAAQ;AACtC,UAAI,UAAU,IAAI;AAChB,cAAM,OAAO,SAAS,CAAC;MAC/B;IACA;EACA;;;;;EAOG,KAAK,SAAS,MAAM;AACnB,UAAM,YAAY,KAAK,OAAO,IAAI;AAClC,QAAI,WAAW;AACb,gBAAU,QAAQ,cAAY,SAAS,GAAG,IAAI,CAAC;IACrD;EACA;;;;EAKG,aAAa,UAAU;AACtB,SAAK,KAAK,kBAAkB,QAAQ;AAEpC,QAAI,KAAK,WAAU,KAAM,KAAK,YAAY;AACxC,aAAO,KAAK,WAAW,KAAK,QAAQ,EAAE,KAAK,MAAM,YAAU;AACzDA,uBAAe,OAAO,MAAM,iCAAiC,MAAM;AACnE,eAAO;MACf,CAAO;IACP;AAEIA,mBAAe,OAAO,MAAM,oBAAoB;AAEhD,WAAO,oBAAoB,CAAA,CAAE;EACjC;;;EAKG,qBAAqB;AACpB,UAAM,EAAE,aAAY,IAAK,KAAK;AAC9B,SAAK,gBAAgB,kBAAkB,MAAM,YAAY;AACzD,2BAAuB,MAAM,YAAY;EAC7C;;EAGG,wBAAwB,SAAS,OAAO;;AACvC,QAAI,UAAU,MAAM,UAAU;AAC9B,QAAI,UAAU;AACd,UAAM,cAAa,WAAM,cAAN,mBAAiB;AAEpC,QAAI,YAAY;AACd,gBAAU;AAEV,iBAAW,MAAM,YAAY;AAC3B,cAAM,YAAY,GAAG;AACrB,aAAI,uCAAW,aAAY,OAAO;AAChC,oBAAU;AACV;QACV;MACA;IACA;AAKI,UAAM,qBAAqB,QAAQ,WAAW;AAC9C,UAAM,sBAAuB,sBAAsB,QAAQ,WAAW,KAAO,sBAAsB;AAEnG,QAAI,qBAAqB;AACvB,oBAAc,SAAS;QACrB,GAAI,WAAW,EAAE,QAAQ,UAAS;QAClC,QAAQ,QAAQ,UAAU,OAAO,WAAW,OAAO;MAC3D,CAAO;AACD,WAAK,eAAe,OAAO;IACjC;EACA;;;;;;;;;;;EAYG,wBAAwB,SAAS;AAChC,WAAO,IAAI,YAAY,aAAW;AAChC,UAAI,SAAS;AACb,YAAM,OAAO;AAEb,YAAM,WAAW,YAAY,MAAM;AACjC,YAAI,KAAK,kBAAkB,GAAG;AAC5B,wBAAc,QAAQ;AACtB,kBAAQ,IAAI;QACtB,OAAe;AACL,oBAAU;AACV,cAAI,WAAW,UAAU,SAAS;AAChC,0BAAc,QAAQ;AACtB,oBAAQ,KAAK;UACzB;QACA;MACA,GAAS,IAAI;IACb,CAAK;EACL;;EAGG,aAAa;AACZ,WAAO,KAAK,WAAU,EAAG,YAAY,SAAS,KAAK,eAAe;EACtE;;;;;;;;;;;;;;;EAgBG,cACC,OACA,MACA,cACA,gBACA;AACA,UAAM,UAAU,KAAK,WAAU;AAC/B,UAAM,eAAe,OAAO,KAAK,KAAK,aAAa;AACnD,QAAI,CAAC,KAAK,iBAAgB,6CAAc,SAAQ;AAC9C,WAAK,eAAe;IAC1B;AAEI,SAAK,KAAK,mBAAmB,OAAO,IAAI;AAExC,QAAI,CAAC,MAAM,MAAM;AACf,qBAAe,eAAe,MAAM,YAAY,KAAK,QAAQ;IACnE;AAEI,WAAO,aAAa,SAAS,OAAO,MAAM,cAAc,MAAM,cAAc,EAAE,KAAK,SAAO;AACxF,UAAI,QAAQ,MAAM;AAChB,eAAO;MACf;AAEM,WAAK,KAAK,oBAAoB,KAAK,IAAI;AAEvC,UAAI,WAAW;QACb,OAAO,yBAAyB,YAAY;QAC5C,GAAG,IAAI;MACf;AAEM,YAAM,yBAAyB,mCAAmC,MAAM,YAAY;AAEpF,UAAI,wBAAwB;QAC1B;QACA,GAAG,IAAI;MACf;AAEM,aAAO;IACb,CAAK;EACL;;;;;;;EAQG,cACC,OACA,OAAO,CAAA,GACP,eAAe,gBAAe,GAC9B,iBAAiB,kBAAiB,GAClC;AACA,QAAIA,eAAeC,cAAa,KAAK,GAAG;AACtC,aAAO,IAAI,0BAA0B,yBAAyB,KAAK,EAAE,CAAC,KAAK,WAAW,IAAI;IAChG;AAEI,WAAO,KAAK,cAAc,OAAO,MAAM,cAAc,cAAc,EAAE;MACnE,gBAAc;AACZ,eAAO,WAAW;MAC1B;MACM,YAAU;AACR,YAAID,aAAa;AACf,cAAI,uBAAuB,MAAM,GAAG;AAClC,mBAAO,IAAI,OAAO,OAAO;UACrC,WAAqB,iBAAiB,MAAM,GAAG;AACnC,mBAAO,KAAK,OAAO,OAAO;UACtC,OAAiB;AACL,mBAAO,KAAK,MAAM;UAC9B;QACA;AACQ,eAAO;MACf;IACA;EACA;;;;;;;;;;;;;;EAeG,cACC,OACA,MACA,cACA,gBACA;AACA,UAAM,UAAU,KAAK,WAAU;AAC/B,UAAM,EAAE,WAAU,IAAK;AAEvB,UAAM,gBAAgB,mBAAmB,KAAK;AAC9C,UAAME,WAAUD,cAAa,KAAK;AAClC,UAAM,YAAY,MAAM,QAAQ;AAChC,UAAM,kBAAkB,0BAA0B,SAAS;AAK3D,UAAM,mBAAmB,OAAO,eAAe,cAAc,SAAY,gBAAgB,UAAU;AACnG,QAAIC,YAAW,OAAO,qBAAqB,YAAY,KAAK,OAAM,IAAK,kBAAkB;AACvF,WAAK,mBAAmB,eAAe,OAAO;AAC9C,aAAO;QACL;UACE,oFAAoF,UAAU;QACxG;MACA;IACA;AAEI,UAAM,eAAgB,cAAc,iBAAiB,WAAW;AAEhE,WAAO,KAAK,cAAc,OAAO,MAAM,cAAc,cAAc,EAChE,KAAK,cAAY;AAChB,UAAI,aAAa,MAAM;AACrB,aAAK,mBAAmB,mBAAmB,YAAY;AACvD,cAAM,yBAAyB,0DAA0D;MACnG;AAEQ,YAAM,sBAAsB,KAAK,QAAS,KAAK,KAAO,eAAe;AACrE,UAAI,qBAAqB;AACvB,eAAO;MACjB;AAEQ,YAAM,SAAS,kBAAkB,MAAM,SAAS,UAAU,IAAI;AAC9D,aAAO,0BAA0B,QAAQ,eAAe;IAChE,CAAO,EACA,KAAK,oBAAkB;;AACtB,UAAI,mBAAmB,MAAM;AAC3B,aAAK,mBAAmB,eAAe,YAAY;AACnD,YAAI,eAAe;AACjB,gBAAM,QAAQ,MAAM,SAAS,CAAA;AAE7B,gBAAM,YAAY,IAAI,MAAM;AAC5B,eAAK,mBAAmB,eAAe,QAAQ,SAAS;QACpE;AACU,cAAM,yBAAyB,GAAG,eAAe,0CAA0C;MACrG;AAEQ,YAAM,UAAU,aAAa,WAAU,KAAM,eAAe,WAAU;AACtE,UAAIA,YAAW,SAAS;AACtB,aAAK,wBAAwB,SAAS,cAAc;MAC9D;AAEQ,UAAI,eAAe;AACjB,cAAM,oBAAkB,oBAAe,0BAAf,mBAAsC,8BAA6B;AAC3F,cAAM,iBAAiB,eAAe,QAAQ,eAAe,MAAM,SAAS;AAE5E,cAAM,mBAAmB,kBAAkB;AAC3C,YAAI,mBAAmB,GAAG;AACxB,eAAK,mBAAmB,eAAe,QAAQ,gBAAgB;QAC3E;MACA;AAKQ,YAAM,kBAAkB,eAAe;AACvC,UAAI,iBAAiB,mBAAmB,eAAe,gBAAgB,MAAM,aAAa;AACxF,cAAM,SAAS;AACf,uBAAe,mBAAmB;UAChC,GAAG;UACH;QACZ;MACA;AAEQ,WAAK,UAAU,gBAAgB,IAAI;AACnC,aAAO;IACf,CAAO,EACA,KAAK,MAAM,YAAU;AACpB,UAAI,uBAAuB,MAAM,KAAK,iBAAiB,MAAM,GAAG;AAC9D,cAAM;MAChB;AAEQ,WAAK,iBAAiB,QAAQ;QAC5B,MAAM;UACJ,YAAY;QACxB;QACU,mBAAmB;MAC7B,CAAS;AACD,YAAM;QACJ;UAA8H,MAAM;MAC9I;IACA,CAAO;EACP;;;;EAKG,SAAS,SAAS;AACjB,SAAK;AACL,SAAK,QAAQ;MACX,WAAS;AACP,aAAK;AACL,eAAO;MACf;MACM,YAAU;AACR,aAAK;AACL,eAAO;MACf;IACA;EACA;;;;EAKG,iBAAiB;AAChB,UAAM,WAAW,KAAK;AACtB,SAAK,YAAY,CAAA;AACjB,WAAO,OAAO,QAAQ,QAAQ,EAAE,IAAI,CAAC,CAAC,KAAK,QAAQ,MAAM;AACvD,YAAM,CAAC,QAAQ,QAAQ,IAAI,IAAI,MAAM,GAAG;AACxC,aAAO;QACL;QACA;QACA;MACR;IACA,CAAK;EACL;;;;EAKG,iBAAiB;AAChBF,mBAAe,OAAO,IAAI,sBAAsB;AAEhD,UAAM,WAAW,KAAK,eAAc;AAEpC,QAAI,SAAS,WAAW,GAAG;AACzBA,qBAAe,OAAO,IAAI,qBAAqB;AAC/C;IACN;AAGI,QAAI,CAAC,KAAK,MAAM;AACdA,qBAAe,OAAO,IAAI,yCAAyC;AACnE;IACN;AAEIA,mBAAe,OAAO,IAAI,qBAAqB,QAAQ;AAEvD,UAAM,WAAW,2BAA2B,UAAU,KAAK,SAAS,UAAU,YAAY,KAAK,IAAI,CAAC;AAIpG,SAAK,aAAa,QAAQ;EAC9B;;;;AAMA;AAgBA,SAAS,0BACP,kBACA,iBACA;AACA,QAAM,oBAAoB,GAAG,eAAe;AAC5C,MAAI,WAAW,gBAAgB,GAAG;AAChC,WAAO,iBAAiB;MACtB,WAAS;AACP,YAAI,CAAC,cAAc,KAAK,KAAK,UAAU,MAAM;AAC3C,gBAAM,mBAAmB,iBAAiB;QACpD;AACQ,eAAO;MACf;MACM,OAAK;AACH,cAAM,mBAAmB,GAAG,eAAe,kBAAkB,CAAC,EAAE;MACxE;IACA;EACA,WAAa,CAAC,cAAc,gBAAgB,KAAK,qBAAqB,MAAM;AACxE,UAAM,mBAAmB,iBAAiB;EAC9C;AACE,SAAO;AACT;AAKA,SAAS,kBACP,QACA,SACA,OACA,MACA;AACA,QAAM,EAAE,YAAY,uBAAuB,eAAc,IAAK;AAC9D,MAAI,iBAAiB;AAErB,MAAIC,cAAa,cAAc,KAAK,YAAY;AAC9C,WAAO,WAAW,gBAAgB,IAAI;EAC1C;AAEE,MAAI,mBAAmB,cAAc,GAAG;AACtC,QAAI,gBAAgB;AAElB,YAAM,wBAAwB,eAAe,kCAAkC,cAAc,CAAC;AAC9F,UAAI,CAAC,uBAAuB;AAC1B,4BAAmB;MAC3B,OAAa;AAEL,yBAAiB,MAAM,OAAO,kCAAkC,qBAAqB,CAAC;MAC9F;AAGM,UAAI,eAAe,OAAO;AACxB,cAAM,iBAAiB,CAAA;AACvB,mBAAW,QAAQ,eAAe,OAAO;AACvC,gBAAM,gBAAgB,eAAe,IAAI;AACzC,cAAI,CAAC,eAAe;AAClB,gCAAmB;AACnB,2BAAe,KAAK,IAAI;UACpC,OAAiB;AACL,2BAAe,KAAK,aAAa;UAC7C;QACA;AACQ,uBAAe,QAAQ;MAC/B;IACA;AAEI,QAAI,uBAAuB;AACzB,UAAI,eAAe,OAAO;AAGxB,cAAM,kBAAkB,eAAe,MAAM;AAC7C,uBAAe,wBAAwB;UACrC,GAAG,MAAM;UACT,2BAA2B;QACrC;MACA;AACM,aAAO,sBAAsB,gBAAiB,IAAI;IACxD;EACA;AAEE,SAAO;AACT;AAEA,SAASA,cAAa,OAAO;AAC3B,SAAO,MAAM,SAAS;AACxB;AAEA,SAAS,mBAAmB,OAAO;AACjC,SAAO,MAAM,SAAS;AACxB;ACn6BA,SAAS,+BAA+B,OAAO;AAC7C,SAAO;IACL;MACE,MAAM;MACN,YAAY,MAAM;MAClB,cAAc;IACpB;IACI;MACE;IACN;EACA;AACA;AAaA,SAAS,kBACP,MACA,UACA,QACA,KACA;AACA,QAAM,UAAU,CAAA;AAEhB,MAAI,qCAAU,KAAK;AACjB,YAAQ,MAAM;MACZ,MAAM,SAAS,IAAI;MACnB,SAAS,SAAS,IAAI;IAC5B;EACA;AAEE,MAAI,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK;AACrB,YAAQ,MAAM,YAAY,GAAG;EACjC;AAEE,SAAO,eAAe,SAAS,CAAC,+BAA+B,IAAI,CAAC,CAAC;AACvE;ACvCA,WAAW,8BAA8B,oBAAI,QAAO;AA+JpD,SAAS,0BAA0B,QAAQ,gBAAgB;;AACzD,QAAM,YAA8B,uBAAuB,MAAM,KAAK,CAAA;AACtE,MAAI,UAAU,WAAW,GAAG;AAC1B;EACJ;AAEE,QAAM,gBAAgB,OAAO,WAAU;AACvC,QAAM,WAAW,kBAAkB,WAAW,cAAc,WAAW,cAAc,QAAQ,OAAO,OAAM,CAAE;AAG5G,mBAAW,gCAAX,mBAAwC,IAAI,QAAQ,CAAA;AAEpD,SAAO,KAAK,WAAW;AAIvB,SAAO,aAAa,QAAQ;AAC9B;AAUA,SAAS,uBAAuB,QAAQ;;AACtC,UAAO,gBAAW,gCAAX,mBAAwC,IAAI;AACrD;AC7LA,SAAS,YACP,aACA,SACA;AACA,MAAI,QAAQ,UAAU,MAAM;AAC1B,QAAID,aAAa;AACf,aAAO,OAAM;IACnB,OAAW;AAEL,qBAAe,MAAM;AAEnB,gBAAQ,KAAK,8EAA8E;MACnG,CAAO;IACP;EACA;AACE,QAAM,QAAQ,gBAAe;AAC7B,QAAM,OAAO,QAAQ,YAAY;AAEjC,QAAM,SAAS,IAAI,YAAY,OAAO;AACtC,mBAAiB,MAAM;AACvB,SAAO,KAAI;AACX,SAAO;AACT;AAKA,SAAS,iBAAiB,QAAQ;AAChC,kBAAe,EAAG,UAAU,MAAM;AACpC;ACxCA,IAAM,2BAA2B,OAAO,IAAI,uBAAuB;AAMnE,SAAS,kBAAkB,OAAO;AAChC,QAAM,SAAS,CAAA;AAEf,WAAS,UAAU;AACjB,WAAO,UAAU,UAAa,OAAO,SAAS;EAClD;AAQE,WAAS,OAAO,MAAM;AACpB,WAAO,OAAO,OAAO,OAAO,QAAQ,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,QAAQ,QAAQ,MAAS;EACjF;AAYE,WAAS,IAAI,cAAc;AACzB,QAAI,CAAC,QAAO,GAAI;AACd,aAAO,oBAAoB,wBAAwB;IACzD;AAGI,UAAM,OAAO,aAAY;AACzB,QAAI,OAAO,QAAQ,IAAI,MAAM,IAAI;AAC/B,aAAO,KAAK,IAAI;IACtB;AACI,SAAK,KACF,KAAK,MAAM,OAAO,IAAI,CAAC,EAIvB;MAAK;MAAM,MACV,OAAO,IAAI,EAAE,KAAK,MAAM,MAAM;MAEtC,CAAS;IACT;AACI,WAAO;EACX;AAWE,WAAS,MAAM,SAAS;AACtB,WAAO,IAAI,YAAY,CAAC,SAAS,WAAW;AAC1C,UAAI,UAAU,OAAO;AAErB,UAAI,CAAC,SAAS;AACZ,eAAO,QAAQ,IAAI;MAC3B;AAGM,YAAM,qBAAqB,WAAW,MAAM;AAC1C,YAAI,WAAW,UAAU,GAAG;AAC1B,kBAAQ,KAAK;QACvB;MACA,GAAS,OAAO;AAGV,aAAO,QAAQ,UAAQ;AACrB,aAAK,oBAAoB,IAAI,EAAE,KAAK,MAAM;AACxC,cAAI,CAAC,EAAE,SAAS;AACd,yBAAa,kBAAkB;AAC/B,oBAAQ,IAAI;UACxB;QACA,GAAW,MAAM;MACjB,CAAO;IACP,CAAK;EACL;AAEE,SAAO;IACL,GAAG;IACH;IACA;EACJ;AACA;ACjGA,IAAM,sBAAsB,KAAK;AAQjC,SAAS,sBAAsB,QAAQ,MAAM,KAAK,IAAG,GAAI;AACvD,QAAM,cAAc,SAAS,GAAG,MAAM,IAAI,EAAE;AAC5C,MAAI,CAAC,MAAM,WAAW,GAAG;AACvB,WAAO,cAAc;EACzB;AAEE,QAAM,aAAa,KAAK,MAAM,GAAG,MAAM,EAAE;AACzC,MAAI,CAAC,MAAM,UAAU,GAAG;AACtB,WAAO,aAAa;EACxB;AAEE,SAAO;AACT;AASA,SAAS,cAAc,QAAQ,cAAc;AAC3C,SAAO,OAAO,YAAY,KAAK,OAAO,OAAO;AAC/C;AAKA,SAAS,cAAc,QAAQ,cAAc,MAAM,KAAK,IAAG,GAAI;AAC7D,SAAO,cAAc,QAAQ,YAAY,IAAI;AAC/C;AAOA,SAAS,iBACP,QACA,EAAE,YAAY,QAAO,GACrB,MAAM,KAAK,IAAG,GACd;AACA,QAAM,oBAAoB;IACxB,GAAG;EACP;AAIE,QAAM,kBAAkB,mCAAU;AAClC,QAAM,mBAAmB,mCAAU;AAEnC,MAAI,iBAAiB;AAenB,eAAW,SAAS,gBAAgB,KAAI,EAAG,MAAM,GAAG,GAAG;AACrD,YAAM,CAAC,YAAY,YAAU,EAAA,EAAM,UAAU,IAAI,MAAM,MAAM,KAAK,CAAC;AACnE,YAAM,cAAc,SAAS,YAAY,EAAE;AAC3C,YAAM,SAAS,CAAC,MAAM,WAAW,IAAI,cAAc,MAAM;AACzD,UAAI,CAAC,YAAY;AACf,0BAAkB,MAAM,MAAM;MACtC,OAAa;AACL,mBAAW,YAAY,WAAW,MAAM,GAAG,GAAG;AAC5C,cAAI,aAAa,iBAAiB;AAEhC,gBAAI,CAAC,cAAc,WAAW,MAAM,GAAG,EAAE,SAAS,QAAQ,GAAG;AAC3D,gCAAkB,QAAQ,IAAI,MAAM;YAClD;UACA,OAAiB;AACL,8BAAkB,QAAQ,IAAI,MAAM;UAChD;QACA;MACA;IACA;EACA,WAAa,kBAAkB;AAC3B,sBAAkB,MAAM,MAAM,sBAAsB,kBAAkB,GAAG;EAC7E,WAAa,eAAe,KAAK;AAC7B,sBAAkB,MAAM,MAAM,KAAK;EACvC;AAEE,SAAO;AACT;AC/FA,IAAM,gCAAgC;AAQtC,SAAS,gBACP,SACA,aACA,SAAS;EACP,QAAQ,cAAc;AAC1B,GACE;AACA,MAAI,aAAa,CAAA;AACjB,QAAM,QAAQ,CAAC,YAAY,OAAO,MAAM,OAAO;AAE/C,WAAS,KAAK,UAAU;AACtB,UAAM,wBAAwB,CAAA;AAG9B,wBAAoB,UAAU,CAAC,MAAM,SAAS;AAC5C,YAAM,eAAe,+BAA+B,IAAI;AACxD,UAAI,cAAc,YAAY,YAAY,GAAG;AAC3C,gBAAQ,mBAAmB,qBAAqB,YAAY;MACpE,OAAa;AACL,8BAAsB,KAAK,IAAI;MACvC;IACA,CAAK;AAGD,QAAI,sBAAsB,WAAW,GAAG;AACtC,aAAO,oBAAoB,CAAA,CAAE;IACnC;AAEI,UAAM,mBAAmB,eAAe,SAAS,CAAC,GAAG,qBAAqB;AAG1E,UAAM,qBAAqB,CAAC,WAAW;AACrC,0BAAoB,kBAAkB,CAAC,MAAM,SAAS;AACpD,gBAAQ,mBAAmB,QAAQ,+BAA+B,IAAI,CAAC;MAC/E,CAAO;IACP;AAEI,UAAM,cAAc,MAClB,YAAY,EAAE,MAAM,kBAAkB,gBAAgB,EAAC,CAAE,EAAE;MACzD,cAAY;AAEV,YAAI,SAAS,eAAe,WAAc,SAAS,aAAa,OAAO,SAAS,cAAc,MAAM;AAClGA,yBAAe,OAAO,KAAK,qCAAqC,SAAS,UAAU,iBAAiB;QAChH;AAEU,qBAAa,iBAAiB,YAAY,QAAQ;AAClD,eAAO;MACjB;MACQ,WAAS;AACP,2BAAmB,eAAe;AAClCA,uBAAe,OAAO,MAAM,gDAAgD,KAAK;AACjF,cAAM;MAChB;IACA;AAEI,WAAO,OAAO,IAAI,WAAW,EAAE;MAC7B,YAAU;MACV,WAAS;AACP,YAAI,UAAU,0BAA0B;AACtCA,yBAAe,OAAO,MAAM,+CAA+C;AAC3E,6BAAmB,gBAAgB;AACnC,iBAAO,oBAAoB,CAAA,CAAE;QACvC,OAAe;AACL,gBAAM;QAChB;MACA;IACA;EACA;AAEE,SAAO;IACL;IACA;EACJ;AACA;ACjFA,SAAS,uBAAuB,kBAAkB;;AAChD,QAAI,sBAAiB,SAAjB,mBAAuB,gBAAe,QAAW;AACnD,qBAAiB,OAAO;MACtB,GAAG,iBAAiB;MACpB,YAAY;IAClB;EACA;AACA;AAKA,SAAS,0BAA0B,SAAS;;AAC1C,MAAI,gBAAgB,SAAS;AAC3B,UAAI,aAAQ,UAAR,mBAAgB,mBAAkB,QAAW;AAC/C,cAAQ,QAAQ;QACd,GAAG,QAAQ;QACX,YAAY;MACpB;IACA;EACA,OAAS;AACL,QAAI,QAAQ,cAAc,QAAW;AACnC,cAAQ,YAAY;IAC1B;EACA;AACA;ACjBA,SAAS,iBAAiB,SAAS,MAAM,QAAQ,CAAC,IAAI,GAAG,SAAS,OAAO;AACvE,QAAM,WAAW,QAAQ,aAAa,CAAA;AAEtC,MAAI,CAAC,SAAS,KAAK;AACjB,aAAS,MAAM;MACb,MAAM,qBAAqB,IAAI;MAC/B,UAAU,MAAM,IAAI,CAAAG,WAAS;QAC3B,MAAM,GAAG,MAAM,YAAYA,KAAI;QAC/B,SAAS;MACjB,EAAQ;MACF,SAAS;IACf;EACA;AAEE,UAAQ,YAAY;AACtB;ACtBA,IAAM,sBAAsB;AAQ5B,SAAS,cAAc,YAAY,MAAM;AACvC,QAAM,SAAS,UAAS;AACxB,QAAM,iBAAiB,kBAAiB;AAExC,MAAI,CAAC;AAAQ;AAEb,QAAM,EAAE,mBAAmB,MAAM,iBAAiB,oBAAmB,IAAK,OAAO,WAAU;AAE3F,MAAI,kBAAkB;AAAG;AAEzB,QAAM,YAAY,uBAAsB;AACxC,QAAM,mBAAmB,EAAE,WAAW,GAAG,WAAU;AACnD,QAAM,kBAAkB,mBACnB,eAAe,MAAM,iBAAiB,kBAAkB,IAAI,CAAC,IAC9D;AAEJ,MAAI,oBAAoB;AAAM;AAE9B,MAAI,OAAO,MAAM;AACf,WAAO,KAAK,uBAAuB,iBAAiB,IAAI;EAC5D;AAEE,iBAAe,cAAc,iBAAiB,cAAc;AAC9D;ACnCA,IAAI;AAEJ,IAAMC,qBAAmB;AAEzB,IAAM,gBAAgB,oBAAI,QAAO;AAEjC,IAAM,+BAAgC,MAAM;AAC1C,SAAO;IACL,MAAMA;IACN,YAAY;AAEV,iCAA2B,SAAS,UAAU;AAI9C,UAAI;AACF,iBAAS,UAAU,WAAW,YAAc,MAAM;AAChD,gBAAM,mBAAmB,oBAAoB,IAAI;AACjD,gBAAM,UACJ,cAAc,IAAI,UAAS,CAAE,KAAM,qBAAqB,SAAY,mBAAmB;AACzF,iBAAO,yBAAyB,MAAM,SAAS,IAAI;QAC7D;MACA,QAAc;MAEd;IACA;IACI,MAAM,QAAQ;AACZ,oBAAc,IAAI,QAAQ,IAAI;IACpC;EACA;AACA;AAaA,IAAM,8BAA8B,kBAAkB,4BAA4B;ACtClF,IAAM,wBAAwB;EAC5B;EACA;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;EACA;;AACF;AAIA,IAAMA,qBAAmB;AAezB,IAAM,0BAA0B,kBAAkB,CAAC,UAAU,CAAA,MAAO;AAClE,MAAI;AACJ,SAAO;IACL,MAAMA;IACN,MAAM,QAAQ;AACZ,YAAM,gBAAgB,OAAO,WAAU;AACvC,sBAAgB,cAAc,SAAS,aAAa;IAC1D;IACI,aAAa,OAAO,OAAO,QAAQ;AACjC,UAAI,CAAC,eAAe;AAClB,cAAM,gBAAgB,OAAO,WAAU;AACvC,wBAAgB,cAAc,SAAS,aAAa;MAC5D;AACM,aAAOC,mBAAiB,OAAO,aAAa,IAAI,OAAO;IAC7D;EACA;AACA,CAAC;AAkBD,IAAM,4BAA4B,kBAAmB,CAAC,UAAU,CAAA,MAAO;AACrE,SAAO;IACL,GAAG,wBAAwB,OAAO;IAClC,MAAM;EACV;AACA,CAAC;AAED,SAAS,cACP,kBAAkB,CAAA,GAClB,gBAAgB,CAAA,GAChB;AACA,SAAO;IACL,WAAW,CAAC,GAAI,gBAAgB,aAAa,CAAA,GAAK,GAAI,cAAc,aAAa,CAAA,CAAG;IACpF,UAAU,CAAC,GAAI,gBAAgB,YAAY,CAAA,GAAK,GAAI,cAAc,YAAY,CAAA,CAAG;IACjF,cAAc;MACZ,GAAI,gBAAgB,gBAAgB,CAAA;MACpC,GAAI,cAAc,gBAAgB,CAAA;MAClC,GAAI,gBAAgB,uBAAuB,CAAA,IAAK;IACtD;IACI,oBAAoB,CAAC,GAAI,gBAAgB,sBAAsB,CAAA,GAAK,GAAI,cAAc,sBAAsB,CAAA,CAAG;EACnH;AACA;AAEA,SAASA,mBAAiB,OAAO,SAAS;AACxC,MAAI,CAAC,MAAM,MAAM;AAEf,QAAI,gBAAgB,OAAO,QAAQ,YAAY,GAAG;AAChDL,qBACE,OAAO;QACL;SAA0E,oBAAoB,KAAK,CAAC;MAC9G;AACM,aAAO;IACb;AACI,QAAI,gBAAgB,KAAK,GAAG;AAC1BA,qBACE,OAAO;QACL;SAAuF;UACrF;QACZ,CAAW;MACX;AACM,aAAO;IACb;AACI,QAAI,aAAa,OAAO,QAAQ,QAAQ,GAAG;AACzCA,qBACE,OAAO;QACL;SAAsE;UACpE;QACZ,CAAW;OAAW,mBAAmB,KAAK,CAAC;MAC/C;AACM,aAAO;IACb;AACI,QAAI,CAAC,cAAc,OAAO,QAAQ,SAAS,GAAG;AAC5CA,qBACE,OAAO;QACL;SAA2E;UACzE;QACZ,CAAW;OAAW,mBAAmB,KAAK,CAAC;MAC/C;AACM,aAAO;IACb;EACA,WAAa,MAAM,SAAS,eAAe;AAGvC,QAAI,sBAAsB,OAAO,QAAQ,kBAAkB,GAAG;AAC5DA,qBACE,OAAO;QACL;SAAgF,oBAAoB,KAAK,CAAC;MACpH;AACM,aAAO;IACb;EACA;AACE,SAAO;AACT;AAEA,SAAS,gBAAgB,OAAO,cAAc;AAC5C,MAAI,EAAC,6CAAc,SAAQ;AACzB,WAAO;EACX;AAEE,SAAO,yBAAyB,KAAK,EAAE,KAAK,aAAW,yBAAyB,SAAS,YAAY,CAAC;AACxG;AAEA,SAAS,sBAAsB,OAAO,oBAAoB;AACxD,MAAI,EAAC,yDAAoB,SAAQ;AAC/B,WAAO;EACX;AAEE,QAAM,OAAO,MAAM;AACnB,SAAO,OAAO,yBAAyB,MAAM,kBAAkB,IAAI;AACrE;AAEA,SAAS,aAAa,OAAO,UAAU;AACrC,MAAI,EAAC,qCAAU,SAAQ;AACrB,WAAO;EACX;AACE,QAAM,MAAM,mBAAmB,KAAK;AACpC,SAAO,CAAC,MAAM,QAAQ,yBAAyB,KAAK,QAAQ;AAC9D;AAEA,SAAS,cAAc,OAAO,WAAW;AACvC,MAAI,EAAC,uCAAW,SAAQ;AACtB,WAAO;EACX;AACE,QAAM,MAAM,mBAAmB,KAAK;AACpC,SAAO,CAAC,MAAM,OAAO,yBAAyB,KAAK,SAAS;AAC9D;AAEA,SAAS,iBAAiB,SAAS,CAAA,GAAI;AACrC,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,QAAQ,OAAO,CAAC;AAEtB,QAAI,SAAS,MAAM,aAAa,iBAAiB,MAAM,aAAa,iBAAiB;AACnF,aAAO,MAAM,YAAY;IAC/B;EACA;AAEE,SAAO;AACT;AAEA,SAAS,mBAAmB,OAAO;;AACjC,MAAI;AAGF,UAAM,gBAAgB,CAAC,KAAI,WAAM,cAAN,mBAAiB,WAAU,CAAA,CAAG,EACtD,QAAO,EACP,KAAK,WAAK;;AAAI,eAAAM,MAAA,MAAM,cAAN,gBAAAA,IAAiB,eAAc,YAAa,MAAAC,MAAA,MAAM,eAAN,gBAAAA,IAAkB,WAAlB,mBAA0B;KAAM;AAC7F,UAAM,UAAS,oDAAe,eAAf,mBAA2B;AAC1C,WAAO,SAAS,iBAAiB,MAAM,IAAI;EAC/C,SAAW,IAAI;AACXP,mBAAe,OAAO,MAAM,gCAAgC,oBAAoB,KAAK,CAAC,EAAE;AACxF,WAAO;EACX;AACA;AAEA,SAAS,gBAAgB,OAAO;;AAE9B,MAAI,GAAC,iBAAM,cAAN,mBAAiB,WAAjB,mBAAyB,SAAQ;AACpC,WAAO;EACX;AAEE;;IAEE,CAAC,MAAM;IAEP,CAAC,MAAM,UAAU,OAAO,KAAK,WAAS,MAAM,cAAe,MAAM,QAAQ,MAAM,SAAS,WAAY,MAAM,KAAK;;AAEnH;ACvNA,SAAS,4BACP,kCACA,QACA,KACA,OACA,OACA,MACA;;AACA,MAAI,GAAC,WAAM,cAAN,mBAAiB,WAAU,CAAC,QAAQ,CAAC,aAAa,KAAK,mBAAmB,KAAK,GAAG;AACrF;EACJ;AAGE,QAAM,oBACJ,MAAM,UAAU,OAAO,SAAS,IAAI,MAAM,UAAU,OAAO,MAAM,UAAU,OAAO,SAAS,CAAC,IAAI;AAGlG,MAAI,mBAAmB;AACrB,UAAM,UAAU,SAAS;MACvB;MACA;MACA;MACA,KAAK;MACL;MACA,MAAM,UAAU;MAChB;MACA;IACN;EACA;AACA;AAEA,SAAS,6BACP,kCACA,QACA,OACA,OACA,KACA,gBACA,WACA,aACA;AACA,MAAI,eAAe,UAAU,QAAQ,GAAG;AACtC,WAAO;EACX;AAEE,MAAI,gBAAgB,CAAC,GAAG,cAAc;AAGtC,MAAI,aAAa,MAAM,GAAG,GAAG,KAAK,GAAG;AACnC,gDAA4C,WAAW,WAAW;AAClE,UAAM,eAAe,iCAAiC,QAAQ,MAAM,GAAG,CAAC;AACxE,UAAM,iBAAiB,cAAc;AACrC,+CAA2C,cAAc,KAAK,gBAAgB,WAAW;AACzF,oBAAgB;MACd;MACA;MACA;MACA,MAAM,GAAG;MACT;MACA,CAAC,cAAc,GAAG,aAAa;MAC/B;MACA;IACN;EACA;AAIE,MAAI,MAAM,QAAQ,MAAM,MAAM,GAAG;AAC/B,UAAM,OAAO,QAAQ,CAAC,YAAY,MAAM;AACtC,UAAI,aAAa,YAAY,KAAK,GAAG;AACnC,oDAA4C,WAAW,WAAW;AAClE,cAAM,eAAe,iCAAiC,QAAQ,UAAU;AACxE,cAAM,iBAAiB,cAAc;AACrC,mDAA2C,cAAc,UAAU,CAAC,KAAK,gBAAgB,WAAW;AACpG,wBAAgB;UACd;UACA;UACA;UACA;UACA;UACA,CAAC,cAAc,GAAG,aAAa;UAC/B;UACA;QACV;MACA;IACA,CAAK;EACL;AAEE,SAAO;AACT;AAEA,SAAS,4CAA4C,WAAW,aAAa;AAE3E,YAAU,YAAY,UAAU,aAAa,EAAE,MAAM,WAAW,SAAS,KAAI;AAE7E,YAAU,YAAY;IACpB,GAAG,UAAU;IACb,GAAI,UAAU,SAAS,oBAAoB,EAAE,oBAAoB,KAAI;IACrE,cAAc;EAClB;AACA;AAEA,SAAS,2CACP,WACA,QACA,aACA,UACA;AAEA,YAAU,YAAY,UAAU,aAAa,EAAE,MAAM,WAAW,SAAS,KAAI;AAE7E,YAAU,YAAY;IACpB,GAAG,UAAU;IACb,MAAM;IACN;IACA,cAAc;IACd,WAAW;EACf;AACA;AChHA,SAAS,iCAAiC,SAAS;AACjD,QAAM,OAAO;AACb,aAAW,MAAM,OAAO;AACxB,kBAAgB,MAAM,iBAAiB;AACzC;AAEA,SAAS,oBAAoB;AAC3B,MAAI,EAAE,aAAa,aAAa;AAC9B;EACJ;AAEE,iBAAe,QAAQ,SAAU,OAAO;AACtC,QAAI,EAAE,SAAS,WAAW,UAAU;AAClC;IACN;AAEI,SAAK,WAAW,SAAS,OAAO,SAAU,uBAAuB;AAC/D,6BAAuB,KAAK,IAAI;AAEhC,aAAO,YAAa,MAAM;AACxB,cAAM,cAAc,EAAE,MAAM,MAAK;AACjC,wBAAgB,WAAW,WAAW;AAEtC,cAAM,MAAM,uBAAuB,KAAK;AACxC,mCAAK,MAAM,WAAW,SAAS;MACvC;IACA,CAAK;EACL,CAAG;AACH;ACjCA,SAAS,wBAAwB,OAAO;AACtC,SACE,UAAU,SAAS,YAAY,CAAC,SAAS,SAAS,WAAW,OAAO,QAAQ,OAAO,EAAE,SAAS,KAAK,IAAI,QAAQ;AAEnH;ACLA,IAAMI,qBAAmB;AAEzB,IAAM,qBAAsB,MAAM;AAChC,MAAI;AAEJ,SAAO;IACL,MAAMA;IACN,aAAa,cAAc;AAGzB,UAAI,aAAa,MAAM;AACrB,eAAO;MACf;AAGM,UAAI;AACF,YAAI,iBAAiB,cAAc,aAAa,GAAG;AACjDJ,yBAAe,OAAO,KAAK,sEAAsE;AACjG,iBAAO;QACjB;MACA,SAAe,KAAK;MAAA;AAEd,aAAQ,gBAAgB;IAC9B;EACA;AACA;AAKA,IAAM,oBAAoB,kBAAkB,kBAAkB;AAG9D,SAAS,iBAAiB,cAAc,eAAe;AACrD,MAAI,CAAC,eAAe;AAClB,WAAO;EACX;AAEE,MAAI,oBAAoB,cAAc,aAAa,GAAG;AACpD,WAAO;EACX;AAEE,MAAI,sBAAsB,cAAc,aAAa,GAAG;AACtD,WAAO;EACX;AAEE,SAAO;AACT;AAEA,SAAS,oBAAoB,cAAc,eAAe;AACxD,QAAM,iBAAiB,aAAa;AACpC,QAAM,kBAAkB,cAAc;AAGtC,MAAI,CAAC,kBAAkB,CAAC,iBAAiB;AACvC,WAAO;EACX;AAGE,MAAK,kBAAkB,CAAC,mBAAqB,CAAC,kBAAkB,iBAAkB;AAChF,WAAO;EACX;AAEE,MAAI,mBAAmB,iBAAiB;AACtC,WAAO;EACX;AAEE,MAAI,CAAC,mBAAmB,cAAc,aAAa,GAAG;AACpD,WAAO;EACX;AAEE,MAAI,CAAC,kBAAkB,cAAc,aAAa,GAAG;AACnD,WAAO;EACX;AAEE,SAAO;AACT;AAEA,SAAS,sBAAsB,cAAc,eAAe;AAC1D,QAAM,oBAAoB,uBAAuB,aAAa;AAC9D,QAAM,mBAAmB,uBAAuB,YAAY;AAE5D,MAAI,CAAC,qBAAqB,CAAC,kBAAkB;AAC3C,WAAO;EACX;AAEE,MAAI,kBAAkB,SAAS,iBAAiB,QAAQ,kBAAkB,UAAU,iBAAiB,OAAO;AAC1G,WAAO;EACX;AAEE,MAAI,CAAC,mBAAmB,cAAc,aAAa,GAAG;AACpD,WAAO;EACX;AAEE,MAAI,CAAC,kBAAkB,cAAc,aAAa,GAAG;AACnD,WAAO;EACX;AAEE,SAAO;AACT;AAEA,SAAS,kBAAkB,cAAc,eAAe;AACtD,MAAI,gBAAgB,mBAAmB,YAAY;AACnD,MAAI,iBAAiB,mBAAmB,aAAa;AAGrD,MAAI,CAAC,iBAAiB,CAAC,gBAAgB;AACrC,WAAO;EACX;AAGE,MAAK,iBAAiB,CAAC,kBAAoB,CAAC,iBAAiB,gBAAiB;AAC5E,WAAO;EACX;AAEE,kBAAgB;AAChB,mBAAiB;AAGjB,MAAI,eAAe,WAAW,cAAc,QAAQ;AAClD,WAAO;EACX;AAGE,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAE9C,UAAM,SAAS,eAAe,CAAC;AAE/B,UAAM,SAAS,cAAc,CAAC;AAE9B,QACE,OAAO,aAAa,OAAO,YAC3B,OAAO,WAAW,OAAO,UACzB,OAAO,UAAU,OAAO,SACxB,OAAO,aAAa,OAAO,UAC3B;AACA,aAAO;IACb;EACA;AAEE,SAAO;AACT;AAEA,SAAS,mBAAmB,cAAc,eAAe;AACvD,MAAI,qBAAqB,aAAa;AACtC,MAAI,sBAAsB,cAAc;AAGxC,MAAI,CAAC,sBAAsB,CAAC,qBAAqB;AAC/C,WAAO;EACX;AAGE,MAAK,sBAAsB,CAAC,uBAAyB,CAAC,sBAAsB,qBAAsB;AAChG,WAAO;EACX;AAEE,uBAAqB;AACrB,wBAAsB;AAGtB,MAAI;AACF,WAAO,CAAC,EAAE,mBAAmB,KAAK,EAAE,MAAM,oBAAoB,KAAK,EAAE;EACzE,SAAW,KAAK;AACZ,WAAO;EACX;AACA;AAEA,SAAS,uBAAuB,OAAO;;AACrC,WAAO,WAAM,cAAN,mBAAiB,WAAU,MAAM,UAAU,OAAO,CAAC;AAC5D;ACIA,SAAS,SAAS,KAAK;AACrB,MAAI,CAAC,KAAK;AACR,WAAO,CAAA;EACX;AAEE,QAAM,QAAQ,IAAI,MAAM,8DAA8D;AAEtF,MAAI,CAAC,OAAO;AACV,WAAO,CAAA;EACX;AAGE,QAAM,QAAQ,MAAM,CAAC,KAAK;AAC1B,QAAM,WAAW,MAAM,CAAC,KAAK;AAC7B,SAAO;IACL,MAAM,MAAM,CAAC;IACb,MAAM,MAAM,CAAC;IACb,UAAU,MAAM,CAAC;IACjB,QAAQ;IACR,MAAM;IACN,UAAU,MAAM,CAAC,IAAI,QAAQ;;EACjC;AACA;ACtMA,SAAS,wCAAwC,YAAY;AAE3D,MAAI,eAAe,QAAW;AAC5B,WAAO;EACX,WAAa,cAAc,OAAO,aAAa,KAAK;AAChD,WAAO;EACX,WAAa,cAAc,KAAK;AAC5B,WAAO;EACX,OAAS;AACL,WAAO;EACX;AACA;ACVA,IAAMQ,WAAS;AAwDf,SAAS,kBAAkB;AACzB,SAAO,aAAaA,YAAU,CAAC,CAACA,SAAO;AACzC;AAWA,SAAS,oBAAoB;AAC3B,MAAI,EAAE,WAAWA,WAAS;AACxB,WAAO;EACX;AAEE,MAAI;AACF,QAAI,QAAO;AACX,QAAI,QAAQ,wBAAwB;AACpC,QAAI,SAAQ;AACZ,WAAO;EACX,SAAW,GAAG;AACV,WAAO;EACX;AACA;AAMA,SAAS,iBAAiB,MAAM;AAC9B,SAAO,QAAQ,mDAAmD,KAAK,KAAK,SAAQ,CAAE;AACxF;AAQA,SAAS,sBAAsB;;AAC7B,MAAI,OAAO,gBAAgB,UAAU;AACnC,WAAO;EACX;AAEE,MAAI,CAAC,kBAAiB,GAAI;AACxB,WAAO;EACX;AAIE,MAAI,iBAAiBA,SAAO,KAAK,GAAG;AAClC,WAAO;EACX;AAIE,MAAI,SAAS;AACb,QAAM,MAAMA,SAAO;AAEnB,MAAI,OAAO,OAAQ,IAAI,kBAAoB,YAAY;AACrD,QAAI;AACF,YAAM,UAAU,IAAI,cAAc,QAAQ;AAC1C,cAAQ,SAAS;AACjB,UAAI,KAAK,YAAY,OAAO;AAC5B,WAAI,aAAQ,kBAAR,mBAAuB,OAAO;AAEhC,iBAAS,iBAAiB,QAAQ,cAAc,KAAK;MAC7D;AACM,UAAI,KAAK,YAAY,OAAO;IAClC,SAAa,KAAK;AACZR,qBACE,OAAO,KAAK,mFAAmF,GAAG;IAC1G;EACA;AAEE,SAAO;AACT;AC5HA,SAAS,+BACP,SACA,sBACA;AACA,QAAM,OAAO;AACb,aAAW,MAAM,OAAO;AACxB,kBAAgB,MAAM,MAAM,gBAAgB,QAAW,oBAAoB,CAAC;AAC9E;AAgBA,SAAS,gBAAgB,iBAAiB,uBAAuB,OAAO;AACtE,MAAI,wBAAwB,CAAC,oBAAmB,GAAI;AAClD;EACJ;AAEE,OAAK,YAAY,SAAS,SAAU,eAAe;AACjD,WAAO,YAAa,MAAM;AAQxB,YAAM,eAAe,IAAI,MAAK;AAE9B,YAAM,EAAE,QAAQ,IAAG,IAAK,eAAe,IAAI;AAC3C,YAAM,cAAc;QAClB;QACA,WAAW;UACT;UACA;QACV;QACQ,gBAAgB,mBAAkB,IAAK;;QAEvC;QACA,SAAS,wBAAwB,IAAI;MAC7C;AAG4B;AACpB,wBAAgB,SAAS;UACvB,GAAG;QACb,CAAS;MACT;AAGM,aAAO,cAAc,MAAM,YAAY,IAAI,EAAE;QAC3C,OAAO,aAAa;AAGX;AACL,4BAAgB,SAAS;cACvB,GAAG;cACH,cAAc,mBAAkB,IAAK;cACrC;YACd,CAAa;UACb;AAEU,iBAAO;QACjB;QACQ,CAAC,UAAU;AACT,0BAAgB,SAAS;YACvB,GAAG;YACH,cAAc,mBAAkB,IAAK;YACrC;UACZ,CAAW;AAED,cAAI,QAAQ,KAAK,KAAK,MAAM,UAAU,QAAW;AAK/C,kBAAM,QAAQ,aAAa;AAC3B,qCAAyB,OAAO,eAAe,CAAC;UAC5D;AAOU,cACE,iBAAiB,cAChB,MAAM,YAAY,qBACjB,MAAM,YAAY,iBAClB,MAAM,YAAY,oDACpB;AACA,gBAAI;AACF,oBAAMS,OAAM,IAAI,IAAI,YAAY,UAAU,GAAG;AAC7C,oBAAM,UAAU,GAAG,MAAM,OAAO,KAAKA,KAAI,IAAI;YAC3D,QAAoB;YAEpB;UACA;AAKU,gBAAM;QAChB;MACA;IACA;EACA,CAAG;AACH;AAuEA,SAAS,QAAQ,KAAK,MAAM;AAC1B,SAAO,CAAC,CAAC,OAAO,OAAO,QAAQ,YAAY,CAAC,CAAE,IAAM,IAAI;AAC1D;AAEA,SAAS,mBAAmB,UAAU;AACpC,MAAI,OAAO,aAAa,UAAU;AAChC,WAAO;EACX;AAEE,MAAI,CAAC,UAAU;AACb,WAAO;EACX;AAEE,MAAI,QAAQ,UAAU,KAAK,GAAG;AAC5B,WAAO,SAAS;EACpB;AAEE,MAAI,SAAS,UAAU;AACrB,WAAO,SAAS,SAAQ;EAC5B;AAEE,SAAO;AACT;AAMA,SAAS,eAAe,WAAW;AACjC,MAAI,UAAU,WAAW,GAAG;AAC1B,WAAO,EAAE,QAAQ,OAAO,KAAK,GAAE;EACnC;AAEE,MAAI,UAAU,WAAW,GAAG;AAC1B,UAAM,CAAC,KAAK,OAAO,IAAI;AAEvB,WAAO;MACL,KAAK,mBAAmB,GAAG;MAC3B,QAAQ,QAAQ,SAAS,QAAQ,IAAI,OAAO,QAAQ,MAAM,EAAE,YAAW,IAAK;IAClF;EACA;AAEE,QAAM,MAAM,UAAU,CAAC;AACvB,SAAO;IACL,KAAK,mBAAmB,GAAG;IAC3B,QAAQ,QAAQ,KAAK,QAAQ,IAAI,OAAO,IAAI,MAAM,EAAE,YAAW,IAAK;EACxE;AACA;AAEA,SAAS,wBAAwB,WAAW;AAC1C,QAAM,CAAC,iBAAiB,eAAe,IAAI;AAE3C,MAAI;AACF,QACE,OAAO,oBAAoB,YAC3B,oBAAoB,QACpB,aAAa,mBACb,gBAAgB,SAChB;AACA,aAAO,IAAI,QAAQ,gBAAgB,OAAO;IAChD;AAEI,QAAI,UAAU,eAAe,GAAG;AAC9B,aAAO,IAAI,QAAQ,gBAAgB,OAAO;IAChD;EACA,QAAU;EAEV;AAEE;AACF;ACtPA,SAAS,eAAe;AAEM,SAAO;AACrC;AC5BA,IAAMD,WAAS;AAEf,IAAI,gBAAgB;AAKpB,SAAS,sBAAsB;AAC7B,SAAO,gBAAgB;AACzB;AAKA,SAAS,oBAAoB;AAE3B;AACA,aAAW,MAAM;AACf;EACJ,CAAG;AACH;AAaA,SAAS,KACP,IACA,UAEC,CAAA,GACD;AAQA,WAAS,WAAWE,KAAI;AACtB,WAAO,OAAOA,QAAO;EACzB;AAEE,MAAI,CAAC,WAAW,EAAE,GAAG;AACnB,WAAO;EACX;AAEE,MAAI;AAGF,UAAM,UAAW,GAAK;AACtB,QAAI,SAAS;AACX,UAAI,OAAO,YAAY,YAAY;AACjC,eAAO;MACf,OAAa;AAGL,eAAO;MACf;IACA;AAGI,QAAI,oBAAoB,EAAE,GAAG;AAC3B,aAAO;IACb;EACA,SAAW,GAAG;AAIV,WAAO;EACX;AAIE,QAAM,gBAAgB,YAAc,MAAM;AACxC,QAAI;AAEF,YAAM,mBAAmB,KAAK,IAAI,SAAO,KAAK,KAAK,OAAO,CAAC;AAM3D,aAAO,GAAG,MAAM,MAAM,gBAAgB;IAC5C,SAAa,IAAI;AACX,wBAAiB;AAEjB,gBAAU,WAAS;AACjB,cAAM,kBAAkB,WAAS;AAC/B,cAAI,QAAQ,WAAW;AACrB,kCAAsB,OAAO,MAAoB;AACjD,kCAAsB,OAAO,QAAQ,SAAS;UAC1D;AAEU,gBAAM,QAAQ;YACZ,GAAG,MAAM;YACT,WAAW;UACvB;AAEU,iBAAO;QACjB,CAAS;AAED,yBAAiB,EAAE;MAC3B,CAAO;AAED,YAAM;IACZ;EACA;AAGE,MAAI;AACF,eAAW,YAAY,IAAI;AACzB,UAAI,OAAO,UAAU,eAAe,KAAK,IAAI,QAAQ,GAAG;AACtD,sBAAc,QAAQ,IAAK,GAAG,QAAQ;MAC9C;IACA;EACA,QAAU;EAGV;AAIE,sBAAoB,eAAe,EAAE;AAErC,2BAAyB,IAAI,sBAAsB,aAAa;AAGhE,MAAI;AAEF,UAAM,aAAa,OAAO,yBAAyB,eAAe,MAAM;AACxE,QAAI,WAAW,cAAc;AAC3B,aAAO,eAAe,eAAe,QAAQ;QAC3C,MAAM;AACJ,iBAAO,GAAG;QACpB;MACA,CAAO;IACP;EACA,QAAU;EAGV;AAEE,SAAO;AACT;AAKA,SAAS,qBAAqB;AAE5B,QAAM,MAAM,gBAAe;AAC3B,QAAM,EAAE,SAAQ,IAAKF,SAAO,YAAY,CAAA;AACxC,QAAM,EAAE,UAAS,IAAKA,SAAO,aAAa,CAAA;AAE1C,QAAM,UAAU;IACd,GAAI,YAAY,EAAE,SAAS,SAAQ;IACnC,GAAI,aAAa,EAAE,cAAc,UAAS;EAC9C;AACE,QAAM,UAAU;IACd;IACA;EACJ;AAEE,SAAO;AACT;ACzKA,SAAS,mBAAmB,aAAa,IAAI;AAE3C,QAAM,SAAS,iBAAiB,aAAa,EAAE;AAE/C,QAAM,YAAY;IAChB,MAAM,YAAY,EAAE;IACpB,OAAO,eAAe,EAAE;EAC5B;AAEE,MAAI,OAAO,QAAQ;AACjB,cAAU,aAAa,EAAE,OAAM;EACnC;AAEE,MAAI,UAAU,SAAS,UAAa,UAAU,UAAU,IAAI;AAC1D,cAAU,QAAQ;EACtB;AAEE,SAAO;AACT;AAEA,SAAS,qBACP,aACA,WACA,oBACA,sBACA;AACA,QAAM,SAAS,UAAS;AACxB,QAAM,iBAAiB,iCAAQ,aAAa;AAG5C,QAAM,gBAAgB,2BAA2B,SAAS;AAE1D,QAAM,QAAQ;IACZ,gBAAgB,gBAAgB,WAAW,cAAc;EAC7D;AAEE,MAAI,eAAe;AACjB,WAAO;MACL,WAAW;QACT,QAAQ,CAAC,mBAAmB,aAAa,aAAa,CAAC;MAC/D;MACM;IACN;EACA;AAEE,QAAM,QAAQ;IACZ,WAAW;MACT,QAAQ;QACN;UACE,MAAM,QAAQ,SAAS,IAAI,UAAU,YAAY,OAAO,uBAAuB,uBAAuB;UACtG,OAAO,gCAAgC,WAAW,EAAE,qBAAoB,CAAE;QACpF;MACA;IACA;IACI;EACJ;AAEE,MAAI,oBAAoB;AACtB,UAAM,SAAS,iBAAiB,aAAa,kBAAkB;AAC/D,QAAI,OAAO,QAAQ;AAGjB,YAAM,UAAU,OAAO,CAAC,EAAE,aAAa,EAAE,OAAM;IACrD;EACA;AAEE,SAAO;AACT;AAEA,SAAS,eAAe,aAAa,IAAI;AACvC,SAAO;IACL,WAAW;MACT,QAAQ,CAAC,mBAAmB,aAAa,EAAE,CAAC;IAClD;EACA;AACA;AAGA,SAAS,iBACP,aACA,IACA;AAIA,QAAM,aAAa,GAAG,cAAc,GAAG,SAAS;AAEhD,QAAM,YAAY,6BAA6B,EAAE;AACjD,QAAM,cAAc,qBAAqB,EAAE;AAE3C,MAAI;AACF,WAAO,YAAY,YAAY,WAAW,WAAW;EACzD,SAAW,GAAG;EAEd;AAEE,SAAO,CAAA;AACT;AAGA,IAAM,sBAAsB;AAO5B,SAAS,6BAA6B,IAAI;AACxC,MAAI,MAAM,oBAAoB,KAAK,GAAG,OAAO,GAAG;AAC9C,WAAO;EACX;AAEE,SAAO;AACT;AAUA,SAAS,qBAAqB,IAAI;AAChC,MAAI,OAAO,GAAG,gBAAgB,UAAU;AACtC,WAAO,GAAG;EACd;AAEE,SAAO;AACT;AAIA,SAAS,uBAAuB,WAAW;AAGzC,MAAI,OAAO,gBAAgB,eAAe,OAAO,YAAY,cAAc,aAAa;AAEtF,WAAO,qBAAqB,YAAY;EAC5C,OAAS;AACL,WAAO;EACX;AACA;AAOA,SAAS,YAAY,IAAI;AACvB,QAAM,OAAO,yBAAI;AAIjB,MAAI,CAAC,QAAQ,uBAAuB,EAAE,GAAG;AAEvC,UAAM,mBAAmB,GAAG,WAAW,MAAM,QAAQ,GAAG,OAAO,KAAK,GAAG,QAAQ,UAAU;AACzF,WAAO,mBAAmB,GAAG,QAAQ,CAAC,IAAI;EAC9C;AAEE,SAAO;AACT;AAOA,SAAS,eAAe,IAAI;AAC1B,QAAM,UAAU,yBAAI;AAEpB,MAAI,uBAAuB,EAAE,GAAG;AAE9B,QAAI,MAAM,QAAQ,GAAG,OAAO,KAAK,GAAG,QAAQ,UAAU,GAAG;AACvD,aAAO,GAAG,QAAQ,CAAC;IACzB;AACI,WAAO;EACX;AAEE,MAAI,CAAC,SAAS;AACZ,WAAO;EACX;AAEE,MAAI,QAAQ,SAAS,OAAO,QAAQ,MAAM,YAAY,UAAU;AAC9D,WAAO,QAAQ,MAAM;EACzB;AAEE,SAAO;AACT;AAMA,SAAS,mBACP,aACA,WACA,MACA,kBACA;AACA,QAAM,sBAAqB,6BAAM,uBAAsB;AACvD,QAAM,QAAQ,sBAAsB,aAAa,WAAW,oBAAoB,gBAAgB;AAChG,wBAAsB,KAAK;AAC3B,QAAM,QAAQ;AACd,MAAI,6BAAM,UAAU;AAClB,UAAM,WAAW,KAAK;EAC1B;AACE,SAAO,oBAAoB,KAAK;AAClC;AAMA,SAAS,iBACP,aACA,SACA,QAAQ,QACR,MACA,kBACA;AACA,QAAM,sBAAqB,6BAAM,uBAAsB;AACvD,QAAM,QAAQ,gBAAgB,aAAa,SAAS,oBAAoB,gBAAgB;AACxF,QAAM,QAAQ;AACd,MAAI,6BAAM,UAAU;AAClB,UAAM,WAAW,KAAK;EAC1B;AACE,SAAO,oBAAoB,KAAK;AAClC;AAKA,SAAS,sBACP,aACA,WACA,oBACA,kBACA,sBACA;AACA,MAAI;AAEJ,MAAIP,aAAa,SAAS,KAAO,UAAY,OAAO;AAElD,UAAM,aAAa;AACnB,WAAO,eAAe,aAAa,WAAW,KAAK;EACvD;AASE,MAAI,WAAW,SAAS,KAAK,eAAe,SAAS,GAAI;AACvD,UAAM,eAAe;AAErB,QAAI,WAAY,WAAa;AAC3B,cAAQ,eAAe,aAAa,SAAS;IACnD,OAAW;AACL,YAAM,OAAO,aAAa,SAAS,WAAW,YAAY,IAAI,aAAa;AAC3E,YAAM,UAAU,aAAa,UAAU,GAAG,IAAI,KAAK,aAAa,OAAO,KAAK;AAC5E,cAAQ,gBAAgB,aAAa,SAAS,oBAAoB,gBAAgB;AAClF,4BAAsB,OAAO,OAAO;IAC1C;AACI,QAAI,UAAU,cAAc;AAE1B,YAAM,OAAO,EAAE,GAAG,MAAM,MAAM,qBAAqB,GAAG,aAAa,IAAI,GAAE;IAC/E;AAEI,WAAO;EACX;AACE,MAAI,QAAQ,SAAS,GAAG;AAEtB,WAAO,eAAe,aAAa,SAAS;EAChD;AACE,MAAI,cAAc,SAAS,KAAK,QAAQ,SAAS,GAAG;AAIlD,UAAM,kBAAkB;AACxB,YAAQ,qBAAqB,aAAa,iBAAiB,oBAAoB,oBAAoB;AACnG,0BAAsB,OAAO;MAC3B,WAAW;IACjB,CAAK;AACD,WAAO;EACX;AAWE,UAAQ,gBAAgB,aAAa,WAAY,oBAAoB,gBAAgB;AACrF,wBAAsB,OAAO,GAAG,SAAS,EAAa;AACtD,wBAAsB,OAAO;IAC3B,WAAW;EACf,CAAG;AAED,SAAO;AACT;AAEA,SAAS,gBACP,aACA,SACA,oBACA,kBACA;AACA,QAAM,QAAQ,CAAA;AAEd,MAAI,oBAAoB,oBAAoB;AAC1C,UAAM,SAAS,iBAAiB,aAAa,kBAAkB;AAC/D,QAAI,OAAO,QAAQ;AACjB,YAAM,YAAY;QAChB,QAAQ,CAAC,EAAE,OAAO,SAAS,YAAY,EAAE,OAAM,EAAE,CAAE;MAC3D;IACA;AACI,0BAAsB,OAAO,EAAE,WAAW,KAAI,CAAE;EACpD;AAEE,MAAI,sBAAsB,OAAO,GAAG;AAClC,UAAM,EAAE,4BAA4B,2BAA0B,IAAK;AAEnE,UAAM,WAAW;MACf,SAAS;MACT,QAAQ;IACd;AACI,WAAO;EACX;AAEE,QAAM,UAAU;AAChB,SAAO;AACT;AAEA,SAAS,gCACP,WACA,EAAE,qBAAoB,GACtB;AACA,QAAM,OAAO,+BAA+B,SAAS;AACrD,QAAM,cAAc,uBAAuB,sBAAsB;AAIjE,MAAIA,aAAa,SAAS,GAAG;AAC3B,WAAO,oCAAoC,WAAW,mBAAmB,UAAU,OAAO;EAC9F;AAEE,MAAI,QAAQ,SAAS,GAAG;AACtB,UAAM,YAAY,mBAAmB,SAAS;AAC9C,WAAO,WAAW,SAAS,YAAY,UAAU,IAAI,iBAAiB,WAAW;EACrF;AAEE,SAAO,sBAAsB,WAAW,eAAe,IAAI;AAC7D;AAEA,SAAS,mBAAmB,KAAK;AAC/B,MAAI;AACF,UAAM,YAAY,OAAO,eAAe,GAAG;AAC3C,WAAO,YAAY,UAAU,YAAY,OAAO;EACpD,SAAW,GAAG;EAEd;AACA;AAGA,SAAS,2BAA2B,KAAK;AACvC,aAAW,QAAQ,KAAK;AACtB,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,GAAG;AACnD,YAAM,QAAQ,IAAI,IAAI;AACtB,UAAI,iBAAiB,OAAO;AAC1B,eAAO;MACf;IACA;EACA;AAEE,SAAO;AACT;AC9XA,IAAM,yBAAyB;AAa/B,IAAM,gBAAN,cAA4B,OAAO;;;;;;EAOhC,YAAY,SAAS;AACpB,UAAM,OAAO;;MAEX,4BAA4B;MAC5B,GAAG;IACT;AACI,UAAM,YAAYO,SAAO,qBAAqB,aAAY;AAC1D,qBAAiB,MAAM,WAAW,CAAC,SAAS,GAAG,SAAS;AAExD,UAAM,IAAI;AAGV,UAAM,SAAS;AACf,UAAM,EAAE,gBAAgB,aAAY,IAAK,OAAO;AAChD,UAAM,aAAa,6CAAc;AAEjC,QAAI,KAAK,qBAAqBA,SAAO,UAAU;AAC7CA,eAAO,SAAS,iBAAiB,oBAAoB,MAAM;AACzD,YAAIA,SAAO,SAAS,oBAAoB,UAAU;AAChD,eAAK,eAAc;AACnB,cAAI,YAAY;AACd,sCAA0B,MAAM;UAC5C;QACA;MACA,CAAO;IACP;AAEI,QAAI,YAAY;AACd,aAAO,GAAG,SAAS,MAAM;AACvB,kCAA0B,MAAM;MACxC,CAAO;AAED,aAAO,GAAG,mBAAmB,MAAM;AACjC,YAAI,OAAO,sBAAsB;AAC/B,uBAAa,OAAO,oBAAoB;QAClD;AAEQ,eAAO,uBAAuB,WAAW,MAAM;AAC7C,oCAA0B,MAAM;QAC1C,GAAW,sBAAsB;MACjC,CAAO;IACP;AAEI,QAAI,gBAAgB;AAClB,aAAO,GAAG,oBAAoB,sBAAsB;AACpD,aAAO,GAAG,qBAAqB,yBAAyB;IAC9D;EACA;;;;EAKG,mBAAmB,WAAW,MAAM;AACnC,WAAO,mBAAmB,KAAK,SAAS,aAAa,WAAW,MAAM,KAAK,SAAS,gBAAgB;EACxG;;;;EAKG,iBACC,SACA,QAAQ,QACR,MACA;AACA,WAAO,iBAAiB,KAAK,SAAS,aAAa,SAAS,OAAO,MAAM,KAAK,SAAS,gBAAgB;EAC3G;;;;EAKG,cACC,OACA,MACA,cACA,gBACA;AACA,UAAM,WAAW,MAAM,YAAY;AAEnC,WAAO,MAAM,cAAc,OAAO,MAAM,cAAc,cAAc;EACxE;AACA;ACnGA,IAAMR,gBAAe,OAAO,qBAAqB,eAAe;ACHhE,IAAM,SAAS;ACCf,IAAM,oBAAoB;AAE1B,IAAI;AACJ,IAAI;AACJ,IAAI;AAQJ,SAAS,uCAAuC,SAAS;AACvD,QAAM,OAAO;AACb,aAAW,MAAM,OAAO;AACxB,kBAAgB,MAAM,aAAa;AACrC;AAGA,SAAS,gBAAgB;AACvB,MAAI,CAAC,OAAO,UAAU;AACpB;EACJ;AAKE,QAAM,oBAAoB,gBAAgB,KAAK,MAAM,KAAK;AAC1D,QAAM,wBAAwB,oBAAoB,mBAAmB,IAAI;AACzE,SAAO,SAAS,iBAAiB,SAAS,uBAAuB,KAAK;AACtE,SAAO,SAAS,iBAAiB,YAAY,uBAAuB,KAAK;AAOzE,GAAC,eAAe,MAAM,EAAE,QAAQ,CAAC,WAAW;;AAC1C,UAAM,eAAe;AACrB,UAAM,SAAQ,kBAAa,MAAM,MAAnB,mBAAsB;AAGpC,QAAI,GAAC,oCAAO,mBAAP,+BAAwB,sBAAqB;AAChD;IACN;AAEI,SAAK,OAAO,oBAAoB,SAAU,0BAA0B;AAClE,aAAO,SAAW,MAAM,UAAU,SAAS;AACzC,YAAI,SAAS,WAAW,QAAQ,YAAY;AAC1C,cAAI;AACF,kBAAMW,YAAY,KAAK,sCACrB,KAAK,uCAAuC,CAAA;AAC9C,kBAAM,iBAAkBA,UAAS,IAAI,IAAIA,UAAS,IAAI,KAAK,EAAE,UAAU,EAAC;AAExE,gBAAI,CAAC,eAAe,SAAS;AAC3B,oBAAM,UAAU,oBAAoB,iBAAiB;AACrD,6BAAe,UAAU;AACzB,uCAAyB,KAAK,MAAM,MAAM,SAAS,OAAO;YACxE;AAEY,2BAAe;UAC3B,SAAmB,GAAG;UAGtB;QACA;AAEQ,eAAO,yBAAyB,KAAK,MAAM,MAAM,UAAU,OAAO;MAC1E;IACA,CAAK;AAED;MACE;MACA;MACA,SAAU,6BAA6B;AACrC,eAAO,SAAW,MAAM,UAAU,SAAS;AACzC,cAAI,SAAS,WAAW,QAAQ,YAAY;AAC1C,gBAAI;AACF,oBAAMA,YAAW,KAAK,uCAAuC,CAAA;AAC7D,oBAAM,iBAAiBA,UAAS,IAAI;AAEpC,kBAAI,gBAAgB;AAClB,+BAAe;AAEf,oBAAI,eAAe,YAAY,GAAG;AAChC,8CAA4B,KAAK,MAAM,MAAM,eAAe,SAAS,OAAO;AAC5E,iCAAe,UAAU;AACzB,yBAAOA,UAAS,IAAI;gBACtC;AAGgB,oBAAI,OAAO,KAAKA,SAAQ,EAAE,WAAW,GAAG;AACtC,yBAAO,KAAK;gBAC9B;cACA;YACA,SAAqB,GAAG;YAGxB;UACA;AAEU,iBAAO,4BAA4B,KAAK,MAAM,MAAM,UAAU,OAAO;QAC/E;MACA;IACA;EACA,CAAG;AACH;AAKA,SAAS,6BAA6B,OAAO;AAE3C,MAAI,MAAM,SAAS,uBAAuB;AACxC,WAAO;EACX;AAEE,MAAI;AAGF,QAAI,CAAC,MAAM,UAAW,MAAM,OAAS,cAAc,2BAA2B;AAC5E,aAAO;IACb;EACA,SAAW,GAAG;EAGd;AAKE,SAAO;AACT;AAMA,SAAS,mBAAmB,WAAW,QAAQ;AAE7C,MAAI,cAAc,YAAY;AAC5B,WAAO;EACX;AAEE,MAAI,EAAC,iCAAQ,UAAS;AACpB,WAAO;EACX;AAIE,MAAI,OAAO,YAAY,WAAW,OAAO,YAAY,cAAc,OAAO,mBAAmB;AAC3F,WAAO;EACX;AAEE,SAAO;AACT;AAKA,SAAS,oBACP,SACA,iBAAiB,OACjB;AACA,SAAO,CAAC,UAAU;AAIhB,QAAI,CAAC,SAAS,MAAM,iBAAiB,GAAG;AACtC;IACN;AAEI,UAAM,SAAS,eAAe,KAAK;AAGnC,QAAI,mBAAmB,MAAM,MAAM,MAAM,GAAG;AAC1C;IACN;AAGI,6BAAyB,OAAO,mBAAmB,IAAI;AAEvD,QAAI,UAAU,CAAC,OAAO,WAAW;AAE/B,+BAAyB,QAAQ,aAAa,MAAK,CAAE;IAC3D;AAEI,UAAM,OAAO,MAAM,SAAS,aAAa,UAAU,MAAM;AAKzD,QAAI,CAAC,6BAA6B,KAAK,GAAG;AACxC,YAAM,cAAc,EAAE,OAAO,MAAM,QAAQ,eAAc;AACzD,cAAQ,WAAW;AACnB,8BAAwB,MAAM;AAC9B,kCAA4B,SAAS,OAAO,YAAY;IAC9D;AAGI,iBAAa,eAAe;AAC5B,sBAAkB,OAAO,WAAW,MAAM;AACxC,kCAA4B;AAC5B,8BAAwB;IAC9B,GAAO,iBAAiB;EACxB;AACA;AAEA,SAAS,eAAe,OAAO;AAC7B,MAAI;AACF,WAAO,MAAM;EACjB,SAAW,GAAG;AAGV,WAAO;EACX;AACA;ACxNA,IAAI;AAUJ,SAAS,iCAAiC,SAAS;AACjD,QAAM,OAAO;AACb,aAAW,MAAM,OAAO;AACxB,kBAAgB,MAAM,iBAAiB;AACzC;AAKA,SAAS,oBAAoB;AAG3B,SAAO,iBAAiB,YAAY,MAAM;AACxC,UAAM,KAAK,OAAO,SAAS;AAE3B,UAAM,OAAO;AACb,eAAW;AAEX,QAAI,SAAS,IAAI;AACf;IACN;AAEI,UAAM,cAAc,EAAE,MAAM,GAAE;AAC9B,oBAAgB,WAAW,WAAW;EAC1C,CAAG;AAGD,MAAI,CAAC,gBAAe,GAAI;AACtB;EACJ;AAEE,WAAS,2BAA2B,yBAAyB;AAC3D,WAAO,YAAc,MAAM;AACzB,YAAM,MAAM,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI;AACxC,UAAI,KAAK;AACP,cAAM,OAAO;AAOb,cAAM,KAAK,eAAe,OAAO,GAAG,CAAC;AAGrC,mBAAW;AAEX,YAAI,SAAS,IAAI;AACf,iBAAO,wBAAwB,MAAM,MAAM,IAAI;QACzD;AAEQ,cAAM,cAAc,EAAE,MAAM,GAAE;AAC9B,wBAAgB,WAAW,WAAW;MAC9C;AACM,aAAO,wBAAwB,MAAM,MAAM,IAAI;IACrD;EACA;AAEE,OAAK,OAAO,SAAS,aAAa,0BAA0B;AAC5D,OAAK,OAAO,SAAS,gBAAgB,0BAA0B;AACjE;AAEA,SAAS,eAAe,WAAW;AACjC,MAAI;AACF,UAAM,MAAM,IAAI,IAAI,WAAW,OAAO,SAAS,MAAM;AACrD,WAAO,IAAI,SAAQ;EACvB,QAAU;AAEN,WAAO;EACX;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChFA,IAAM,sBAAsB;AAU5B,SAAS,6BAA6B,SAAS;AAC7C,QAAM,OAAO;AACb,aAAW,MAAM,OAAO;AACxB,kBAAgB,MAAM,aAAa;AACrC;AAGA,SAAS,gBAAgB;AACvB,MAAI,CAAE,OAAS,gBAAgB;AAC7B;EACJ;AAEE,QAAM,WAAW,eAAe;AAGhC,WAAS,OAAO,IAAI,MAAM,SAAS,MAAM;IACvC,MACE,cACA,gBACA,iBAGA;AAMA,YAAM,eAAe,IAAI,MAAK;AAE9B,YAAM,iBAAiB,mBAAkB,IAAK;AAI9C,YAAM,SAAS,SAAS,gBAAgB,CAAC,CAAC,IAAI,gBAAgB,CAAC,EAAE,YAAW,IAAK;AACjF,YAAM,MAAM,eAAe,gBAAgB,CAAC,CAAC;AAE7C,UAAI,CAAC,UAAU,CAAC,KAAK;AACnB,eAAO,aAAa,MAAM,gBAAgB,eAAe;MACjE;AAEM,qBAAe,mBAAmB,IAAI;QACpC;QACA;QACA,iBAAiB,CAAA;MACzB;AAGM,UAAI,WAAW,UAAU,IAAI,MAAM,YAAY,GAAG;AAChD,uBAAe,yBAAyB;MAChD;AAEM,YAAM,4BAA4B,MAAM;AAEtC,cAAM,UAAU,eAAe,mBAAmB;AAElD,YAAI,CAAC,SAAS;AACZ;QACV;AAEQ,YAAI,eAAe,eAAe,GAAG;AACnC,cAAI;AAGF,oBAAQ,cAAc,eAAe;UACjD,SAAmB,GAAG;UAEtB;AAEU,gBAAM,cAAc;YAClB,cAAc,mBAAkB,IAAK;YACrC;YACA,KAAK;YACL;UACZ;AACU,0BAAgB,OAAO,WAAW;QAC5C;MACA;AAEM,UAAI,wBAAwB,kBAAkB,OAAO,eAAe,uBAAuB,YAAY;AACrG,uBAAe,qBAAqB,IAAI,MAAM,eAAe,oBAAoB;UAC/E,MAAM,4BAA4B,2BAA2B,4BAA4B;AACvF,sCAAyB;AACzB,mBAAO,2BAA2B,MAAM,2BAA2B,0BAA0B;UACzG;QACA,CAAS;MACT,OAAa;AACL,uBAAe,iBAAiB,oBAAoB,yBAAyB;MACrF;AAKM,qBAAe,mBAAmB,IAAI,MAAM,eAAe,kBAAkB;QAC3E,MACE,0BACA,yBACA,0BACA;AACA,gBAAM,CAAC,QAAQ,KAAK,IAAI;AAExB,gBAAM,UAAU,wBAAwB,mBAAmB;AAE3D,cAAI,WAAW,SAAS,MAAM,KAAK,SAAS,KAAK,GAAG;AAClD,oBAAQ,gBAAgB,OAAO,YAAW,CAAE,IAAI;UAC5D;AAEU,iBAAO,yBAAyB,MAAM,yBAAyB,wBAAwB;QACjG;MACA,CAAO;AAED,aAAO,aAAa,MAAM,gBAAgB,eAAe;IAC/D;EACA,CAAG;AAGD,WAAS,OAAO,IAAI,MAAM,SAAS,MAAM;IACvC,MAAM,cAAc,aAAa,cAAc;AAC7C,YAAM,gBAAgB,YAAY,mBAAmB;AAErD,UAAI,CAAC,eAAe;AAClB,eAAO,aAAa,MAAM,aAAa,YAAY;MAC3D;AAEM,UAAI,aAAa,CAAC,MAAM,QAAW;AACjC,sBAAc,OAAO,aAAa,CAAC;MAC3C;AAEM,YAAM,cAAc;QAClB,gBAAgB,mBAAkB,IAAK;QACvC,KAAK;MACb;AACM,sBAAgB,OAAO,WAAW;AAElC,aAAO,aAAa,MAAM,aAAa,YAAY;IACzD;EACA,CAAG;AACH;AAWA,SAAS,eAAe,KAAK;AAC3B,MAAI,SAAS,GAAG,GAAG;AACjB,WAAO;EACX;AAEE,MAAI;AAGF,WAAQ,IAAM,SAAQ;EAC1B,QAAU;EAAA;AAER,SAAO;AACT;ACvKA,SAAS,mBACP,SACA,cAAc,wBAAwB,OAAO,GAC7C;AACA,MAAI,kBAAkB;AACtB,MAAI,eAAe;AAEnB,WAAS,YAAY,SAAS;AAC5B,UAAM,cAAc,QAAQ,KAAK;AACjC,uBAAmB;AACnB;AAEA,UAAM,iBAAiB;MACrB,MAAM,QAAQ;MACd,QAAQ;MACR,gBAAgB;MAChB,SAAS,QAAQ;;;;;;;;;;;;MAYjB,WAAW,mBAAmB,OAAS,eAAe;MACtD,GAAG,QAAQ;IACjB;AAEI,QAAI,CAAC,aAAa;AAChB,gCAA0B,OAAO;AACjC,aAAO,oBAAoB,mCAAmC;IACpE;AAEI,QAAI;AAEF,aAAO,YAAY,QAAQ,KAAK,cAAc,EAAE,KAAK,cAAY;AAC/D,2BAAmB;AACnB;AACA,eAAO;UACL,YAAY,SAAS;UACrB,SAAS;YACP,wBAAwB,SAAS,QAAQ,IAAI,sBAAsB;YACnE,eAAe,SAAS,QAAQ,IAAI,aAAa;UAC7D;QACA;MACA,CAAO;IACP,SAAa,GAAG;AACV,gCAA0B,OAAO;AACjC,yBAAmB;AACnB;AACA,aAAO,oBAAoB,CAAC;IAClC;EACA;AAEE,SAAO,gBAAgB,SAAS,WAAW;AAC7C;AC7DA,IAAM,kBAAkB;AAExB,IAAM,iBAAiB;AAEvB,SAAS,YAAY,UAAU,MAAM,QAAQ,OAAO;AAClD,QAAM,QAAQ;IACZ;IACA,UAAU,SAAS,gBAAgB,mBAAmB;IACtD,QAAQ;;EACZ;AAEE,MAAI,WAAW,QAAW;AACxB,UAAM,SAAS;EACnB;AAEE,MAAI,UAAU,QAAW;AACvB,UAAM,QAAQ;EAClB;AAEE,SAAO;AACT;AAKA,IAAM,sBAAsB;AAG5B,IAAM,cACJ;AAEF,IAAM,kBAAkB;AAKxB,IAAM,sBAAsB,UAAQ;AAElC,QAAM,YAAY,oBAAoB,KAAK,IAAI;AAE/C,MAAI,WAAW;AACb,UAAM,CAAA,EAAG,UAAUC,OAAM,GAAG,IAAI;AAChC,WAAO,YAAY,UAAU,kBAAkB,CAACA,OAAM,CAAC,GAAG;EAC9D;AAEE,QAAM,QAAQ,YAAY,KAAK,IAAI;AAEnC,MAAI,OAAO;AACT,UAAM,SAAS,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,QAAQ,MAAM,MAAM;AAExD,QAAI,QAAQ;AACV,YAAM,WAAW,gBAAgB,KAAK,MAAM,CAAC,CAAC;AAE9C,UAAI,UAAU;AAEZ,cAAM,CAAC,IAAI,SAAS,CAAC;AACrB,cAAM,CAAC,IAAI,SAAS,CAAC;AACrB,cAAM,CAAC,IAAI,SAAS,CAAC;MAC7B;IACA;AAII,UAAM,CAAC,MAAM,QAAQ,IAAI,8BAA8B,MAAM,CAAC,KAAK,kBAAkB,MAAM,CAAC,CAAC;AAE7F,WAAO,YAAY,UAAU,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,QAAW,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,MAAS;EACzG;AAEE;AACF;AAEA,IAAM,wBAAwB,CAAC,iBAAiB,mBAAmB;AAKnE,IAAM,aACJ;AACF,IAAM,iBAAiB;AAEvB,IAAM,QAAQ,UAAQ;AACpB,QAAM,QAAQ,WAAW,KAAK,IAAI;AAElC,MAAI,OAAO;AACT,UAAM,SAAS,MAAM,CAAC,KAAK,MAAM,CAAC,EAAE,QAAQ,SAAS,IAAI;AACzD,QAAI,QAAQ;AACV,YAAM,WAAW,eAAe,KAAK,MAAM,CAAC,CAAC;AAE7C,UAAI,UAAU;AAEZ,cAAM,CAAC,IAAI,MAAM,CAAC,KAAK;AACvB,cAAM,CAAC,IAAI,SAAS,CAAC;AACrB,cAAM,CAAC,IAAI,SAAS,CAAC;AACrB,cAAM,CAAC,IAAI;MACnB;IACA;AAEI,QAAI,WAAW,MAAM,CAAC;AACtB,QAAI,OAAO,MAAM,CAAC,KAAK;AACvB,KAAC,MAAM,QAAQ,IAAI,8BAA8B,MAAM,QAAQ;AAE/D,WAAO,YAAY,UAAU,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,QAAW,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,MAAS;EACzG;AAEE;AACF;AAEA,IAAM,uBAAuB,CAAC,gBAAgB,KAAK;AAiCnD,IAAM,0BAA0B,CAAC,uBAAuB,oBAAoB;AAE5E,IAAM,qBAAqB,kBAAkB,GAAG,uBAAuB;AAsBvE,IAAM,gCAAgC,CAAC,MAAM,aAAa;AACxD,QAAM,oBAAoB,KAAK,QAAQ,kBAAkB,MAAM;AAC/D,QAAM,uBAAuB,KAAK,QAAQ,sBAAsB,MAAM;AAEtE,SAAO,qBAAqB,uBACxB;IACE,KAAK,QAAQ,GAAG,MAAM,KAAM,KAAK,MAAM,GAAG,EAAE,CAAC,IAAM;IACnD,oBAAoB,oBAAoB,QAAQ,KAAK,wBAAwB,QAAQ;EAC7F,IACM,CAAC,MAAM,QAAQ;AACrB;AC7KA,IAAMZ,eAAe,OAAO,qBAAqB,eAAe;ACChE,IAAM,4BAA4B;AAElC,IAAMI,qBAAmB;AAEzB,IAAM,0BAA2B,CAAC,UAAU,CAAA,MAAO;AACjD,QAAM,WAAW;IACf,SAAS;IACT,KAAK;IACL,OAAO;IACP,SAAS;IACT,QAAQ;IACR,KAAK;IACL,GAAG;EACP;AAEE,SAAO;IACL,MAAMA;IACN,MAAM,QAAQ;AAEZ,UAAI,SAAS,SAAS;AACpB,yCAAiC,6BAA6B,MAAM,CAAC;MAC7E;AACM,UAAI,SAAS,KAAK;AAChB,+CAAuC,yBAAyB,QAAQ,SAAS,GAAG,CAAC;MAC7F;AACM,UAAI,SAAS,KAAK;AAChB,qCAA6B,yBAAyB,MAAM,CAAC;MACrE;AACM,UAAI,SAAS,OAAO;AAClB,uCAA+B,2BAA2B,MAAM,CAAC;MACzE;AACM,UAAI,SAAS,SAAS;AACpB,yCAAiC,6BAA6B,MAAM,CAAC;MAC7E;AACM,UAAI,SAAS,QAAQ;AACnB,eAAO,GAAG,mBAAmB,4BAA4B,MAAM,CAAC;MACxE;IACA;EACA;AACA;AAEA,IAAM,yBAAyB,kBAAkB,uBAAuB;AAKxE,SAAS,4BAA4B,QAAQ;AAC3C,SAAO,SAAS,oBAAoB,OAAO;AACzC,QAAI,UAAS,MAAO,QAAQ;AAC1B;IACN;AAEI;MACE;QACE,UAAU,UAAU,MAAM,SAAS,gBAAgB,gBAAgB,OAAO;QAC1E,UAAU,MAAM;QAChB,OAAO,MAAM;QACb,SAAS,oBAAoB,KAAK;MAC1C;MACM;QACE;MACR;IACA;EACA;AACA;AAMA,SAAS,yBACP,QACA,KACA;AACA,SAAO,SAAS,oBAAoB,aAAa;AAC/C,QAAI,UAAS,MAAO,QAAQ;AAC1B;IACN;AAEI,QAAI;AACJ,QAAI;AACJ,QAAI,WAAW,OAAO,QAAQ,WAAW,IAAI,qBAAqB;AAElE,QAAI,kBACF,OAAO,QAAQ,YAAY,OAAO,IAAI,oBAAoB,WAAW,IAAI,kBAAkB;AAC7F,QAAI,mBAAmB,kBAAkB,2BAA2B;AAClE,MAAAJ,gBACE,OAAO;QACL,yCAAyC,yBAAyB,oBAAoB,eAAe,oCAAoC,yBAAyB;MAC5K;AACM,wBAAkB;IACxB;AAEI,QAAI,OAAO,aAAa,UAAU;AAChC,iBAAW,CAAC,QAAQ;IAC1B;AAGI,QAAI;AACF,YAAM,QAAQ,YAAY;AAC1B,YAAM,UAAU,SAAS,KAAK,IAAI,MAAM,SAAS;AAEjD,eAAS,iBAAiB,SAAS,EAAE,UAAU,gBAAe,CAAE;AAChE,sBAAgB,iBAAiB,OAAO;IAC9C,SAAa,GAAG;AACV,eAAS;IACf;AAEI,QAAI,OAAO,WAAW,GAAG;AACvB;IACN;AAEI,UAAM,aAAa;MACjB,UAAU,MAAM,YAAY,IAAI;MAChC,SAAS;IACf;AAEI,QAAI,eAAe;AACjB,iBAAW,OAAO,EAAE,qBAAqB,cAAa;IAC5D;AAEI,kBAAc,YAAY;MACxB,OAAO,YAAY;MACnB,MAAM,YAAY;MAClB,QAAQ,YAAY;IAC1B,CAAK;EACL;AACA;AAKA,SAAS,6BAA6B,QAAQ;AAC5C,SAAO,SAAS,mBAAmB,aAAa;AAC9C,QAAI,UAAS,MAAO,QAAQ;AAC1B;IACN;AAEI,UAAM,aAAa;MACjB,UAAU;MACV,MAAM;QACJ,WAAW,YAAY;QACvB,QAAQ;MAChB;MACM,OAAO,wBAAwB,YAAY,KAAK;MAChD,SAAS,SAAS,YAAY,MAAM,GAAG;IAC7C;AAEI,QAAI,YAAY,UAAU,UAAU;AAClC,UAAI,YAAY,KAAK,CAAC,MAAM,OAAO;AACjC,mBAAW,UAAU,qBAAqB,SAAS,YAAY,KAAK,MAAM,CAAC,GAAG,GAAG,KAAK,gBAAgB;AACtG,mBAAW,KAAK,YAAY,YAAY,KAAK,MAAM,CAAC;MAC5D,OAAa;AAEL;MACR;IACA;AAEI,kBAAc,YAAY;MACxB,OAAO,YAAY;MACnB,OAAO,YAAY;IACzB,CAAK;EACL;AACA;AAKA,SAAS,yBAAyB,QAAQ;AACxC,SAAO,SAAS,eAAe,aAAa;AAC1C,QAAI,UAAS,MAAO,QAAQ;AAC1B;IACN;AAEI,UAAM,EAAE,gBAAgB,aAAY,IAAK;AAEzC,UAAM,gBAAgB,YAAY,IAAI,mBAAmB;AAGzD,QAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,eAAe;AACtD;IACN;AAEI,UAAM,EAAE,QAAQ,KAAK,aAAa,KAAI,IAAK;AAE3C,UAAM,OAAO;MACX;MACA;MACA;IACN;AAEI,UAAM,OAAO;MACX,KAAK,YAAY;MACjB,OAAO;MACP;MACA;IACN;AAEI,UAAM,aAAa;MACjB,UAAU;MACV;MACA,MAAM;MACN,OAAO,wCAAwC,WAAW;IAChE;AAEI,WAAO,KAAK,mCAAmC,YAAY,IAAI;AAE/D,kBAAc,YAAY,IAAI;EAClC;AACA;AAKA,SAAS,2BAA2B,QAAQ;AAC1C,SAAO,SAAS,iBAAiB,aAAa;AAC5C,QAAI,UAAS,MAAO,QAAQ;AAC1B;IACN;AAEI,UAAM,EAAE,gBAAgB,aAAY,IAAK;AAGzC,QAAI,CAAC,cAAc;AACjB;IACN;AAEI,QAAI,YAAY,UAAU,IAAI,MAAM,YAAY,KAAK,YAAY,UAAU,WAAW,QAAQ;AAE5F;IACN;AAOI,QAAI,YAAY,OAAO;AACrB,YAAM,OAAO,YAAY;AACzB,YAAM,OAAO;QACX,MAAM,YAAY;QAClB,OAAO,YAAY;QACnB;QACA;MACR;AAEM,YAAM,aAAa;QACjB,UAAU;QACV;QACA,OAAO;QACP,MAAM;MACd;AAEM,aAAO,KAAK,mCAAmC,YAAY,IAAI;AAE/D,oBAAc,YAAY,IAAI;IACpC,OAAW;AACL,YAAM,WAAW,YAAY;AAC7B,YAAM,OAAO;QACX,GAAG,YAAY;QACf,aAAa,qCAAU;MAC/B;AAMM,YAAM,OAAO;QACX,OAAO,YAAY;QACnB;QACA;QACA;MACR;AAEM,YAAM,aAAa;QACjB,UAAU;QACV;QACA,MAAM;QACN,OAAO,wCAAwC,KAAK,WAAW;MACvE;AAEM,aAAO,KAAK,mCAAmC,YAAY,IAAI;AAE/D,oBAAc,YAAY,IAAI;IACpC;EACA;AACA;AAKA,SAAS,6BAA6B,QAAQ;AAC5C,SAAO,SAAS,mBAAmB,aAAa;AAC9C,QAAI,UAAS,MAAO,QAAQ;AAC1B;IACN;AAEI,QAAI,OAAO,YAAY;AACvB,QAAI,KAAK,YAAY;AACrB,UAAM,YAAY,SAASQ,SAAO,SAAS,IAAI;AAC/C,QAAI,aAAa,OAAO,SAAS,IAAI,IAAI;AACzC,UAAM,WAAW,SAAS,EAAE;AAG5B,QAAI,EAAC,yCAAY,OAAM;AACrB,mBAAa;IACnB;AAII,QAAI,UAAU,aAAa,SAAS,YAAY,UAAU,SAAS,SAAS,MAAM;AAChF,WAAK,SAAS;IACpB;AACI,QAAI,UAAU,aAAa,WAAW,YAAY,UAAU,SAAS,WAAW,MAAM;AACpF,aAAO,WAAW;IACxB;AAEI,kBAAc;MACZ,UAAU;MACV,MAAM;QACJ;QACA;MACR;IACA,CAAK;EACL;AACA;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,CAAC,CAAC,SAAS,CAAC,CAAE,MAAQ;AAC/B;AC5UA,IAAM,uBAAuB;EAC3B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAEA,IAAMJ,qBAAmB;AAEzB,IAAM,+BAAgC,CAAC,UAAU,CAAA,MAAO;AACtD,QAAM,WAAW;IACf,gBAAgB;IAChB,aAAa;IACb,uBAAuB;IACvB,aAAa;IACb,YAAY;IACZ,GAAG;EACP;AAEE,SAAO;IACL,MAAMA;;;IAGN,YAAY;AACV,UAAI,SAAS,YAAY;AACvB,aAAKI,UAAQ,cAAc,iBAAiB;MACpD;AAEM,UAAI,SAAS,aAAa;AACxB,aAAKA,UAAQ,eAAe,iBAAiB;MACrD;AAEM,UAAI,SAAS,uBAAuB;AAClC,aAAKA,UAAQ,yBAAyB,QAAQ;MACtD;AAEM,UAAI,SAAS,kBAAkB,oBAAoBA,UAAQ;AACzD,aAAK,eAAe,WAAW,QAAQ,QAAQ;MACvD;AAEM,YAAM,oBAAoB,SAAS;AACnC,UAAI,mBAAmB;AACrB,cAAM,cAAc,MAAM,QAAQ,iBAAiB,IAAI,oBAAoB;AAC3E,oBAAY,QAAQ,gBAAgB;MAC5C;IACA;EACA;AACA;AAKA,IAAM,8BAA8B,kBAAkB,4BAA4B;AAElF,SAAS,kBAAkB,UAAU;AACnC,SAAO,YAAc,MAAM;AACzB,UAAM,mBAAmB,KAAK,CAAC;AAC/B,SAAK,CAAC,IAAI,KAAK,kBAAkB;MAC/B,WAAW;QACT,MAAM,EAAE,UAAU,gBAAgB,QAAQ,EAAC;QAC3C,SAAS;QACT,MAAM;MACd;IACA,CAAK;AACD,WAAO,SAAS,MAAM,MAAM,IAAI;EACpC;AACA;AAEA,SAAS,SAAS,UAAU;AAC1B,SAAO,SAAW,UAAU;AAC1B,WAAO,SAAS,MAAM,MAAM;MAC1B,KAAK,UAAU;QACb,WAAW;UACT,MAAM;YACJ,UAAU;YACV,SAAS,gBAAgB,QAAQ;UAC7C;UACU,SAAS;UACT,MAAM;QAChB;MACA,CAAO;IACP,CAAK;EACL;AACA;AAEA,SAAS,SAAS,cAAc;AAC9B,SAAO,YAAc,MAAM;AAEzB,UAAM,MAAM;AACZ,UAAM,sBAAsB,CAAC,UAAU,WAAW,cAAc,oBAAoB;AAEpF,wBAAoB,QAAQ,UAAQ;AAClC,UAAI,QAAQ,OAAO,OAAO,IAAI,IAAI,MAAM,YAAY;AAClD,aAAK,KAAK,MAAM,SAAU,UAAU;AAClC,gBAAM,cAAc;YAClB,WAAW;cACT,MAAM;gBACJ,UAAU;gBACV,SAAS,gBAAgB,QAAQ;cACjD;cACc,SAAS;cACT,MAAM;YACpB;UACA;AAGU,gBAAM,mBAAmB,oBAAoB,QAAQ;AACrD,cAAI,kBAAkB;AACpB,wBAAY,UAAU,KAAK,UAAU,gBAAgB,gBAAgB;UACjF;AAGU,iBAAO,KAAK,UAAU,WAAW;QAC3C,CAAS;MACT;IACA,CAAK;AAED,WAAO,aAAa,MAAM,MAAM,IAAI;EACxC;AACA;AAEA,SAAS,iBAAiB,QAAQ;;AAChC,QAAM,eAAeA;AACrB,QAAM,SAAQ,kBAAa,MAAM,MAAnB,mBAAsB;AAGpC,MAAI,GAAC,oCAAO,mBAAP,+BAAwB,sBAAqB;AAChD;EACJ;AAEE,OAAK,OAAO,oBAAoB,SAAU,UAE3C;AACG,WAAO,SAAW,WAAW,IAAI,SAAS;AACxC,UAAI;AACF,YAAI,sBAAsB,EAAE,GAAG;AAO7B,aAAG,cAAc,KAAK,GAAG,aAAa;YACpC,WAAW;cACT,MAAM;gBACJ,UAAU;gBACV,SAAS,gBAAgB,EAAE;gBAC3B;cAChB;cACc,SAAS;cACT,MAAM;YACpB;UACA,CAAW;QACX;MACA,QAAc;MAEd;AAEM,aAAO,SAAS,MAAM,MAAM;QAC1B;QACA,KAAK,IAAI;UACP,WAAW;YACT,MAAM;cACJ,UAAU;cACV,SAAS,gBAAgB,EAAE;cAC3B;YACd;YACY,SAAS;YACT,MAAM;UAClB;QACA,CAAS;QACD;MACR,CAAO;IACP;EACA,CAAG;AAED,OAAK,OAAO,uBAAuB,SAAU,6BAE9C;AACG,WAAO,SAAW,WAAW,IAAI,SAAS;AAkBxC,UAAI;AACF,cAAM,uBAAwB,GAAK;AACnC,YAAI,sBAAsB;AACxB,sCAA4B,KAAK,MAAM,WAAW,sBAAsB,OAAO;QACzF;MACA,SAAe,GAAG;MAElB;AACM,aAAO,4BAA4B,KAAK,MAAM,WAAW,IAAI,OAAO;IAC1E;EACA,CAAG;AACH;AAEA,SAAS,sBAAsB,KAAK;AAClC,SAAO,OAAQ,IAAM,gBAAgB;AACvC;ACvOA,IAAM,4BAA4B,kBAAkB,MAAM;AACxD,SAAO;IACL,MAAM;IACN,YAAY;AACV,UAAI,OAAOA,SAAO,aAAa,aAAa;AAC1C,QAAAR,gBACE,OAAO,KAAK,qFAAqF;AACnG;MACR;AAMM,mBAAa,EAAE,gBAAgB,KAAI,CAAE;AACrC,qBAAc;AAGd,uCAAiC,CAAC,EAAE,MAAM,GAAE,MAAO;AAEjD,YAAI,SAAS,UAAa,SAAS,IAAI;AACrC,uBAAa,EAAE,gBAAgB,KAAI,CAAE;AACrC,yBAAc;QACxB;MACA,CAAO;IACP;EACA;AACA,CAAC;ACjCD,IAAMI,qBAAmB;AAEzB,IAAM,6BAA8B,CAAC,UAAU,CAAA,MAAO;AACpD,QAAM,WAAW;IACf,SAAS;IACT,sBAAsB;IACtB,GAAG;EACP;AAEE,SAAO;IACL,MAAMA;IACN,YAAY;AACV,YAAM,kBAAkB;IAC9B;IACI,MAAM,QAAQ;AACZ,UAAI,SAAS,SAAS;AACpB,qCAA6B,MAAM;AACnC,yBAAiB,SAAS;MAClC;AACM,UAAI,SAAS,sBAAsB;AACjC,kDAA0C,MAAM;AAChD,yBAAiB,sBAAsB;MAC/C;IACA;EACA;AACA;AAEA,IAAM,4BAA4B,kBAAkB,0BAA0B;AAE9E,SAAS,6BAA6B,QAAQ;AAC5C,uCAAqC,UAAQ;AAC3C,UAAM,EAAE,aAAa,iBAAgB,IAAK,WAAU;AAEpD,QAAI,UAAS,MAAO,UAAU,oBAAmB,GAAI;AACnD;IACN;AAEI,UAAM,EAAE,KAAK,KAAK,MAAM,QAAQ,MAAK,IAAK;AAE1C,UAAM,QAAQ;MACZ,sBAAsB,aAAa,SAAS,KAAK,QAAW,kBAAkB,KAAK;MACnF;MACA;MACA;IACN;AAEI,UAAM,QAAQ;AAEd,iBAAa,OAAO;MAClB,mBAAmB;MACnB,WAAW;QACT,SAAS;QACT,MAAM;MACd;IACA,CAAK;EACL,CAAG;AACH;AAEA,SAAS,0CAA0C,QAAQ;AACzD,oDAAkD,OAAK;AACrD,UAAM,EAAE,aAAa,iBAAgB,IAAK,WAAU;AAEpD,QAAI,UAAS,MAAO,UAAU,oBAAmB,GAAI;AACnD;IACN;AAEI,UAAM,QAAQ,4BAA4B,CAAC;AAE3C,UAAM,QAAQ,YAAY,KAAK,IAC3B,iCAAiC,KAAK,IACtC,sBAAsB,aAAa,OAAO,QAAW,kBAAkB,IAAI;AAE/E,UAAM,QAAQ;AAEd,iBAAa,OAAO;MAClB,mBAAmB;MACnB,WAAW;QACT,SAAS;QACT,MAAM;MACd;IACA,CAAK;EACL,CAAG;AACH;AAEA,SAAS,4BAA4B,OAAO;AAC1C,MAAI,YAAY,KAAK,GAAG;AACtB,WAAO;EACX;AAGE,MAAI;AAIF,QAAI,YAAa,OAAS;AACxB,aAAQ,MAAQ;IACtB;AAOI,QAAI,YAAa,SAAW,YAAa,MAAQ,QAAQ;AACvD,aAAQ,MAAQ,OAAO;IAC7B;EACA,QAAU;EAAA;AAER,SAAO;AACT;AAQA,SAAS,iCAAiC,QAAQ;AAChD,SAAO;IACL,WAAW;MACT,QAAQ;QACN;UACE,MAAM;;UAEN,OAAO,oDAAoD,OAAO,MAAM,CAAC;QACnF;MACA;IACA;EACA;AACA;AAEA,SAAS,8BACP,OACA,KACA,MACA,QACA;AAEA,QAAM,IAAK,MAAM,YAAY,MAAM,aAAa,CAAA;AAEhD,QAAM,KAAM,EAAE,SAAS,EAAE,UAAU,CAAA;AAEnC,QAAM,MAAO,GAAG,CAAC,IAAI,GAAG,CAAC,KAAK,CAAA;AAE9B,QAAM,OAAQ,IAAI,aAAa,IAAI,cAAc,CAAA;AAEjD,QAAM,QAAS,KAAK,SAAS,KAAK,UAAU,CAAA;AAE5C,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,QAAM,WAAW,SAAS,GAAG,KAAK,IAAI,SAAS,IAAI,MAAM,gBAAe;AAGxE,MAAI,MAAM,WAAW,GAAG;AACtB,UAAM,KAAK;MACT;MACA;MACA,UAAU;MACV,QAAQ;MACR;IACN,CAAK;EACL;AAEE,SAAO;AACT;AAEA,SAAS,iBAAiB,MAAM;AAC9B,EAAAJ,gBAAe,OAAO,IAAI,4BAA4B,IAAI,EAAE;AAC9D;AAEA,SAAS,aAAa;AACpB,QAAM,SAAS,UAAS;AACxB,QAAM,WAAU,iCAAQ,iBAAgB;IACtC,aAAa,MAAM,CAAA;IACnB,kBAAkB;EACtB;AACE,SAAO;AACT;AC/KA,IAAM,yBAAyB,kBAAkB,MAAM;AACrD,SAAO;IACL,MAAM;IACN,gBAAgB,OAAO;;AAErB,UAAI,CAACQ,SAAO,aAAa,CAACA,SAAO,YAAY,CAACA,SAAO,UAAU;AAC7D;MACR;AAEM,YAAM,UAAU,mBAAkB;AAClC,YAAM,UAAU;QACd,GAAG,QAAQ;QACX,IAAG,WAAM,YAAN,mBAAe;MAC1B;AAEM,YAAM,UAAU;QACd,GAAG;QACH,GAAG,MAAM;QACT;MACR;IACA;EACA;AACA,CAAC;AC1BD,IAAM,cAAc;AACpB,IAAM,gBAAgB;AAEtB,IAAM,mBAAmB;AAEzB,IAAM,2BAA4B,CAAC,UAAU,CAAA,MAAO;AAClD,QAAM,QAAQ,QAAQ,SAAS;AAC/B,QAAM,MAAM,QAAQ,OAAO;AAE3B,SAAO;IACL,MAAM;IACN,gBAAgB,OAAO,MAAM,QAAQ;AACnC,YAAMK,WAAU,OAAO,WAAU;AAEjC;;QAEE;QACAA,SAAQ;QACR;QACA;QACA;QACA;MACR;IACA;EACA;AACA;AAKA,IAAM,0BAA0B,kBAAkB,wBAAwB;ACnB1E,SAAS,uBAAuB,UAAU;AAKxC,SAAO;;;IAGL,0BAAyB;IACzB,4BAA2B;IAC3B,4BAA2B;IAC3B,uBAAsB;IACtB,0BAAyB;IACzB,wBAAuB;IACvB,kBAAiB;IACjB,uBAAsB;IACtB,0BAAyB;EAC7B;AACA;AAGA,SAAS,oBAAoB,aAAa,CAAA,GAAI;;AAC5C,QAAM,iBAAiB;IACrB,qBAAqB,uBAAsB;IAC3C,SACE,OAAO,uBAAuB,WAC1B,sBACAL,cAAO,mBAAPA,mBAAuB;;IAC7B,mBAAmB;EACvB;AAEE,SAAO;IACL,GAAG;IACH,GAAG,0BAA0B,UAAU;EAC3C;AACA;AAMA,SAAS,0BAA0B,KAAK;AACtC,QAAM,eAAe,CAAA;AAErB,aAAW,KAAK,OAAO,oBAAoB,GAAG,GAAG;AAC/C,UAAM,MAAM;AACZ,QAAI,IAAI,GAAG,MAAM,QAAW;AAC1B,mBAAa,GAAG,IAAI,IAAI,GAAG;IACjC;EACA;AAEE,SAAO;AACT;AAgDA,SAAS,KAAK,iBAAiB,CAAA,GAAI;AACjC,MAAI,CAAC,eAAe,6BAA6B,0BAAyB,GAAI;AAC5E;EACJ;AAEE,QAAM,UAAU,oBAAoB,cAAc;AAClD,QAAM,gBAAgB;IACpB,GAAG;IACH,aAAa,kCAAkC,QAAQ,eAAe,kBAAkB;IACxF,cAAc,uBAAuB,OAAO;IAC5C,WAAW,QAAQ,aAAa;EACpC;AAEE,SAAO,YAAY,eAAe,aAAa;AACjD;AAkBA,SAAS,8BAA8B;;AACrC,MAAI,OAAOA,SAAO,WAAW,aAAa;AAExC,WAAO;EACX;AAEE,QAAM,UAAUA;AAIhB,MAAI,QAAQ,IAAI;AACd,WAAO;EACX;AAEE,QAAM,kBAAkB,QAAQ,QAAQ,KAAK,QAAQ,SAAS;AAE9D,MAAI,GAAC,wDAAiB,YAAjB,mBAA0B,KAAI;AACjC,WAAO;EACX;AAEE,QAAM,OAAO,gBAAe;AAC5B,QAAM,qBAAqB,CAAC,oBAAoB,iBAAiB,wBAAwB,sBAAsB;AAG/G,QAAM,2BACJA,aAAWA,SAAO,OAAO,mBAAmB,KAAK,cAAY,KAAK,WAAW,GAAG,QAAQ,KAAK,CAAC;AAEhG,SAAO,CAAC;AACV;AAEA,SAAS,4BAA4B;AACnC,MAAI,4BAA2B,GAAI;AACjC,QAAIR,cAAa;AACf,qBAAe,MAAM;AAEnB,gBAAQ;UACN;QACV;MACA,CAAO;IACP;AAEI,WAAO;EACX;AACA;AC3Le,eAAA,kBAAK;AAChB,SAAO,MAAM,IAAI,QAAQ,CAAC,YAAW;AACjCc,SAAY;MACR,KAAK;MACL,cAAc,CAAA;MACd,WAAW,OAAK;AACZ,YAAI,MAAM,aAAa,MAAM,UAAU,QAAQ;AAC3C,gBAAM,aAAa,MAAM,UAAU,OAAO,CAAC;AAC3C,cAAI,WAAW,cAAc,WAAW,WAAW,QAAQ;AACvD,kBAAM,YAAY,WAAW,WAAW,OAAO,KAC3C,WAAS,MAAM,YAAY,MAAM,SAAS,SAAS,MAAM,CAAC;AAE9D,gBAAI,CAAC,WAAW;AACZ,qBAAO;;;;AAInB,eAAO;;MAEX,kBAAkB;IACrB,CAAA;AACD,YAAQ,CAAA,CAAE;EACd,CAAC;AACL;ACxBY,IAAC,gBAAgB;;;ACEjB,IAAC,uBAAuB,OAAO,KAAK,YAAY;AAC1D,MAAI,OAAO,WAAW;AAAa,WAAO;AAC1C,QAAM,cAAa;AACnB,SAAO,cAAc,KAAA,MAAA,s6ZAAsC,GAAA,OAAA;AAC7D;;;CCPC,WAAU;AAAC,MAAG,gBAAc,OAAO,UAAQ,WAAS,OAAO,WAAS,WAAS,OAAO,gBAAe;AAAC,QAAI,IAAE;AAAY,WAAO,cAAY,WAAU;AAAC,aAAO,QAAQ,UAAU,GAAE,CAAC,GAAE,KAAK,WAAW;AAAA,IAAC;AAAE,gBAAY,YAAU,EAAE;AAAU,gBAAY,UAAU,cAAY;AAAY,WAAO,eAAe,aAAY,CAAC;AAAA,EAAC;AAAC,GAAG;", "names": ["DEBUG_BUILD", "isErrorEvent", "isError", "name", "INTEGRATION_NAME", "_shouldDropEvent", "_a", "_b", "WINDOW", "url", "fn", "handlers", "line", "options", "Sentry.init"]}