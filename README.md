# AI招聘助手演示平台

智能AI招聘功能演示站点，展示完整的智能招聘解决方案。

## 🚀 功能特性

本演示平台集成了AI招聘系统的所有核心功能：

### 求职者服务
- **🎯 职业规划助手** - AI智能职业规划建议，制定个人发展路径
- **🎭 模拟面试系统** - 真实面试场景模拟，提供专业反馈

### HR工具
- **📄 简历匹配分析** - 智能分析简历与职位匹配度
- **📝 职位生成器** - AI智能生成职位描述
- **📊 面试评估报告** - 详细的面试评估和建议
- **❓ 模拟出题大师** - 智能生成面试题目
- **🔄 千岗千简历** - 批量处理简历和职位匹配
- **💬 HR智能助手** - 专业HR咨询服务

### 办公工具
- **🛡️ 劳动合同卫士** - 智能审查合同条款
- **📋 会议总结助手** - 自动生成会议纪要

## 🛠️ 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **AI组件**: pcm-agents
- **UI组件**: pcm-agents-vue
- **样式**: CSS3 + PC端优化设计

## 📦 项目设置

### 安装依赖

```bash
pnpm install
```

### 开发环境运行

```bash
pnpm dev
```

### 生产环境构建

```bash
pnpm build
```

## 🔑 系统配置

本演示使用的系统密钥：
- **Secret ID (AK)**: `ak-BMSZyMnACKX6MPNne9zPfdFA`
- **Secret Key (SK)**: `sk-qrFpGRrDiAcu0YSUirhOVgPBT83qusb5`
- **User ID**: `76015687511834624`

### Token获取机制

应用启动时会自动尝试从API获取访问token：

1. **自动获取**: 使用配置的AK、SK、UserID向API请求token
2. **多端点尝试**: 自动尝试多个可能的API端点
3. **备用方案**: 如果API不可用，使用备用token格式
4. **状态显示**: 界面会显示token获取状态和结果

## 🎨 界面预览

![演示界面](image/714416A-534C-4981-9615-99D316474533.png)

## 📱 响应式设计

- ✅ 桌面端优化
- ✅ 平板端适配
- ✅ 移动端友好

## 🏢 版权信息

© 2024 上海未软人工智能公司 版权所有

技术支持：AI招聘解决方案提供商

## 📞 联系我们

如需了解更多信息或技术支持，请联系：
- 官网：[聘才猫官网]
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx

---

*本项目为AI招聘系统功能演示，展示AI在招聘领域的应用潜力。*


