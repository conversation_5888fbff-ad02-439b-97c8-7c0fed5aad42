{"version": 3, "sources": ["../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/src/utils/env.ts", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@stencil+store@2.1.3_@stencil+core@4.31.0/node_modules/@stencil/store/dist/index.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/store/auth.store.ts", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/store/config.store.ts", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/src/utils/error-event.ts", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/src/utils/sentry-reporter.ts"], "sourcesContent": ["// 默认 API 域名\r\nconst DEFAULT_API_DOMAIN = 'https://api.pincaimao.com/agents/platform';\r\n\r\n// 从环境变量获取 API 域名，如果未设置则使用默认值\r\nexport const API_DOMAIN = process.env.API_DOMAIN || DEFAULT_API_DOMAIN;\r\n\r\n// 导出其他环境变量\r\nexport const ENV = {\r\n  API_DOMAIN,\r\n  // 可以添加其他环境变量\r\n}; ", "import { getRenderingRef, forceUpdate } from '@stencil/core';\n\nconst appendToMap = (map, propName, value) => {\n    const items = map.get(propName);\n    if (!items) {\n        map.set(propName, [value]);\n    }\n    else if (!items.includes(value)) {\n        items.push(value);\n    }\n};\nconst debounce = (fn, ms) => {\n    let timeoutId;\n    return (...args) => {\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        timeoutId = setTimeout(() => {\n            timeoutId = 0;\n            fn(...args);\n        }, ms);\n    };\n};\n\n/**\n * Check if a possible element isConnected.\n * The property might not be there, so we check for it.\n *\n * We want it to return true if isConnected is not a property,\n * otherwise we would remove these elements and would not update.\n *\n * Better leak in Edge than to be useless.\n */\nconst isConnected = (maybeElement) => !('isConnected' in maybeElement) || maybeElement.isConnected;\nconst cleanupElements = debounce((map) => {\n    for (let key of map.keys()) {\n        map.set(key, map.get(key).filter(isConnected));\n    }\n}, 2_000);\nconst stencilSubscription = () => {\n    if (typeof getRenderingRef !== 'function') {\n        // If we are not in a stencil project, we do nothing.\n        // This function is not really exported by @stencil/core.\n        return {};\n    }\n    const elmsToUpdate = new Map();\n    return {\n        dispose: () => elmsToUpdate.clear(),\n        get: (propName) => {\n            const elm = getRenderingRef();\n            if (elm) {\n                appendToMap(elmsToUpdate, propName, elm);\n            }\n        },\n        set: (propName) => {\n            const elements = elmsToUpdate.get(propName);\n            if (elements) {\n                elmsToUpdate.set(propName, elements.filter(forceUpdate));\n            }\n            cleanupElements(elmsToUpdate);\n        },\n        reset: () => {\n            elmsToUpdate.forEach((elms) => elms.forEach(forceUpdate));\n            cleanupElements(elmsToUpdate);\n        },\n    };\n};\n\nconst unwrap = (val) => (typeof val === 'function' ? val() : val);\nconst createObservableMap = (defaultState, shouldUpdate = (a, b) => a !== b) => {\n    const unwrappedState = unwrap(defaultState);\n    let states = new Map(Object.entries(unwrappedState ?? {}));\n    const handlers = {\n        dispose: [],\n        get: [],\n        set: [],\n        reset: [],\n    };\n    const reset = () => {\n        // When resetting the state, the default state may be a function - unwrap it to invoke it.\n        // otherwise, the state won't be properly reset\n        states = new Map(Object.entries(unwrap(defaultState) ?? {}));\n        handlers.reset.forEach((cb) => cb());\n    };\n    const dispose = () => {\n        // Call first dispose as resetting the state would\n        // cause less updates ;)\n        handlers.dispose.forEach((cb) => cb());\n        reset();\n    };\n    const get = (propName) => {\n        handlers.get.forEach((cb) => cb(propName));\n        return states.get(propName);\n    };\n    const set = (propName, value) => {\n        const oldValue = states.get(propName);\n        if (shouldUpdate(value, oldValue, propName)) {\n            states.set(propName, value);\n            handlers.set.forEach((cb) => cb(propName, value, oldValue));\n        }\n    };\n    const state = (typeof Proxy === 'undefined'\n        ? {}\n        : new Proxy(unwrappedState, {\n            get(_, propName) {\n                return get(propName);\n            },\n            ownKeys(_) {\n                return Array.from(states.keys());\n            },\n            getOwnPropertyDescriptor() {\n                return {\n                    enumerable: true,\n                    configurable: true,\n                };\n            },\n            has(_, propName) {\n                return states.has(propName);\n            },\n            set(_, propName, value) {\n                set(propName, value);\n                return true;\n            },\n        }));\n    const on = (eventName, callback) => {\n        handlers[eventName].push(callback);\n        return () => {\n            removeFromArray(handlers[eventName], callback);\n        };\n    };\n    const onChange = (propName, cb) => {\n        const unSet = on('set', (key, newValue) => {\n            if (key === propName) {\n                cb(newValue);\n            }\n        });\n        // We need to unwrap the defaultState because it might be a function.\n        // Otherwise we might not be sending the right reset value.\n        const unReset = on('reset', () => cb(unwrap(defaultState)[propName]));\n        return () => {\n            unSet();\n            unReset();\n        };\n    };\n    const use = (...subscriptions) => {\n        const unsubs = subscriptions.reduce((unsubs, subscription) => {\n            if (subscription.set) {\n                unsubs.push(on('set', subscription.set));\n            }\n            if (subscription.get) {\n                unsubs.push(on('get', subscription.get));\n            }\n            if (subscription.reset) {\n                unsubs.push(on('reset', subscription.reset));\n            }\n            if (subscription.dispose) {\n                unsubs.push(on('dispose', subscription.dispose));\n            }\n            return unsubs;\n        }, []);\n        return () => unsubs.forEach((unsub) => unsub());\n    };\n    const forceUpdate = (key) => {\n        const oldValue = states.get(key);\n        handlers.set.forEach((cb) => cb(key, oldValue, oldValue));\n    };\n    return {\n        state,\n        get,\n        set,\n        on,\n        onChange,\n        use,\n        dispose,\n        reset,\n        forceUpdate,\n    };\n};\nconst removeFromArray = (array, item) => {\n    const index = array.indexOf(item);\n    if (index >= 0) {\n        array[index] = array[array.length - 1];\n        array.length--;\n    }\n};\n\nconst createStore = (defaultState, shouldUpdate) => {\n    const map = createObservableMap(defaultState, shouldUpdate);\n    map.use(stencilSubscription());\n    return map;\n};\n\nexport { createObservableMap, createStore };\n", "// src/store/auth.store.ts\r\nimport { createStore } from '@stencil/store';\r\n\r\nexport const { state: authState, onChange } = createStore({\r\n  token: localStorage.getItem('pcm-sdk-auth-token') || null\r\n});\r\n\r\n// 添加一些辅助方法\r\nexport const authStore = {\r\n  getToken: () => authState.token,\r\n  setToken: (token: string) => {\r\n    authState.token = token;\r\n    localStorage.setItem('pcm-sdk-auth-token', token);\r\n  },\r\n  clearToken: () => {\r\n    authState.token = null;\r\n    localStorage.removeItem('pcm-sdk-auth-token');\r\n  }\r\n};\r\n\r\n// 自动保存到localStorage\r\nonChange('token', value => {\r\n  if (value) {\r\n    localStorage.setItem('pcm-sdk-auth-token', value);\r\n  } else {\r\n    localStorage.removeItem('pcm-sdk-auth-token');\r\n  }\r\n});", "import { createStore } from '@stencil/store';\r\n\r\n// 定义配置数据的类型\r\nexport interface ConfigData {\r\n  [key: string]: any;\r\n}\r\n\r\n// 从localStorage获取初始配置\r\nconst getInitialConfig = (): ConfigData => {\r\n  try {\r\n    const storedConfig = localStorage.getItem('pcm-sdk-config-data');\r\n    return storedConfig ? JSON.parse(storedConfig) : {};\r\n  } catch (error) {\r\n    console.error('Error parsing stored config:', error);\r\n    return {};\r\n  }\r\n};\r\n\r\n// 创建配置存储\r\nexport const { state: configState, onChange } = createStore<{\r\n  data: ConfigData;\r\n}>({\r\n  data: getInitialConfig()\r\n});\r\n\r\n// 配置存储的辅助方法\r\nexport const configStore = {\r\n  // 获取整个配置对象\r\n  getConfig: (): ConfigData => configState.data,\r\n  \r\n  // 获取特定配置项\r\n  getItem: <T>(key: string, defaultValue?: T): T => {\r\n    return configState.data[key] !== undefined ? configState.data[key] : defaultValue;\r\n  },\r\n  \r\n  // 设置特定配置项\r\n  setItem: <T>(key: string, value: T): void => {\r\n    configState.data = {\r\n      ...configState.data,\r\n      [key]: value\r\n    };\r\n  },\r\n  \r\n  // 移除特定配置项\r\n  removeItem: (key: string): void => {\r\n    const newConfig = { ...configState.data };\r\n    delete newConfig[key];\r\n    configState.data = newConfig;\r\n  },\r\n  \r\n  // 清除所有配置\r\n  clear: (): void => {\r\n    configState.data = {};\r\n  },\r\n  \r\n  // 批量更新配置\r\n  updateConfig: (newConfig: Partial<ConfigData>): void => {\r\n    configState.data = {\r\n      ...configState.data,\r\n      ...newConfig\r\n    };\r\n  }\r\n};\r\n\r\n// 自动保存到localStorage\r\nonChange('data', value => {\r\n  try {\r\n    if (Object.keys(value).length > 0) {\r\n      localStorage.setItem('pcm-sdk-config-data', JSON.stringify(value));\r\n    } else {\r\n      localStorage.removeItem('pcm-sdk-config-data');\r\n    }\r\n  } catch (error) {\r\n    console.error('Error saving config to localStorage:', error);\r\n  }\r\n}); ", "export interface ErrorEventDetail {\r\n  error: any;\r\n  message: string;\r\n}\r\n\r\nexport class ErrorEventBus {\r\n  /**\r\n   * 触发错误事件\r\n   */\r\n  static emitError(detail: ErrorEventDetail): void {\r\n    const event = new CustomEvent('pcm-error', {\r\n      bubbles: true,\r\n      composed: true,\r\n      detail\r\n    });\r\n    document.dispatchEvent(event);\r\n  }\r\n\r\n  /**\r\n   * 添加错误事件监听器\r\n   */\r\n  static addErrorListener(callback: (detail: ErrorEventDetail) => void): () => void {\r\n    const handler = (event: CustomEvent<ErrorEventDetail>) => {\r\n      callback(event.detail);\r\n    };\r\n    \r\n    document.addEventListener('pcm-error', handler as EventListener);\r\n    \r\n    // 返回移除监听器的函数\r\n    return () => {\r\n      document.removeEventListener('pcm-error', handler as EventListener);\r\n    };\r\n  }\r\n} ", "import * as Sentry from \"@sentry/browser\";\r\nimport { configStore } from \"../../store/config.store\";\r\n\r\n/**\r\n * Sentry事件上报工具类\r\n * 提供统一的错误上报接口，自动添加用户认证信息\r\n */\r\nexport class SentryReporter {\r\n\r\n  /**\r\n   * 捕获并上报错误\r\n   * @param error 错误对象\r\n   * @param context 错误上下文\r\n   */\r\n  static captureError(error: any, context?: Record<string, any> & { title?: string }): void {\r\n    try {\r\n      // 添加认证信息\r\n      const CUser = configStore.getItem('pcm-sdk-CUser');\r\n      if (CUser) {\r\n        Sentry.setUser({ id: String(CUser) });\r\n      }\r\n      \r\n      let finalError = error;\r\n      \r\n      // 如果提供了自定义标题，创建新的错误对象\r\n      if (context?.title) {\r\n        finalError = new Error(context.title);\r\n        if (error?.stack) {\r\n          finalError.stack = error.stack;\r\n        }\r\n        finalError.cause = error;\r\n      }\r\n      \r\n      // 添加上下文\r\n      if (context) {\r\n        const { title, ...otherContext } = context;\r\n        Sentry.setContext('error_context', otherContext);\r\n      }\r\n      \r\n      // 上报错误\r\n      Sentry.captureException(finalError);\r\n    } catch (e) {\r\n      console.error('Sentry 上报错误失败:', e);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 捕获并上报消息\r\n   * @param message 消息内容\r\n   * @param context 消息上下文\r\n   */\r\n  static captureMessage(message: string, context?: Record<string, any>): void {\r\n    try {\r\n      // 添加认证信息\r\n      const CUser = configStore.getItem('pcm-sdk-CUser');\r\n      if (CUser) {\r\n        Sentry.setUser({ id: String(CUser) });\r\n      }\r\n      \r\n      // 添加上下文\r\n      if (context) {\r\n        Sentry.setContext('message_context', context);\r\n      }\r\n      \r\n      // 上报消息\r\n      Sentry.captureMessage(message);\r\n    } catch (e) {\r\n      console.error('Sentry 上报消息失败:', e);\r\n    }\r\n  }\r\n\r\n}"], "mappings": ";;;;;;;;;;;;AAIO,IAAM,aAAa;ACF1B,IAAM,cAAc,CAAC,KAAK,UAAU,UAAU;AAC1C,QAAM,QAAQ,IAAI,IAAI,QAAQ;AAC9B,MAAI,CAAC,OAAO;AACR,QAAI,IAAI,UAAU,CAAC,KAAK,CAAC;EACjC,WACa,CAAC,MAAM,SAAS,KAAK,GAAG;AAC7B,UAAM,KAAK,KAAK;EACxB;AACA;AACA,IAAM,WAAW,CAAC,IAAI,OAAO;AACzB,MAAI;AACJ,SAAO,IAAI,SAAS;AAChB,QAAI,WAAW;AACX,mBAAa,SAAS;IAClC;AACQ,gBAAY,WAAW,MAAM;AACzB,kBAAY;AACZ,SAAG,GAAG,IAAI;IACtB,GAAW,EAAE;EACb;AACA;AAWA,IAAM,cAAc,CAAC,iBAAiB,EAAE,iBAAiB,iBAAiB,aAAa;AACvF,IAAM,kBAAkB,SAAS,CAAC,QAAQ;AACtC,WAAS,OAAO,IAAI,KAAI,GAAI;AACxB,QAAI,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,OAAO,WAAW,CAAC;EACrD;AACA,GAAG,GAAK;AACR,IAAM,sBAAsB,MAAM;AAC9B,MAAI,OAAO,oBAAoB,YAAY;AAGvC,WAAO,CAAA;EACf;AACI,QAAM,eAAe,oBAAI,IAAG;AAC5B,SAAO;IACH,SAAS,MAAM,aAAa,MAAK;IACjC,KAAK,CAAC,aAAa;AACf,YAAM,MAAM,gBAAe;AAC3B,UAAI,KAAK;AACL,oBAAY,cAAc,UAAU,GAAG;MACvD;IACA;IACQ,KAAK,CAAC,aAAa;AACf,YAAM,WAAW,aAAa,IAAI,QAAQ;AAC1C,UAAI,UAAU;AACV,qBAAa,IAAI,UAAU,SAAS,OAAO,WAAW,CAAC;MACvE;AACY,sBAAgB,YAAY;IACxC;IACQ,OAAO,MAAM;AACT,mBAAa,QAAQ,CAAC,SAAS,KAAK,QAAQ,WAAW,CAAC;AACxD,sBAAgB,YAAY;IACxC;EACA;AACA;AAEA,IAAM,SAAS,CAAC,QAAS,OAAO,QAAQ,aAAa,IAAG,IAAK;AAC7D,IAAM,sBAAsB,CAAC,cAAc,eAAe,CAAC,GAAG,MAAM,MAAM,MAAM;AAC5E,QAAM,iBAAiB,OAAO,YAAY;AAC1C,MAAI,SAAS,IAAI,IAAI,OAAO,QAAQ,kBAAkB,CAAA,CAAE,CAAC;AACzD,QAAM,WAAW;IACb,SAAS,CAAA;IACT,KAAK,CAAA;IACL,KAAK,CAAA;IACL,OAAO,CAAA;EACf;AACI,QAAM,QAAQ,MAAM;AAGhB,aAAS,IAAI,IAAI,OAAO,QAAQ,OAAO,YAAY,KAAK,CAAA,CAAE,CAAC;AAC3D,aAAS,MAAM,QAAQ,CAAC,OAAO,GAAE,CAAE;EAC3C;AACI,QAAM,UAAU,MAAM;AAGlB,aAAS,QAAQ,QAAQ,CAAC,OAAO,GAAE,CAAE;AACrC,UAAK;EACb;AACI,QAAM,MAAM,CAAC,aAAa;AACtB,aAAS,IAAI,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC;AACzC,WAAO,OAAO,IAAI,QAAQ;EAClC;AACI,QAAM,MAAM,CAAC,UAAU,UAAU;AAC7B,UAAM,WAAW,OAAO,IAAI,QAAQ;AACpC,QAAI,aAAa,OAAO,UAAU,QAAQ,GAAG;AACzC,aAAO,IAAI,UAAU,KAAK;AAC1B,eAAS,IAAI,QAAQ,CAAC,OAAO,GAAG,UAAU,OAAO,QAAQ,CAAC;IACtE;EACA;AACI,QAAM,QAAS,OAAO,UAAU,cAC1B,CAAA,IACA,IAAI,MAAM,gBAAgB;IACxB,IAAI,GAAG,UAAU;AACb,aAAO,IAAI,QAAQ;IACnC;IACY,QAAQ,GAAG;AACP,aAAO,MAAM,KAAK,OAAO,KAAI,CAAE;IAC/C;IACY,2BAA2B;AACvB,aAAO;QACH,YAAY;QACZ,cAAc;MAClC;IACA;IACY,IAAI,GAAG,UAAU;AACb,aAAO,OAAO,IAAI,QAAQ;IAC1C;IACY,IAAI,GAAG,UAAU,OAAO;AACpB,UAAI,UAAU,KAAK;AACnB,aAAO;IACvB;EACA,CAAS;AACL,QAAM,KAAK,CAAC,WAAW,aAAa;AAChC,aAAS,SAAS,EAAE,KAAK,QAAQ;AACjC,WAAO,MAAM;AACT,sBAAgB,SAAS,SAAS,GAAG,QAAQ;IACzD;EACA;AACI,QAAMA,YAAW,CAAC,UAAU,OAAO;AAC/B,UAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,aAAa;AACvC,UAAI,QAAQ,UAAU;AAClB,WAAG,QAAQ;MAC3B;IACA,CAAS;AAGD,UAAM,UAAU,GAAG,SAAS,MAAM,GAAG,OAAO,YAAY,EAAE,QAAQ,CAAC,CAAC;AACpE,WAAO,MAAM;AACT,YAAK;AACL,cAAO;IACnB;EACA;AACI,QAAM,MAAM,IAAI,kBAAkB;AAC9B,UAAM,SAAS,cAAc,OAAO,CAACC,SAAQ,iBAAiB;AAC1D,UAAI,aAAa,KAAK;AAClB,QAAAA,QAAO,KAAK,GAAG,OAAO,aAAa,GAAG,CAAC;MACvD;AACY,UAAI,aAAa,KAAK;AAClB,QAAAA,QAAO,KAAK,GAAG,OAAO,aAAa,GAAG,CAAC;MACvD;AACY,UAAI,aAAa,OAAO;AACpB,QAAAA,QAAO,KAAK,GAAG,SAAS,aAAa,KAAK,CAAC;MAC3D;AACY,UAAI,aAAa,SAAS;AACtB,QAAAA,QAAO,KAAK,GAAG,WAAW,aAAa,OAAO,CAAC;MAC/D;AACY,aAAOA;IACnB,GAAW,CAAA,CAAE;AACL,WAAO,MAAM,OAAO,QAAQ,CAAC,UAAU,MAAK,CAAE;EACtD;AACI,QAAMC,eAAc,CAAC,QAAQ;AACzB,UAAM,WAAW,OAAO,IAAI,GAAG;AAC/B,aAAS,IAAI,QAAQ,CAAC,OAAO,GAAG,KAAK,UAAU,QAAQ,CAAC;EAChE;AACI,SAAO;IACH;IACA;IACA;IACA;IACA,UAAAF;IACA;IACA;IACA;IACA,aAAAE;EACR;AACA;AACA,IAAM,kBAAkB,CAAC,OAAO,SAAS;AACrC,QAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,MAAI,SAAS,GAAG;AACZ,UAAM,KAAK,IAAI,MAAM,MAAM,SAAS,CAAC;AACrC,UAAM;EACd;AACA;AAEA,IAAM,cAAc,CAAC,cAAc,iBAAiB;AAChD,QAAM,MAAM,oBAAoB,cAAc,YAAY;AAC1D,MAAI,IAAI,oBAAmB,CAAE;AAC7B,SAAO;AACX;AC3LO,IAAM,EAAE,OAAO,WAAS,UAAEF,WAAQ,IAAK,YAAY;EACxD,OAAO,aAAa,QAAQ,oBAAoB,KAAK;AACtD,CAAA;AAGY,IAAA,YAAY;EACvB,UAAU,MAAM,UAAU;EAC1B,UAAU,CAAC,UAAiB;AAC1B,cAAU,QAAQ;AAClB,iBAAa,QAAQ,sBAAsB,KAAK;;EAElD,YAAY,MAAK;AACf,cAAU,QAAQ;AAClB,iBAAa,WAAW,oBAAoB;;;AAKhDA,WAAS,SAAS,WAAQ;AACxB,MAAI,OAAO;AACT,iBAAa,QAAQ,sBAAsB,KAAK;SAC3C;AACL,iBAAa,WAAW,oBAAoB;;AAEhD,CAAC;ACnBD,IAAM,mBAAmB,MAAiB;AACxC,MAAI;AACF,UAAM,eAAe,aAAa,QAAQ,qBAAqB;AAC/D,WAAO,eAAe,KAAK,MAAM,YAAY,IAAI,CAAA;WAC1C,OAAO;AACd,YAAQ,MAAM,gCAAgC,KAAK;AACnD,WAAO,CAAA;;AAEX;AAGO,IAAM,EAAE,OAAO,aAAa,SAAQ,IAAK,YAE7C;EACD,MAAM,iBAAgB;AACvB,CAAA;AAGY,IAAA,cAAc;;EAEzB,WAAW,MAAkB,YAAY;;EAGzC,SAAS,CAAI,KAAa,iBAAuB;AAC/C,WAAO,YAAY,KAAK,GAAG,MAAM,SAAY,YAAY,KAAK,GAAG,IAAI;;;EAIvE,SAAS,CAAI,KAAa,UAAkB;AAC1C,gBAAY,OAAO;MACjB,GAAG,YAAY;MACf,CAAC,GAAG,GAAG;;;;EAKX,YAAY,CAAC,QAAqB;AAChC,UAAM,YAAY,EAAE,GAAG,YAAY,KAAI;AACvC,WAAO,UAAU,GAAG;AACpB,gBAAY,OAAO;;;EAIrB,OAAO,MAAW;AAChB,gBAAY,OAAO,CAAA;;;EAIrB,cAAc,CAAC,cAAwC;AACrD,gBAAY,OAAO;MACjB,GAAG,YAAY;MACf,GAAG;;;;AAMT,SAAS,QAAQ,WAAQ;AACvB,MAAI;AACF,QAAI,OAAO,KAAK,KAAK,EAAE,SAAS,GAAG;AACjC,mBAAa,QAAQ,uBAAuB,KAAK,UAAU,KAAK,CAAC;WAC5D;AACL,mBAAa,WAAW,qBAAqB;;WAExC,OAAO;AACd,YAAQ,MAAM,wCAAwC,KAAK;;AAE/D,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICtEY,sBAAa;;;;EAIxB,OAAO,UAAU,QAAwB;AACvC,UAAM,QAAQ,IAAI,YAAY,aAAa;MACzC,SAAS;MACT,UAAU;MACV;IACD,CAAA;AACD,aAAS,cAAc,KAAK;;;;;EAM9B,OAAO,iBAAiB,UAA4C;AAClE,UAAM,UAAU,CAAC,UAAwC;AACvD,eAAS,MAAM,MAAM;IACvB;AAEA,aAAS,iBAAiB,aAAa,OAAwB;AAG/D,WAAO,MAAK;AACV,eAAS,oBAAoB,aAAa,OAAwB;IACpE;;AAEH;IC1BY,uBAAc;;;;;;EAOzB,OAAO,aAAa,OAAY,SAAkD;AAChF,QAAI;AAEF,YAAM,QAAQ,YAAY,QAAQ,eAAe;AACjD,UAAI,OAAO;AACTG,gBAAe,EAAE,IAAI,OAAO,KAAK,EAAC,CAAE;;AAGtC,UAAI,aAAa;AAGjB,UAAI,mCAAS,OAAO;AAClB,qBAAa,IAAI,MAAM,QAAQ,KAAK;AACpC,YAAI,+BAAO,OAAO;AAChB,qBAAW,QAAQ,MAAM;;AAE3B,mBAAW,QAAQ;;AAIrB,UAAI,SAAS;AACX,cAAM,EAAE,OAAO,GAAG,aAAY,IAAK;AACnCC,mBAAkB,iBAAiB,YAAY;;AAIjDC,uBAAwB,UAAU;aAC3B,GAAG;AACV,cAAQ,MAAM,kBAAkB,CAAC;;;;;;;;EASrC,OAAO,eAAe,SAAiB,SAA6B;AAClE,QAAI;AAEF,YAAM,QAAQ,YAAY,QAAQ,eAAe;AACjD,UAAI,OAAO;AACTF,gBAAe,EAAE,IAAI,OAAO,KAAK,EAAC,CAAE;;AAItC,UAAI,SAAS;AACXC,mBAAkB,mBAAmB,OAAO;;AAI9CE,qBAAsB,OAAO;aACtB,GAAG;AACV,cAAQ,MAAM,kBAAkB,CAAC;;;AAItC;", "names": ["onChange", "unsubs", "forceUpdate", "Sentry.setUser", "Sentry.setContext", "Sentry.captureException", "Sentry.captureMessage"]}