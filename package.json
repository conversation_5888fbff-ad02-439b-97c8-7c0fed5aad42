{"name": "ai-recruitment-demo", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"pcm-agents": "0.6.5", "pcm-agents-vue": "^0.2.6", "vue": "^3.2.37"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^22.13.9", "@vitejs/plugin-vue": "^4.2.1", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^4.4.5", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}