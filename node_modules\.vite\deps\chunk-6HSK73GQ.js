import {
  captureException,
  captureMessage,
  setContext,
  setUser
} from "./chunk-CTIEEM75.js";
import {
  forceUpdate,
  getRenderingRef
} from "./chunk-SPDCWX4H.js";

// node_modules/.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/index-B6Ew8Ryg.js
var API_DOMAIN = "https://api.pincaimao.com/agents/platform";
var appendToMap = (map, propName, value) => {
  const items = map.get(propName);
  if (!items) {
    map.set(propName, [value]);
  } else if (!items.includes(value)) {
    items.push(value);
  }
};
var debounce = (fn, ms) => {
  let timeoutId;
  return (...args) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      timeoutId = 0;
      fn(...args);
    }, ms);
  };
};
var isConnected = (maybeElement) => !("isConnected" in maybeElement) || maybeElement.isConnected;
var cleanupElements = debounce((map) => {
  for (let key of map.keys()) {
    map.set(key, map.get(key).filter(isConnected));
  }
}, 2e3);
var stencilSubscription = () => {
  if (typeof getRenderingRef !== "function") {
    return {};
  }
  const elmsToUpdate = /* @__PURE__ */ new Map();
  return {
    dispose: () => elmsToUpdate.clear(),
    get: (propName) => {
      const elm = getRenderingRef();
      if (elm) {
        appendToMap(elmsToUpdate, propName, elm);
      }
    },
    set: (propName) => {
      const elements = elmsToUpdate.get(propName);
      if (elements) {
        elmsToUpdate.set(propName, elements.filter(forceUpdate));
      }
      cleanupElements(elmsToUpdate);
    },
    reset: () => {
      elmsToUpdate.forEach((elms) => elms.forEach(forceUpdate));
      cleanupElements(elmsToUpdate);
    }
  };
};
var unwrap = (val) => typeof val === "function" ? val() : val;
var createObservableMap = (defaultState, shouldUpdate = (a, b) => a !== b) => {
  const unwrappedState = unwrap(defaultState);
  let states = new Map(Object.entries(unwrappedState ?? {}));
  const handlers = {
    dispose: [],
    get: [],
    set: [],
    reset: []
  };
  const reset = () => {
    states = new Map(Object.entries(unwrap(defaultState) ?? {}));
    handlers.reset.forEach((cb) => cb());
  };
  const dispose = () => {
    handlers.dispose.forEach((cb) => cb());
    reset();
  };
  const get = (propName) => {
    handlers.get.forEach((cb) => cb(propName));
    return states.get(propName);
  };
  const set = (propName, value) => {
    const oldValue = states.get(propName);
    if (shouldUpdate(value, oldValue, propName)) {
      states.set(propName, value);
      handlers.set.forEach((cb) => cb(propName, value, oldValue));
    }
  };
  const state = typeof Proxy === "undefined" ? {} : new Proxy(unwrappedState, {
    get(_, propName) {
      return get(propName);
    },
    ownKeys(_) {
      return Array.from(states.keys());
    },
    getOwnPropertyDescriptor() {
      return {
        enumerable: true,
        configurable: true
      };
    },
    has(_, propName) {
      return states.has(propName);
    },
    set(_, propName, value) {
      set(propName, value);
      return true;
    }
  });
  const on = (eventName, callback) => {
    handlers[eventName].push(callback);
    return () => {
      removeFromArray(handlers[eventName], callback);
    };
  };
  const onChange2 = (propName, cb) => {
    const unSet = on("set", (key, newValue) => {
      if (key === propName) {
        cb(newValue);
      }
    });
    const unReset = on("reset", () => cb(unwrap(defaultState)[propName]));
    return () => {
      unSet();
      unReset();
    };
  };
  const use = (...subscriptions) => {
    const unsubs = subscriptions.reduce((unsubs2, subscription) => {
      if (subscription.set) {
        unsubs2.push(on("set", subscription.set));
      }
      if (subscription.get) {
        unsubs2.push(on("get", subscription.get));
      }
      if (subscription.reset) {
        unsubs2.push(on("reset", subscription.reset));
      }
      if (subscription.dispose) {
        unsubs2.push(on("dispose", subscription.dispose));
      }
      return unsubs2;
    }, []);
    return () => unsubs.forEach((unsub) => unsub());
  };
  const forceUpdate2 = (key) => {
    const oldValue = states.get(key);
    handlers.set.forEach((cb) => cb(key, oldValue, oldValue));
  };
  return {
    state,
    get,
    set,
    on,
    onChange: onChange2,
    use,
    dispose,
    reset,
    forceUpdate: forceUpdate2
  };
};
var removeFromArray = (array, item) => {
  const index = array.indexOf(item);
  if (index >= 0) {
    array[index] = array[array.length - 1];
    array.length--;
  }
};
var createStore = (defaultState, shouldUpdate) => {
  const map = createObservableMap(defaultState, shouldUpdate);
  map.use(stencilSubscription());
  return map;
};
var { state: authState, onChange: onChange$1 } = createStore({
  token: localStorage.getItem("pcm-sdk-auth-token") || null
});
var authStore = {
  getToken: () => authState.token,
  setToken: (token) => {
    authState.token = token;
    localStorage.setItem("pcm-sdk-auth-token", token);
  },
  clearToken: () => {
    authState.token = null;
    localStorage.removeItem("pcm-sdk-auth-token");
  }
};
onChange$1("token", (value) => {
  if (value) {
    localStorage.setItem("pcm-sdk-auth-token", value);
  } else {
    localStorage.removeItem("pcm-sdk-auth-token");
  }
});
var getInitialConfig = () => {
  try {
    const storedConfig = localStorage.getItem("pcm-sdk-config-data");
    return storedConfig ? JSON.parse(storedConfig) : {};
  } catch (error) {
    console.error("Error parsing stored config:", error);
    return {};
  }
};
var { state: configState, onChange } = createStore({
  data: getInitialConfig()
});
var configStore = {
  // 获取整个配置对象
  getConfig: () => configState.data,
  // 获取特定配置项
  getItem: (key, defaultValue) => {
    return configState.data[key] !== void 0 ? configState.data[key] : defaultValue;
  },
  // 设置特定配置项
  setItem: (key, value) => {
    configState.data = {
      ...configState.data,
      [key]: value
    };
  },
  // 移除特定配置项
  removeItem: (key) => {
    const newConfig = { ...configState.data };
    delete newConfig[key];
    configState.data = newConfig;
  },
  // 清除所有配置
  clear: () => {
    configState.data = {};
  },
  // 批量更新配置
  updateConfig: (newConfig) => {
    configState.data = {
      ...configState.data,
      ...newConfig
    };
  }
};
onChange("data", (value) => {
  try {
    if (Object.keys(value).length > 0) {
      localStorage.setItem("pcm-sdk-config-data", JSON.stringify(value));
    } else {
      localStorage.removeItem("pcm-sdk-config-data");
    }
  } catch (error) {
    console.error("Error saving config to localStorage:", error);
  }
});
var getEffectiveToken = () => {
  return authStore.getToken() || "";
};
var REQUEST_TIMEOUT = 3 * 60 * 1e3;
var fetchWithTimeout = async (url, options, timeout = REQUEST_TIMEOUT) => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    if (error instanceof Error && error.name === "AbortError") {
      throw new Error("请求超时，请检查网络连接后重试");
    }
    throw error;
  }
};
var sendSSERequest = async (config, isRetry = false) => {
  var _a;
  const { url, method, headers = {}, data, onMessage, onError, onComplete } = config;
  try {
    const requestUrl = `${API_DOMAIN}${url}`;
    if (!headers["authorization"] && !headers["Authorization"]) {
      const token = getEffectiveToken();
      if (token) {
        headers["authorization"] = `Bearer ${token}`;
      }
    }
    const response = await fetchWithTimeout(requestUrl, {
      method,
      headers: {
        "Accept": "text/event-stream",
        "Content-Type": "application/json",
        ...headers
      },
      body: data ? JSON.stringify(data) : void 0
    });
    if (response.status === 401) {
      createTokenInvalidEvent();
      if (!isRetry) {
        return sendSSERequest(config, true);
      }
    }
    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
    }
    const reader = (_a = response.body) == null ? void 0 : _a.getReader();
    if (!reader)
      throw new Error("No reader available");
    const decoder = new TextDecoder();
    let buffer = "";
    while (true) {
      const { value, done } = await reader.read();
      if (done)
        break;
      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";
      for (const line of lines) {
        if (line.startsWith("data:")) {
          try {
            const jsonStr = line.substring(5).trim();
            if (!jsonStr)
              continue;
            const data2 = JSON.parse(jsonStr);
            onMessage == null ? void 0 : onMessage(data2);
          } catch (e) {
            console.error("解析 SSE 数据错误:", e, "原始数据:", line);
          }
        }
      }
    }
    onComplete == null ? void 0 : onComplete();
  } catch (error) {
    console.error("SSE 请求错误:", error);
    if (!isRetry) {
      return sendSSERequest(config, true);
    }
    onError == null ? void 0 : onError(error);
  }
};
var createTokenInvalidEvent = () => {
  const event = new CustomEvent("pcm-token-invalid", {
    bubbles: true,
    composed: true,
    detail: { timestamp: (/* @__PURE__ */ new Date()).getTime() }
  });
  document.dispatchEvent(event);
};
var sendHttpRequest = async (config, isRetry = true) => {
  const { url, method = "GET", headers = {}, params = {}, data, formData, onMessage } = config;
  try {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== void 0 && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    let requestUrl = `${API_DOMAIN}${url}${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;
    if (headers["authorization"] == void 0 && headers["Authorization"] == void 0) {
      const token = getEffectiveToken();
      if (token) {
        headers["authorization"] = `Bearer ${token}`;
      }
    }
    const requestConfig = {
      method,
      headers: formData ? { ...headers } : {
        "Content-Type": "application/json",
        ...headers
      }
    };
    if (formData) {
      requestConfig.body = formData;
    } else if (method !== "GET" && method !== "HEAD" && data) {
      requestConfig.body = JSON.stringify(data);
    }
    if (method === "GET" && data) {
      const dataParams = new URLSearchParams();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== void 0 && value !== null) {
          dataParams.append(key, String(value));
        }
      });
      requestUrl += (queryParams.toString() ? "&" : "?") + dataParams.toString();
    }
    const response = await fetchWithTimeout(requestUrl, requestConfig);
    if (response.status === 401) {
      createTokenInvalidEvent();
      if (isRetry) {
        return sendHttpRequest(config, false);
      }
    }
    const responseData = await response.json();
    if (onMessage) {
      onMessage(responseData);
    }
    if (!response.ok) {
      console.error(`HTTP错误: ${response.status} ${response.statusText}`);
      return {
        success: false,
        message: responseData.message || `HTTP错误: ${response.status}`,
        error: responseData
      };
    }
    if (responseData.code !== 0) {
      console.error(`API错误: ${responseData.message}`);
      return {
        success: false,
        message: responseData.message,
        error: responseData
      };
    }
    return {
      success: true,
      data: responseData.data
    };
  } catch (error) {
    console.error("HTTP请求错误:", error);
    if (isRetry) {
      return sendHttpRequest(config, false);
    }
    if (config.onError) {
      config.onError(error);
    }
    return {
      success: false,
      error,
      message: error instanceof Error ? error.message : "未知错误"
    };
  } finally {
    if (config.onComplete) {
      config.onComplete();
    }
  }
};
var verifyApiKey = async (token) => {
  const response = await sendHttpRequest({
    url: "/sdk/v1/user",
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`
    }
  }, false);
  if (!response.success && response.error && response.error.code === 401) {
    return false;
  } else {
    configStore.setItem("pcm-sdk-CUser", `${response.data.user}(${response.data.chat_user})`);
    return response.success;
  }
};
var fetchAgentInfo = async (botId) => {
  if (!botId) {
    throw new Error("智能体ID不能为空");
  }
  try {
    const response = await sendHttpRequest({
      url: `/sdk/v1/agent/${botId}/info`,
      method: "GET"
    });
    if (!response.success) {
      throw new Error(response.message || "获取智能体信息失败");
    }
    return response.data;
  } catch (error) {
    console.error("获取智能体信息失败:", error);
    throw error;
  }
};
var calculateFileSHA256 = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      var _a;
      try {
        const arrayBuffer = (_a = e.target) == null ? void 0 : _a.result;
        const hashBuffer = await crypto.subtle.digest("SHA-256", arrayBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
        resolve(hashHex);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = () => reject(new Error("读取文件失败"));
    reader.readAsArrayBuffer(file);
  });
};
var uploadFileWithRetry = async (url, file, contentType = "application/octet-stream", maxRetries = 2) => {
  let retries = 0;
  while (true) {
    try {
      const response = await fetchWithTimeout(url, {
        method: "PUT",
        body: file,
        // 直接发送文件内容，不使用FormData
        headers: {
          "Content-Type": contentType
        }
      });
      if (!response.ok) {
        throw new Error(`文件上传失败: ${response.status} ${response.statusText}`);
      }
      return response;
    } catch (error) {
      retries++;
      console.error(`文件上传错误(尝试 ${retries}/${maxRetries}):`, error);
      if (retries >= maxRetries) {
        throw error;
      }
    }
  }
};
var uploadFileToBackend = async (file, headers, params) => {
  try {
    const sha256 = await calculateFileSHA256(file);
    const uploadUrlResponse = await sendHttpRequest({
      url: "/sdk/v1/files/generate-upload-url",
      method: "POST",
      data: {
        ...params,
        filename: file.name,
        filesize: file.size,
        sha256
      },
      headers
    });
    if (!uploadUrlResponse.success || !uploadUrlResponse.data) {
      throw new Error(uploadUrlResponse.message || "获取上传URL失败");
    }
    const generate = uploadUrlResponse.data;
    if (generate.is_deleted != 1) {
      const uploadResponse = await uploadFileWithRetry(generate.upload_url, file, generate.content_type || "application/octet-stream");
      if (!uploadResponse.ok) {
        throw new Error(`文件上传到腾讯云失败: ${uploadResponse.status} ${uploadResponse.statusText}`);
      }
      sendHttpRequest({
        url: "/sdk/v1/files/mark-as-upload",
        method: "POST",
        data: {
          cos_key: generate.cos.cos_key
        },
        headers
      });
    }
    return {
      cos_key: generate.cos.cos_key,
      file_name: generate.filename,
      file_size: generate.filesize,
      ext: generate.filetype
    };
  } catch (error) {
    console.error("文件上传错误:", error);
    throw error;
  }
};
var synthesizeAudio = async (text, token, isRetry = false) => {
  const effectiveToken = getEffectiveToken();
  if (!effectiveToken) {
    throw new Error("API密钥不能为空");
  }
  try {
    const response = await fetchWithTimeout(`${API_DOMAIN}/sdk/v1/tts/synthesize_audio`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "authorization": "Bearer " + effectiveToken
      },
      body: JSON.stringify({ text })
    });
    if (response.status === 401) {
      createTokenInvalidEvent();
      if (!isRetry) {
        return synthesizeAudio(text, token, true);
      }
    }
    if (!response.ok) {
      throw new Error("语音合成失败");
    }
    const audioBlob = await response.blob();
    return URL.createObjectURL(audioBlob);
  } catch (error) {
    console.error("语音合成错误:", error);
    if (!isRetry) {
      return synthesizeAudio(text, token, true);
    }
    throw error;
  }
};

// node_modules/.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/sentry-reporter-Di7JtC0A.js
var ErrorEventBus = class {
  /**
   * 触发错误事件
   */
  static emitError(detail) {
    const event = new CustomEvent("pcm-error", {
      bubbles: true,
      composed: true,
      detail
    });
    document.dispatchEvent(event);
  }
  /**
   * 添加错误事件监听器
   */
  static addErrorListener(callback) {
    const handler = (event) => {
      callback(event.detail);
    };
    document.addEventListener("pcm-error", handler);
    return () => {
      document.removeEventListener("pcm-error", handler);
    };
  }
};
var SentryReporter = class {
  /**
   * 捕获并上报错误
   * @param error 错误对象
   * @param context 错误上下文
   */
  static captureError(error, context) {
    try {
      const CUser = configStore.getItem("pcm-sdk-CUser");
      if (CUser) {
        setUser({ id: String(CUser) });
      }
      let finalError = error;
      if (context == null ? void 0 : context.title) {
        finalError = new Error(context.title);
        if (error == null ? void 0 : error.stack) {
          finalError.stack = error.stack;
        }
        finalError.cause = error;
      }
      if (context) {
        const { title, ...otherContext } = context;
        setContext("error_context", otherContext);
      }
      captureException(finalError);
    } catch (e) {
      console.error("Sentry 上报错误失败:", e);
    }
  }
  /**
   * 捕获并上报消息
   * @param message 消息内容
   * @param context 消息上下文
   */
  static captureMessage(message, context) {
    try {
      const CUser = configStore.getItem("pcm-sdk-CUser");
      if (CUser) {
        setUser({ id: String(CUser) });
      }
      if (context) {
        setContext("message_context", context);
      }
      captureMessage(message);
    } catch (e) {
      console.error("Sentry 上报消息失败:", e);
    }
  }
};

export {
  authStore,
  configStore,
  sendSSERequest,
  sendHttpRequest,
  verifyApiKey,
  fetchAgentInfo,
  uploadFileToBackend,
  synthesizeAudio,
  ErrorEventBus,
  SentryReporter
};
//# sourceMappingURL=chunk-6HSK73GQ.js.map
