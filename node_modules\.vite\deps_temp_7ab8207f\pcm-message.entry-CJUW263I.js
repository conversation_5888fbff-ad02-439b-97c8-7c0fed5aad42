import {
  getElement,
  h,
  registerInstance
} from "./chunk-SPDCWX4H.js";
import {
  __publicField
} from "./chunk-5H7I2G36.js";

// node_modules/.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/pcm-message.entry.js
var pcmMessageCss = ".pcm-message{position:fixed;top:20px;left:50%;transform:translateX(-50%) translateY(-100%);background-color:#fff;padding:10px 16px;border-radius:4px;box-shadow:0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);pointer-events:all;z-index:1010;opacity:0;transition:all 0.3s}.pcm-message-visible{transform:translateX(-50%) translateY(0);opacity:1}.pcm-message-content{display:flex;align-items:center}.pcm-message-icon{margin-right:8px;font-size:16px;line-height:1}.pcm-message-success .pcm-message-icon{color:#52c41a}.pcm-message-error .pcm-message-icon{color:#ff4d4f}.pcm-message-warning .pcm-message-icon{color:#faad14}.pcm-message-info .pcm-message-icon{color:#1890ff}.pcm-message-text{font-size:14px;line-height:1.5}.pcm-message-container{position:fixed;top:20px;left:0;width:100%;display:flex;flex-direction:column;align-items:center;pointer-events:none;z-index:1010}";
var PcmMessage = class {
  constructor(hostRef) {
    __publicField(this, "content", "");
    __publicField(this, "type", "info");
    __publicField(this, "duration", 3e4);
    // 默认显示3秒
    __publicField(this, "visible", false);
    __publicField(this, "timer");
    registerInstance(this, hostRef);
  }
  get el() {
    return getElement(this);
  }
  componentDidLoad() {
    if (this.content) {
      this.show();
    }
  }
  disconnectedCallback() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
  }
  async show() {
    this.visible = true;
    if (this.duration > 0) {
      this.timer = window.setTimeout(() => {
        this.close();
      }, this.duration);
    }
  }
  async close() {
    this.visible = false;
    setTimeout(() => {
      if (this.el && this.el.parentNode) {
        this.el.parentNode.removeChild(this.el);
      }
    }, 300);
  }
  render() {
    return h("div", { key: "63146d694f008826e4e7a029db70a827a9e9e9c5", class: {
      "pcm-message": true,
      "pcm-message-visible": this.visible,
      [`pcm-message-${this.type}`]: true
    } }, h("div", { key: "8c8467b4d6b4e6762b8208ca130de1ea6deaa0f7", class: "pcm-message-content" }, h("span", { key: "5efcdced38c96b93672bde9ce46cb251c797221c", class: "pcm-message-icon" }, this.renderIcon()), h("span", { key: "df54c4e30460b92d0a8ff83b6d364e802c11330a", class: "pcm-message-text" }, this.content)));
  }
  renderIcon() {
    switch (this.type) {
      case "success":
        return h("svg", { viewBox: "64 64 896 896", width: "16px", height: "16px", fill: "currentColor" }, h("path", { d: "M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z" }));
      case "error":
        return h("svg", { viewBox: "64 64 896 896", width: "16px", height: "16px", fill: "currentColor" }, h("path", { d: "M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 01-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z" }));
      case "warning":
        return h("svg", { viewBox: "64 64 896 896", width: "16px", height: "16px", fill: "currentColor" }, h("path", { d: "M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z" }));
      default:
        return h("svg", { viewBox: "64 64 896 896", width: "16px", height: "16px", fill: "currentColor" }, h("path", { d: "M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 664c-30.9 0-56-25.1-56-56 0-30.9 25.1-56 56-56s56 25.1 56 56c0 30.9-25.1 56-56 56zm32-296c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V248c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184z" }));
    }
  }
};
PcmMessage.style = pcmMessageCss;
export {
  PcmMessage as pcm_message
};
//# sourceMappingURL=pcm-message.entry-CJUW263I.js.map
