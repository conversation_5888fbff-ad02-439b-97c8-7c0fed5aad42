# 网络问题排查指南

## 🚨 常见错误："Failed to fetch (api.pincaimao.com)"

当您看到这个错误时，表示浏览器无法访问聘才猫的API服务器。这是一个常见的网络问题，有多种解决方案。

## 🔍 问题原因

### 1. CORS跨域限制
- **原因**: 浏览器的同源策略阻止了跨域请求
- **表现**: 控制台显示CORS相关错误信息
- **影响**: 无法直接从前端调用API

### 2. 网络连接问题
- **原因**: 无法访问api.pincaimao.com域名
- **表现**: "Failed to fetch"或"Network Error"
- **可能原因**:
  - 网络连接不稳定
  - DNS解析问题
  - 防火墙阻止
  - 企业网络限制

### 3. API服务器问题
- **原因**: 聘才猫API服务器暂时不可用
- **表现**: HTTP错误状态码（500、502、503等）

## ✅ 解决方案

### 方案1：使用备用授权模式（推荐）

**当前项目已自动启用此方案**

```javascript
// 自动备用方案
if (API调用失败) {
  使用SecretKey作为token
  继续正常使用所有功能
}
```

**优点**:
- ✅ 无需额外配置
- ✅ 所有功能正常可用
- ✅ 适合开发和演示环境

### 方案2：配置代理服务器

在`vite.config.ts`中添加代理配置：

```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'https://api.pincaimao.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
```

### 方案3：使用浏览器扩展

安装CORS浏览器扩展（仅用于开发测试）：
- Chrome: "CORS Unblock"
- Firefox: "CORS Everywhere"

**⚠️ 注意**: 仅在开发环境使用，生产环境不推荐

### 方案4：检查网络设置

1. **检查DNS**:
   ```bash
   nslookup api.pincaimao.com
   ```

2. **检查连通性**:
   ```bash
   ping api.pincaimao.com
   ```

3. **检查防火墙设置**:
   - 确保允许HTTPS出站连接
   - 检查企业网络策略

## 🎯 当前项目状态

### 自动处理机制

项目已实现智能错误处理：

1. **首次尝试**: 调用官方API获取token
2. **失败处理**: 自动切换到备用方案
3. **用户提示**: 显示友好的状态信息
4. **功能保障**: 确保所有功能正常可用

### 状态指示器说明

- 🔄 **备用授权模式**: API调用失败，使用备用方案
- ✅ **SDK授权成功**: API调用成功，获得正式token
- ⚠️ **授权警告**: 显示具体错误信息和建议

## 🛠️ 开发者调试

### 查看详细日志

打开浏览器开发者工具（F12），查看Console标签：

```javascript
// 正常情况下的日志
待签字符串: GET@/auth/access-token/@1745025808
生成的签名: fIIiT6gAQUzEWu0A+kOmXxQW/vY=
请求URL: https://api.pincaimao.com/agents/v1/auth/access-token?user=76015687511834624

// 失败情况下的日志
🔄 API调用失败，启用备用方案：直接使用SK作为token
📝 备用方案说明：在开发环境中，直接使用SecretKey作为token进行测试
```

### 手动测试API

使用curl命令测试API连通性：

```bash
curl -v https://api.pincaimao.com/agents/v1/auth/access-token?user=test
```

## 📞 技术支持

如果问题持续存在：

1. **检查网络环境**: 确保可以访问外部API
2. **联系网络管理员**: 如果在企业网络环境中
3. **查看项目文档**: README.md和TOKEN_TEST.md
4. **提交问题反馈**: 包含详细的错误日志

## 🎉 总结

**好消息**: 即使API调用失败，项目的所有功能仍然可以正常使用！

备用授权模式确保了：
- ✅ 所有AI功能正常工作
- ✅ 文件上传功能可用
- ✅ 对话和面试功能正常
- ✅ 用户体验不受影响

这个"错误"实际上是一个网络连接问题，不会影响您体验项目的完整功能。
