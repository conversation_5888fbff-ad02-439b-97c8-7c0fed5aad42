{"version": 3, "sources": ["../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/debug-build.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/version.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/worldwide.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/carrier.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/is.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/browser.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/logger.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/string.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/object.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/misc.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/time.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/session.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/merge.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/spanOnScope.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/propagationContext.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/scope.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/defaultScopes.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/asyncContext/stackStrategy.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/asyncContext/index.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/currentScopes.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/semanticAttributes.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/tracing/spanstatus.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/tracing/utils.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/baggage.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/spanUtils.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/stacktrace.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/hasSpansEnabled.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/constants.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/dsn.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/tracing/dynamicSamplingContext.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/normalize.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/syncpromise.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/eventProcessors.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils-hoist/debug-ids.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/applyScopeDataToEvent.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/utils/prepareEvent.js", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/node_modules/.pnpm/@sentry+core@9.22.0/node_modules/@sentry/core/build/esm/exports.js"], "sourcesContent": ["/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nconst DEBUG_BUILD = (typeof __SENTRY_DEBUG__ === 'undefined' || __SENTRY_DEBUG__);\n\nexport { DEBUG_BUILD };\n//# sourceMappingURL=debug-build.js.map\n", "// This is a magic string replaced by rollup\n\nconst SDK_VERSION = \"9.22.0\" ;\n\nexport { SDK_VERSION };\n//# sourceMappingURL=version.js.map\n", "/** Internal global with common properties and Sentry extensions  */\n\n/** Get's the global object for the current JavaScript runtime */\nconst GLOBAL_OBJ = globalThis ;\n\nexport { GLOBAL_OBJ };\n//# sourceMappingURL=worldwide.js.map\n", "import { SDK_VERSION } from './utils-hoist/version.js';\nimport { GLOBAL_OBJ } from './utils-hoist/worldwide.js';\n\n/**\n * An object that contains globally accessible properties and maintains a scope stack.\n * @hidden\n */\n\n/**\n * Returns the global shim registry.\n *\n * FIXME: This function is problematic, because despite always returning a valid Carrier,\n * it has an optional `__SENTRY__` property, which then in turn requires us to always perform an unnecessary check\n * at the call-site. We always access the carrier through this function, so we can guarantee that `__SENTRY__` is there.\n **/\nfunction getMainCarrier() {\n  // This ensures a Sentry carrier exists\n  getSentryCarrier(GLOBAL_OBJ);\n  return GLOBAL_OBJ;\n}\n\n/** Will either get the existing sentry carrier, or create a new one. */\nfunction getSentryCarrier(carrier) {\n  const __SENTRY__ = (carrier.__SENTRY__ = carrier.__SENTRY__ || {});\n\n  // For now: First SDK that sets the .version property wins\n  __SENTRY__.version = __SENTRY__.version || SDK_VERSION;\n\n  // Intentionally populating and returning the version of \"this\" SDK instance\n  // rather than what's set in .version so that \"this\" SDK always gets its carrier\n  return (__SENTRY__[SDK_VERSION] = __SENTRY__[SDK_VERSION] || {});\n}\n\n/**\n * Returns a global singleton contained in the global `__SENTRY__[]` object.\n *\n * If the singleton doesn't already exist in `__SENTRY__`, it will be created using the given factory\n * function and added to the `__SENTRY__` object.\n *\n * @param name name of the global singleton on __SENTRY__\n * @param creator creator Factory function to create the singleton if it doesn't already exist on `__SENTRY__`\n * @param obj (Optional) The global object on which to look for `__SENTRY__`, if not `GLOBAL_OBJ`'s return value\n * @returns the singleton\n */\nfunction getGlobalSingleton(\n  name,\n  creator,\n  obj = GLOBAL_OBJ,\n) {\n  const __SENTRY__ = (obj.__SENTRY__ = obj.__SENTRY__ || {});\n  const carrier = (__SENTRY__[SDK_VERSION] = __SENTRY__[SDK_VERSION] || {});\n  // Note: We do not want to set `carrier.version` here, as this may be called before any `init` is called, e.g. for the default scopes\n  return carrier[name] || (carrier[name] = creator());\n}\n\nexport { getGlobalSingleton, getMainCarrier, getSentryCarrier };\n//# sourceMappingURL=carrier.js.map\n", "// eslint-disable-next-line @typescript-eslint/unbound-method\nconst objectToString = Object.prototype.toString;\n\n/**\n * Checks whether given value's type is one of a few Error or Error-like\n * {@link isError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isError(wat) {\n  switch (objectToString.call(wat)) {\n    case '[object Error]':\n    case '[object Exception]':\n    case '[object DOMException]':\n    case '[object WebAssembly.Exception]':\n      return true;\n    default:\n      return isInstanceOf(wat, Error);\n  }\n}\n/**\n * Checks whether given value is an instance of the given built-in class.\n *\n * @param wat The value to be checked\n * @param className\n * @returns A boolean representing the result.\n */\nfunction isBuiltin(wat, className) {\n  return objectToString.call(wat) === `[object ${className}]`;\n}\n\n/**\n * Checks whether given value's type is ErrorEvent\n * {@link isErrorEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isErrorEvent(wat) {\n  return isBuiltin(wat, 'ErrorEvent');\n}\n\n/**\n * Checks whether given value's type is DOMError\n * {@link isDOMError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isDOMError(wat) {\n  return isBuiltin(wat, 'DOMError');\n}\n\n/**\n * Checks whether given value's type is DOMException\n * {@link isDOMException}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isDOMException(wat) {\n  return isBuiltin(wat, 'DOMException');\n}\n\n/**\n * Checks whether given value's type is a string\n * {@link isString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isString(wat) {\n  return isBuiltin(wat, 'String');\n}\n\n/**\n * Checks whether given string is parameterized\n * {@link isParameterizedString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isParameterizedString(wat) {\n  return (\n    typeof wat === 'object' &&\n    wat !== null &&\n    '__sentry_template_string__' in wat &&\n    '__sentry_template_values__' in wat\n  );\n}\n\n/**\n * Checks whether given value is a primitive (undefined, null, number, boolean, string, bigint, symbol)\n * {@link isPrimitive}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isPrimitive(wat) {\n  return wat === null || isParameterizedString(wat) || (typeof wat !== 'object' && typeof wat !== 'function');\n}\n\n/**\n * Checks whether given value's type is an object literal, or a class instance.\n * {@link isPlainObject}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isPlainObject(wat) {\n  return isBuiltin(wat, 'Object');\n}\n\n/**\n * Checks whether given value's type is an Event instance\n * {@link isEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isEvent(wat) {\n  return typeof Event !== 'undefined' && isInstanceOf(wat, Event);\n}\n\n/**\n * Checks whether given value's type is an Element instance\n * {@link isElement}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isElement(wat) {\n  return typeof Element !== 'undefined' && isInstanceOf(wat, Element);\n}\n\n/**\n * Checks whether given value's type is an regexp\n * {@link isRegExp}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isRegExp(wat) {\n  return isBuiltin(wat, 'RegExp');\n}\n\n/**\n * Checks whether given value has a then function.\n * @param wat A value to be checked.\n */\nfunction isThenable(wat) {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return Boolean(wat?.then && typeof wat.then === 'function');\n}\n\n/**\n * Checks whether given value's type is a SyntheticEvent\n * {@link isSyntheticEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isSyntheticEvent(wat) {\n  return isPlainObject(wat) && 'nativeEvent' in wat && 'preventDefault' in wat && 'stopPropagation' in wat;\n}\n\n/**\n * Checks whether given value's type is an instance of provided constructor.\n * {@link isInstanceOf}.\n *\n * @param wat A value to be checked.\n * @param base A constructor to be used in a check.\n * @returns A boolean representing the result.\n */\nfunction isInstanceOf(wat, base) {\n  try {\n    return wat instanceof base;\n  } catch (_e) {\n    return false;\n  }\n}\n\n/**\n * Checks whether given value's type is a Vue ViewModel.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nfunction isVueViewModel(wat) {\n  // Not using Object.prototype.toString because in Vue 3 it would read the instance's Symbol(Symbol.toStringTag) property.\n  return !!(typeof wat === 'object' && wat !== null && ((wat ).__isVue || (wat )._isVue));\n}\n\n/**\n * Checks whether the given parameter is a Standard Web API Request instance.\n *\n * Returns false if Request is not available in the current runtime.\n */\nfunction isRequest(request) {\n  return typeof Request !== 'undefined' && isInstanceOf(request, Request);\n}\n\nexport { isDOMError, isDOMException, isElement, isError, isErrorEvent, isEvent, isInstanceOf, isParameterizedString, isPlainObject, isPrimitive, isRegExp, isRequest, isString, isSyntheticEvent, isThenable, isVueViewModel };\n//# sourceMappingURL=is.js.map\n", "import { isString } from './is.js';\nimport { GL<PERSON><PERSON>L_OBJ } from './worldwide.js';\n\nconst WINDOW = GLOBAL_OBJ ;\n\nconst DEFAULT_MAX_STRING_LENGTH = 80;\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nfunction htmlTreeAsString(\n  elem,\n  options = {},\n) {\n  if (!elem) {\n    return '<unknown>';\n  }\n\n  // try/catch both:\n  // - accessing event.target (see getsentry/raven-js#838, #768)\n  // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n  // - can throw an exception in some circumstances.\n  try {\n    let currentElem = elem ;\n    const MAX_TRAVERSE_HEIGHT = 5;\n    const out = [];\n    let height = 0;\n    let len = 0;\n    const separator = ' > ';\n    const sepLength = separator.length;\n    let nextStr;\n    const keyAttrs = Array.isArray(options) ? options : options.keyAttrs;\n    const maxStringLength = (!Array.isArray(options) && options.maxStringLength) || DEFAULT_MAX_STRING_LENGTH;\n\n    while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {\n      nextStr = _htmlElementAsString(currentElem, keyAttrs);\n      // bail out if\n      // - nextStr is the 'html' element\n      // - the length of the string that would be created exceeds maxStringLength\n      //   (ignore this limit if we are on the first iteration)\n      if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= maxStringLength)) {\n        break;\n      }\n\n      out.push(nextStr);\n\n      len += nextStr.length;\n      currentElem = currentElem.parentNode;\n    }\n\n    return out.reverse().join(separator);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nfunction _htmlElementAsString(el, keyAttrs) {\n  const elem = el\n\n;\n\n  const out = [];\n\n  if (!elem?.tagName) {\n    return '';\n  }\n\n  // @ts-expect-error WINDOW has HTMLElement\n  if (WINDOW.HTMLElement) {\n    // If using the component name annotation plugin, this value may be available on the DOM node\n    if (elem instanceof HTMLElement && elem.dataset) {\n      if (elem.dataset['sentryComponent']) {\n        return elem.dataset['sentryComponent'];\n      }\n      if (elem.dataset['sentryElement']) {\n        return elem.dataset['sentryElement'];\n      }\n    }\n  }\n\n  out.push(elem.tagName.toLowerCase());\n\n  // Pairs of attribute keys defined in `serializeAttribute` and their values on element.\n  const keyAttrPairs = keyAttrs?.length\n    ? keyAttrs.filter(keyAttr => elem.getAttribute(keyAttr)).map(keyAttr => [keyAttr, elem.getAttribute(keyAttr)])\n    : null;\n\n  if (keyAttrPairs?.length) {\n    keyAttrPairs.forEach(keyAttrPair => {\n      out.push(`[${keyAttrPair[0]}=\"${keyAttrPair[1]}\"]`);\n    });\n  } else {\n    if (elem.id) {\n      out.push(`#${elem.id}`);\n    }\n\n    const className = elem.className;\n    if (className && isString(className)) {\n      const classes = className.split(/\\s+/);\n      for (const c of classes) {\n        out.push(`.${c}`);\n      }\n    }\n  }\n  const allowedAttrs = ['aria-label', 'type', 'name', 'title', 'alt'];\n  for (const k of allowedAttrs) {\n    const attr = elem.getAttribute(k);\n    if (attr) {\n      out.push(`[${k}=\"${attr}\"]`);\n    }\n  }\n\n  return out.join('');\n}\n\n/**\n * A safe form of location.href\n */\nfunction getLocationHref() {\n  try {\n    return WINDOW.document.location.href;\n  } catch (oO) {\n    return '';\n  }\n}\n\n/**\n * Given a DOM element, traverses up the tree until it finds the first ancestor node\n * that has the `data-sentry-component` or `data-sentry-element` attribute with `data-sentry-component` taking\n * precedence. This attribute is added at build-time by projects that have the component name annotation plugin installed.\n *\n * @returns a string representation of the component for the provided DOM element, or `null` if not found\n */\nfunction getComponentName(elem) {\n  // @ts-expect-error WINDOW has HTMLElement\n  if (!WINDOW.HTMLElement) {\n    return null;\n  }\n\n  let currentElem = elem ;\n  const MAX_TRAVERSE_HEIGHT = 5;\n  for (let i = 0; i < MAX_TRAVERSE_HEIGHT; i++) {\n    if (!currentElem) {\n      return null;\n    }\n\n    if (currentElem instanceof HTMLElement) {\n      if (currentElem.dataset['sentryComponent']) {\n        return currentElem.dataset['sentryComponent'];\n      }\n      if (currentElem.dataset['sentryElement']) {\n        return currentElem.dataset['sentryElement'];\n      }\n    }\n\n    currentElem = currentElem.parentNode;\n  }\n\n  return null;\n}\n\nexport { getComponentName, getLocationHref, htmlTreeAsString };\n//# sourceMappingURL=browser.js.map\n", "import { getGlobalSingleton } from '../carrier.js';\nimport { DEBUG_BUILD } from '../debug-build.js';\nimport { GLOBAL_OBJ } from './worldwide.js';\n\n/** Prefix for logging strings */\nconst PREFIX = 'Sentry Logger ';\n\nconst CONSOLE_LEVELS = [\n  'debug',\n  'info',\n  'warn',\n  'error',\n  'log',\n  'assert',\n  'trace',\n] ;\n\n/** This may be mutated by the console instrumentation. */\nconst originalConsoleMethods\n\n = {};\n\n/** A Sentry Logger instance. */\n\n/**\n * Temporarily disable sentry console instrumentations.\n *\n * @param callback The function to run against the original `console` messages\n * @returns The results of the callback\n */\nfunction consoleSandbox(callback) {\n  if (!('console' in GLOBAL_OBJ)) {\n    return callback();\n  }\n\n  const console = GLOBAL_OBJ.console ;\n  const wrappedFuncs = {};\n\n  const wrappedLevels = Object.keys(originalConsoleMethods) ;\n\n  // Restore all wrapped console methods\n  wrappedLevels.forEach(level => {\n    const originalConsoleMethod = originalConsoleMethods[level] ;\n    wrappedFuncs[level] = console[level] ;\n    console[level] = originalConsoleMethod;\n  });\n\n  try {\n    return callback();\n  } finally {\n    // Revert restoration to wrapped state\n    wrappedLevels.forEach(level => {\n      console[level] = wrappedFuncs[level] ;\n    });\n  }\n}\n\nfunction makeLogger() {\n  let enabled = false;\n  const logger = {\n    enable: () => {\n      enabled = true;\n    },\n    disable: () => {\n      enabled = false;\n    },\n    isEnabled: () => enabled,\n  };\n\n  if (DEBUG_BUILD) {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = (...args) => {\n        if (enabled) {\n          consoleSandbox(() => {\n            GLOBAL_OBJ.console[name](`${PREFIX}[${name}]:`, ...args);\n          });\n        }\n      };\n    });\n  } else {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = () => undefined;\n    });\n  }\n\n  return logger ;\n}\n\n/**\n * This is a logger singleton which either logs things or no-ops if logging is not enabled.\n * The logger is a singleton on the carrier, to ensure that a consistent logger is used throughout the SDK.\n */\nconst logger = getGlobalSingleton('logger', makeLogger);\n\nexport { CONSOLE_LEVELS, consoleSandbox, logger, originalConsoleMethods };\n//# sourceMappingURL=logger.js.map\n", "import { isString, isRegExp, isVueViewModel } from './is.js';\n\n/**\n * Truncates given string to the maximum characters count\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string (0 = unlimited)\n * @returns string Encoded\n */\nfunction truncate(str, max = 0) {\n  if (typeof str !== 'string' || max === 0) {\n    return str;\n  }\n  return str.length <= max ? str : `${str.slice(0, max)}...`;\n}\n\n/**\n * This is basically just `trim_line` from\n * https://github.com/getsentry/sentry/blob/master/src/sentry/lang/javascript/processor.py#L67\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string\n * @returns string Encoded\n */\nfunction snipLine(line, colno) {\n  let newLine = line;\n  const lineLength = newLine.length;\n  if (lineLength <= 150) {\n    return newLine;\n  }\n  if (colno > lineLength) {\n    // eslint-disable-next-line no-param-reassign\n    colno = lineLength;\n  }\n\n  let start = Math.max(colno - 60, 0);\n  if (start < 5) {\n    start = 0;\n  }\n\n  let end = Math.min(start + 140, lineLength);\n  if (end > lineLength - 5) {\n    end = lineLength;\n  }\n  if (end === lineLength) {\n    start = Math.max(end - 140, 0);\n  }\n\n  newLine = newLine.slice(start, end);\n  if (start > 0) {\n    newLine = `'{snip} ${newLine}`;\n  }\n  if (end < lineLength) {\n    newLine += ' {snip}';\n  }\n\n  return newLine;\n}\n\n/**\n * Join values in array\n * @param input array of values to be joined together\n * @param delimiter string to be placed in-between values\n * @returns Joined values\n */\nfunction safeJoin(input, delimiter) {\n  if (!Array.isArray(input)) {\n    return '';\n  }\n\n  const output = [];\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < input.length; i++) {\n    const value = input[i];\n    try {\n      // This is a hack to fix a Vue3-specific bug that causes an infinite loop of\n      // console warnings. This happens when a Vue template is rendered with\n      // an undeclared variable, which we try to stringify, ultimately causing\n      // Vue to issue another warning which repeats indefinitely.\n      // see: https://github.com/getsentry/sentry-javascript/pull/8981\n      if (isVueViewModel(value)) {\n        output.push('[VueViewModel]');\n      } else {\n        output.push(String(value));\n      }\n    } catch (e) {\n      output.push('[value cannot be serialized]');\n    }\n  }\n\n  return output.join(delimiter);\n}\n\n/**\n * Checks if the given value matches a regex or string\n *\n * @param value The string to test\n * @param pattern Either a regex or a string against which `value` will be matched\n * @param requireExactStringMatch If true, `value` must match `pattern` exactly. If false, `value` will match\n * `pattern` if it contains `pattern`. Only applies to string-type patterns.\n */\nfunction isMatchingPattern(\n  value,\n  pattern,\n  requireExactStringMatch = false,\n) {\n  if (!isString(value)) {\n    return false;\n  }\n\n  if (isRegExp(pattern)) {\n    return pattern.test(value);\n  }\n  if (isString(pattern)) {\n    return requireExactStringMatch ? value === pattern : value.includes(pattern);\n  }\n\n  return false;\n}\n\n/**\n * Test the given string against an array of strings and regexes. By default, string matching is done on a\n * substring-inclusion basis rather than a strict equality basis\n *\n * @param testString The string to test\n * @param patterns The patterns against which to test the string\n * @param requireExactStringMatch If true, `testString` must match one of the given string patterns exactly in order to\n * count. If false, `testString` will match a string pattern if it contains that pattern.\n * @returns\n */\nfunction stringMatchesSomePattern(\n  testString,\n  patterns = [],\n  requireExactStringMatch = false,\n) {\n  return patterns.some(pattern => isMatchingPattern(testString, pattern, requireExactStringMatch));\n}\n\nexport { isMatchingPattern, safeJoin, snipLine, stringMatchesSomePattern, truncate };\n//# sourceMappingURL=string.js.map\n", "import { DEBUG_BUILD } from '../debug-build.js';\nimport { htmlTreeAsString } from './browser.js';\nimport { isError, isEvent, isInstanceOf, isPrimitive, isElement } from './is.js';\nimport { logger } from './logger.js';\nimport { truncate } from './string.js';\n\n/**\n * Replace a method in an object with a wrapped version of itself.\n *\n * If the method on the passed object is not a function, the wrapper will not be applied.\n *\n * @param source An object that contains a method to be wrapped.\n * @param name The name of the method to be wrapped.\n * @param replacementFactory A higher-order function that takes the original version of the given method and returns a\n * wrapped version. Note: The function returned by `replacementFactory` needs to be a non-arrow function, in order to\n * preserve the correct value of `this`, and the original method must be called using `origMethod.call(this, <other\n * args>)` or `origMethod.apply(this, [<other args>])` (rather than being called directly), again to preserve `this`.\n * @returns void\n */\nfunction fill(source, name, replacementFactory) {\n  if (!(name in source)) {\n    return;\n  }\n\n  // explicitly casting to unknown because we don't know the type of the method initially at all\n  const original = source[name] ;\n\n  if (typeof original !== 'function') {\n    return;\n  }\n\n  const wrapped = replacementFactory(original) ;\n\n  // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n  // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n  if (typeof wrapped === 'function') {\n    markFunctionWrapped(wrapped, original);\n  }\n\n  try {\n    source[name] = wrapped;\n  } catch {\n    DEBUG_BUILD && logger.log(`Failed to replace method \"${name}\" in object`, source);\n  }\n}\n\n/**\n * Defines a non-enumerable property on the given object.\n *\n * @param obj The object on which to set the property\n * @param name The name of the property to be set\n * @param value The value to which to set the property\n */\nfunction addNonEnumerableProperty(obj, name, value) {\n  try {\n    Object.defineProperty(obj, name, {\n      // enumerable: false, // the default, so we can save on bundle size by not explicitly setting it\n      value: value,\n      writable: true,\n      configurable: true,\n    });\n  } catch (o_O) {\n    DEBUG_BUILD && logger.log(`Failed to add non-enumerable property \"${name}\" to object`, obj);\n  }\n}\n\n/**\n * Remembers the original function on the wrapped function and\n * patches up the prototype.\n *\n * @param wrapped the wrapper function\n * @param original the original function that gets wrapped\n */\nfunction markFunctionWrapped(wrapped, original) {\n  try {\n    const proto = original.prototype || {};\n    wrapped.prototype = original.prototype = proto;\n    addNonEnumerableProperty(wrapped, '__sentry_original__', original);\n  } catch (o_O) {} // eslint-disable-line no-empty\n}\n\n/**\n * This extracts the original function if available.  See\n * `markFunctionWrapped` for more information.\n *\n * @param func the function to unwrap\n * @returns the unwrapped version of the function if available.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction getOriginalFunction(func) {\n  return func.__sentry_original__;\n}\n\n/**\n * Transforms any `Error` or `Event` into a plain object with all of their enumerable properties, and some of their\n * non-enumerable properties attached.\n *\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\n * @returns An Event or Error turned into an object - or the value argument itself, when value is neither an Event nor\n *  an Error.\n */\nfunction convertToPlainObject(value)\n\n {\n  if (isError(value)) {\n    return {\n      message: value.message,\n      name: value.name,\n      stack: value.stack,\n      ...getOwnProperties(value),\n    };\n  } else if (isEvent(value)) {\n    const newObj\n\n = {\n      type: value.type,\n      target: serializeEventTarget(value.target),\n      currentTarget: serializeEventTarget(value.currentTarget),\n      ...getOwnProperties(value),\n    };\n\n    if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n      newObj.detail = value.detail;\n    }\n\n    return newObj;\n  } else {\n    return value;\n  }\n}\n\n/** Creates a string representation of the target of an `Event` object */\nfunction serializeEventTarget(target) {\n  try {\n    return isElement(target) ? htmlTreeAsString(target) : Object.prototype.toString.call(target);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/** Filters out all but an object's own properties */\nfunction getOwnProperties(obj) {\n  if (typeof obj === 'object' && obj !== null) {\n    const extractedProps = {};\n    for (const property in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, property)) {\n        extractedProps[property] = (obj )[property];\n      }\n    }\n    return extractedProps;\n  } else {\n    return {};\n  }\n}\n\n/**\n * Given any captured exception, extract its keys and create a sorted\n * and truncated list that will be used inside the event message.\n * eg. `Non-error exception captured with keys: foo, bar, baz`\n */\nfunction extractExceptionKeysForMessage(exception, maxLength = 40) {\n  const keys = Object.keys(convertToPlainObject(exception));\n  keys.sort();\n\n  const firstKey = keys[0];\n\n  if (!firstKey) {\n    return '[object has no keys]';\n  }\n\n  if (firstKey.length >= maxLength) {\n    return truncate(firstKey, maxLength);\n  }\n\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ');\n    if (serialized.length > maxLength) {\n      continue;\n    }\n    if (includedKeys === keys.length) {\n      return serialized;\n    }\n    return truncate(serialized, maxLength);\n  }\n\n  return '';\n}\n\n/**\n * Given any object, return a new object having removed all fields whose value was `undefined`.\n * Works recursively on objects and arrays.\n *\n * Attention: This function keeps circular references in the returned object.\n *\n * @deprecated This function is no longer used by the SDK and will be removed in a future major version.\n */\nfunction dropUndefinedKeys(inputValue) {\n  // This map keeps track of what already visited nodes map to.\n  // Our Set - based memoBuilder doesn't work here because we want to the output object to have the same circular\n  // references as the input object.\n  const memoizationMap = new Map();\n\n  // This function just proxies `_dropUndefinedKeys` to keep the `memoBuilder` out of this function's API\n  return _dropUndefinedKeys(inputValue, memoizationMap);\n}\n\nfunction _dropUndefinedKeys(inputValue, memoizationMap) {\n  // Early return for primitive values\n  if (inputValue === null || typeof inputValue !== 'object') {\n    return inputValue;\n  }\n\n  // Check memo map first for all object types\n  const memoVal = memoizationMap.get(inputValue);\n  if (memoVal !== undefined) {\n    return memoVal ;\n  }\n\n  // handle arrays\n  if (Array.isArray(inputValue)) {\n    const returnValue = [];\n    // Store mapping to handle circular references\n    memoizationMap.set(inputValue, returnValue);\n\n    inputValue.forEach(value => {\n      returnValue.push(_dropUndefinedKeys(value, memoizationMap));\n    });\n\n    return returnValue ;\n  }\n\n  if (isPojo(inputValue)) {\n    const returnValue = {};\n    // Store mapping to handle circular references\n    memoizationMap.set(inputValue, returnValue);\n\n    const keys = Object.keys(inputValue);\n\n    keys.forEach(key => {\n      const val = inputValue[key];\n      if (val !== undefined) {\n        returnValue[key] = _dropUndefinedKeys(val, memoizationMap);\n      }\n    });\n\n    return returnValue ;\n  }\n\n  // For other object types, return as is\n  return inputValue;\n}\n\nfunction isPojo(input) {\n  // Plain objects have Object as constructor or no constructor\n  const constructor = (input ).constructor;\n  return constructor === Object || constructor === undefined;\n}\n\n/**\n * Ensure that something is an object.\n *\n * Turns `undefined` and `null` into `String`s and all other primitives into instances of their respective wrapper\n * classes (String, Boolean, Number, etc.). Acts as the identity function on non-primitives.\n *\n * @param wat The subject of the objectification\n * @returns A version of `wat` which can safely be used with `Object` class methods\n */\nfunction objectify(wat) {\n  let objectified;\n  switch (true) {\n    // this will catch both undefined and null\n    case wat == undefined:\n      objectified = new String(wat);\n      break;\n\n    // Though symbols and bigints do have wrapper classes (`Symbol` and `BigInt`, respectively), for whatever reason\n    // those classes don't have constructors which can be used with the `new` keyword. We therefore need to cast each as\n    // an object in order to wrap it.\n    case typeof wat === 'symbol' || typeof wat === 'bigint':\n      objectified = Object(wat);\n      break;\n\n    // this will catch the remaining primitives: `String`, `Number`, and `Boolean`\n    case isPrimitive(wat):\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      objectified = new (wat ).constructor(wat);\n      break;\n\n    // by process of elimination, at this point we know that `wat` must already be an object\n    default:\n      objectified = wat;\n      break;\n  }\n  return objectified;\n}\n\nexport { addNonEnumerableProperty, convertToPlainObject, dropUndefinedKeys, extractExceptionKeysForMessage, fill, getOriginalFunction, markFunctionWrapped, objectify };\n//# sourceMappingURL=object.js.map\n", "import { addNonEnumerableProperty } from './object.js';\nimport { snipLine } from './string.js';\nimport { GLOBAL_OBJ } from './worldwide.js';\n\nfunction getCrypto() {\n  const gbl = GLOBAL_OBJ ;\n  return gbl.crypto || gbl.msCrypto;\n}\n\n/**\n * UUID4 generator\n * @param crypto Object that provides the crypto API.\n * @returns string Generated UUID4.\n */\nfunction uuid4(crypto = getCrypto()) {\n  let getRandomByte = () => Math.random() * 16;\n  try {\n    if (crypto?.randomUUID) {\n      return crypto.randomUUID().replace(/-/g, '');\n    }\n    if (crypto?.getRandomValues) {\n      getRandomByte = () => {\n        // crypto.getRandomValues might return undefined instead of the typed array\n        // in old Chromium versions (e.g. 23.0.1235.0 (151422))\n        // However, `typedArray` is still filled in-place.\n        // @see https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues#typedarray\n        const typedArray = new Uint8Array(1);\n        crypto.getRandomValues(typedArray);\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        return typedArray[0];\n      };\n    }\n  } catch (_) {\n    // some runtimes can crash invoking crypto\n    // https://github.com/getsentry/sentry-javascript/issues/8935\n  }\n\n  // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n  // Concatenating the following numbers as strings results in '10000000100040008000100000000000'\n  return (([1e7] ) + 1e3 + 4e3 + 8e3 + 1e11).replace(/[018]/g, c =>\n    // eslint-disable-next-line no-bitwise\n    ((c ) ^ ((getRandomByte() & 15) >> ((c ) / 4))).toString(16),\n  );\n}\n\nfunction getFirstException(event) {\n  return event.exception?.values?.[0];\n}\n\n/**\n * Extracts either message or type+value from an event that can be used for user-facing logs\n * @returns event's description\n */\nfunction getEventDescription(event) {\n  const { message, event_id: eventId } = event;\n  if (message) {\n    return message;\n  }\n\n  const firstException = getFirstException(event);\n  if (firstException) {\n    if (firstException.type && firstException.value) {\n      return `${firstException.type}: ${firstException.value}`;\n    }\n    return firstException.type || firstException.value || eventId || '<unknown>';\n  }\n  return eventId || '<unknown>';\n}\n\n/**\n * Adds exception values, type and value to an synthetic Exception.\n * @param event The event to modify.\n * @param value Value of the exception.\n * @param type Type of the exception.\n * @hidden\n */\nfunction addExceptionTypeValue(event, value, type) {\n  const exception = (event.exception = event.exception || {});\n  const values = (exception.values = exception.values || []);\n  const firstException = (values[0] = values[0] || {});\n  if (!firstException.value) {\n    firstException.value = value || '';\n  }\n  if (!firstException.type) {\n    firstException.type = type || 'Error';\n  }\n}\n\n/**\n * Adds exception mechanism data to a given event. Uses defaults if the second parameter is not passed.\n *\n * @param event The event to modify.\n * @param newMechanism Mechanism data to add to the event.\n * @hidden\n */\nfunction addExceptionMechanism(event, newMechanism) {\n  const firstException = getFirstException(event);\n  if (!firstException) {\n    return;\n  }\n\n  const defaultMechanism = { type: 'generic', handled: true };\n  const currentMechanism = firstException.mechanism;\n  firstException.mechanism = { ...defaultMechanism, ...currentMechanism, ...newMechanism };\n\n  if (newMechanism && 'data' in newMechanism) {\n    const mergedData = { ...currentMechanism?.data, ...newMechanism.data };\n    firstException.mechanism.data = mergedData;\n  }\n}\n\n// https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string\nconst SEMVER_REGEXP =\n  /^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\n\n/**\n * Represents Semantic Versioning object\n */\n\nfunction _parseInt(input) {\n  return parseInt(input || '', 10);\n}\n\n/**\n * Parses input into a SemVer interface\n * @param input string representation of a semver version\n */\nfunction parseSemver(input) {\n  const match = input.match(SEMVER_REGEXP) || [];\n  const major = _parseInt(match[1]);\n  const minor = _parseInt(match[2]);\n  const patch = _parseInt(match[3]);\n  return {\n    buildmetadata: match[5],\n    major: isNaN(major) ? undefined : major,\n    minor: isNaN(minor) ? undefined : minor,\n    patch: isNaN(patch) ? undefined : patch,\n    prerelease: match[4],\n  };\n}\n\n/**\n * This function adds context (pre/post/line) lines to the provided frame\n *\n * @param lines string[] containing all lines\n * @param frame StackFrame that will be mutated\n * @param linesOfContext number of context lines we want to add pre/post\n */\nfunction addContextToFrame(lines, frame, linesOfContext = 5) {\n  // When there is no line number in the frame, attaching context is nonsensical and will even break grouping\n  if (frame.lineno === undefined) {\n    return;\n  }\n\n  const maxLines = lines.length;\n  const sourceLine = Math.max(Math.min(maxLines - 1, frame.lineno - 1), 0);\n\n  frame.pre_context = lines\n    .slice(Math.max(0, sourceLine - linesOfContext), sourceLine)\n    .map((line) => snipLine(line, 0));\n\n  // We guard here to ensure this is not larger than the existing number of lines\n  const lineIndex = Math.min(maxLines - 1, sourceLine);\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  frame.context_line = snipLine(lines[lineIndex], frame.colno || 0);\n\n  frame.post_context = lines\n    .slice(Math.min(sourceLine + 1, maxLines), sourceLine + 1 + linesOfContext)\n    .map((line) => snipLine(line, 0));\n}\n\n/**\n * Checks whether or not we've already captured the given exception (note: not an identical exception - the very object\n * in question), and marks it captured if not.\n *\n * This is useful because it's possible for an error to get captured by more than one mechanism. After we intercept and\n * record an error, we rethrow it (assuming we've intercepted it before it's reached the top-level global handlers), so\n * that we don't interfere with whatever effects the error might have had were the SDK not there. At that point, because\n * the error has been rethrown, it's possible for it to bubble up to some other code we've instrumented. If it's not\n * caught after that, it will bubble all the way up to the global handlers (which of course we also instrument). This\n * function helps us ensure that even if we encounter the same error more than once, we only record it the first time we\n * see it.\n *\n * Note: It will ignore primitives (always return `false` and not mark them as seen), as properties can't be set on\n * them. {@link: Object.objectify} can be used on exceptions to convert any that are primitives into their equivalent\n * object wrapper forms so that this check will always work. However, because we need to flag the exact object which\n * will get rethrown, and because that rethrowing happens outside of the event processing pipeline, the objectification\n * must be done before the exception captured.\n *\n * @param A thrown exception to check or flag as having been seen\n * @returns `true` if the exception has already been captured, `false` if not (with the side effect of marking it seen)\n */\nfunction checkOrSetAlreadyCaught(exception) {\n  if (isAlreadyCaptured(exception)) {\n    return true;\n  }\n\n  try {\n    // set it this way rather than by assignment so that it's not ennumerable and therefore isn't recorded by the\n    // `ExtraErrorData` integration\n    addNonEnumerableProperty(exception , '__sentry_captured__', true);\n  } catch (err) {\n    // `exception` is a primitive, so we can't mark it seen\n  }\n\n  return false;\n}\n\nfunction isAlreadyCaptured(exception) {\n  try {\n    return (exception ).__sentry_captured__;\n  } catch {} // eslint-disable-line no-empty\n}\n\nexport { addContextToFrame, addExceptionMechanism, addExceptionTypeValue, checkOrSetAlreadyCaught, getEventDescription, parseSemver, uuid4 };\n//# sourceMappingURL=misc.js.map\n", "import { GLOBAL_OBJ } from './worldwide.js';\n\nconst ONE_SECOND_IN_MS = 1000;\n\n/**\n * A partial definition of the [Performance Web API]{@link https://developer.mozilla.org/en-US/docs/Web/API/Performance}\n * for accessing a high-resolution monotonic clock.\n */\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using the Date API.\n */\nfunction dateTimestampInSeconds() {\n  return Date.now() / ONE_SECOND_IN_MS;\n}\n\n/**\n * Returns a wrapper around the native Performance API browser implementation, or undefined for browsers that do not\n * support the API.\n *\n * Wrapping the native API works around differences in behavior from different browsers.\n */\nfunction createUnixTimestampInSecondsFunc() {\n  const { performance } = GLOBAL_OBJ ;\n  if (!performance?.now) {\n    return dateTimestampInSeconds;\n  }\n\n  // Some browser and environments don't have a timeOrigin, so we fallback to\n  // using Date.now() to compute the starting time.\n  const approxStartingTimeOrigin = Date.now() - performance.now();\n  const timeOrigin = performance.timeOrigin == undefined ? approxStartingTimeOrigin : performance.timeOrigin;\n\n  // performance.now() is a monotonic clock, which means it starts at 0 when the process begins. To get the current\n  // wall clock time (actual UNIX timestamp), we need to add the starting time origin and the current time elapsed.\n  //\n  // TODO: This does not account for the case where the monotonic clock that powers performance.now() drifts from the\n  // wall clock time, which causes the returned timestamp to be inaccurate. We should investigate how to detect and\n  // correct for this.\n  // See: https://github.com/getsentry/sentry-javascript/issues/2590\n  // See: https://github.com/mdn/content/issues/4713\n  // See: https://dev.to/noamr/when-a-millisecond-is-not-a-millisecond-3h6\n  return () => {\n    return (timeOrigin + performance.now()) / ONE_SECOND_IN_MS;\n  };\n}\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using either the Performance or Date APIs, depending on the\n * availability of the Performance API.\n *\n * BUG: Note that because of how browsers implement the Performance API, the clock might stop when the computer is\n * asleep. This creates a skew between `dateTimestampInSeconds` and `timestampInSeconds`. The\n * skew can grow to arbitrary amounts like days, weeks or months.\n * See https://github.com/getsentry/sentry-javascript/issues/2590.\n */\nconst timestampInSeconds = createUnixTimestampInSecondsFunc();\n\n/**\n * Cached result of getBrowserTimeOrigin.\n */\nlet cachedTimeOrigin;\n\n/**\n * Gets the time origin and the mode used to determine it.\n */\nfunction getBrowserTimeOrigin() {\n  // Unfortunately browsers may report an inaccurate time origin data, through either performance.timeOrigin or\n  // performance.timing.navigationStart, which results in poor results in performance data. We only treat time origin\n  // data as reliable if they are within a reasonable threshold of the current time.\n\n  const { performance } = GLOBAL_OBJ ;\n  if (!performance?.now) {\n    return [undefined, 'none'];\n  }\n\n  const threshold = 3600 * 1000;\n  const performanceNow = performance.now();\n  const dateNow = Date.now();\n\n  // if timeOrigin isn't available set delta to threshold so it isn't used\n  const timeOriginDelta = performance.timeOrigin\n    ? Math.abs(performance.timeOrigin + performanceNow - dateNow)\n    : threshold;\n  const timeOriginIsReliable = timeOriginDelta < threshold;\n\n  // While performance.timing.navigationStart is deprecated in favor of performance.timeOrigin, performance.timeOrigin\n  // is not as widely supported. Namely, performance.timeOrigin is undefined in Safari as of writing.\n  // Also as of writing, performance.timing is not available in Web Workers in mainstream browsers, so it is not always\n  // a valid fallback. In the absence of an initial time provided by the browser, fallback to the current time from the\n  // Date API.\n  // eslint-disable-next-line deprecation/deprecation\n  const navigationStart = performance.timing?.navigationStart;\n  const hasNavigationStart = typeof navigationStart === 'number';\n  // if navigationStart isn't available set delta to threshold so it isn't used\n  const navigationStartDelta = hasNavigationStart ? Math.abs(navigationStart + performanceNow - dateNow) : threshold;\n  const navigationStartIsReliable = navigationStartDelta < threshold;\n\n  if (timeOriginIsReliable || navigationStartIsReliable) {\n    // Use the more reliable time origin\n    if (timeOriginDelta <= navigationStartDelta) {\n      return [performance.timeOrigin, 'timeOrigin'];\n    } else {\n      return [navigationStart, 'navigationStart'];\n    }\n  }\n\n  // Either both timeOrigin and navigationStart are skewed or neither is available, fallback to Date.\n  return [dateNow, 'dateNow'];\n}\n\n/**\n * The number of milliseconds since the UNIX epoch. This value is only usable in a browser, and only when the\n * performance API is available.\n */\nfunction browserPerformanceTimeOrigin() {\n  if (!cachedTimeOrigin) {\n    cachedTimeOrigin = getBrowserTimeOrigin();\n  }\n\n  return cachedTimeOrigin[0];\n}\n\nexport { browserPerformanceTimeOrigin, dateTimestampInSeconds, timestampInSeconds };\n//# sourceMappingURL=time.js.map\n", "import { uuid4 } from './utils-hoist/misc.js';\nimport { timestampInSeconds } from './utils-hoist/time.js';\n\n/**\n * Creates a new `Session` object by setting certain default parameters. If optional @param context\n * is passed, the passed properties are applied to the session object.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns a new `Session` object\n */\nfunction makeSession(context) {\n  // Both timestamp and started are in seconds since the UNIX epoch.\n  const startingTime = timestampInSeconds();\n\n  const session = {\n    sid: uuid4(),\n    init: true,\n    timestamp: startingTime,\n    started: startingTime,\n    duration: 0,\n    status: 'ok',\n    errors: 0,\n    ignoreDuration: false,\n    toJSON: () => sessionToJSON(session),\n  };\n\n  if (context) {\n    updateSession(session, context);\n  }\n\n  return session;\n}\n\n/**\n * Updates a session object with the properties passed in the context.\n *\n * Note that this function mutates the passed object and returns void.\n * (Had to do this instead of returning a new and updated session because closing and sending a session\n * makes an update to the session after it was passed to the sending logic.\n * @see Client.captureSession )\n *\n * @param session the `Session` to update\n * @param context the `SessionContext` holding the properties that should be updated in @param session\n */\n// eslint-disable-next-line complexity\nfunction updateSession(session, context = {}) {\n  if (context.user) {\n    if (!session.ipAddress && context.user.ip_address) {\n      session.ipAddress = context.user.ip_address;\n    }\n\n    if (!session.did && !context.did) {\n      session.did = context.user.id || context.user.email || context.user.username;\n    }\n  }\n\n  session.timestamp = context.timestamp || timestampInSeconds();\n\n  if (context.abnormal_mechanism) {\n    session.abnormal_mechanism = context.abnormal_mechanism;\n  }\n\n  if (context.ignoreDuration) {\n    session.ignoreDuration = context.ignoreDuration;\n  }\n  if (context.sid) {\n    // Good enough uuid validation. — Kamil\n    session.sid = context.sid.length === 32 ? context.sid : uuid4();\n  }\n  if (context.init !== undefined) {\n    session.init = context.init;\n  }\n  if (!session.did && context.did) {\n    session.did = `${context.did}`;\n  }\n  if (typeof context.started === 'number') {\n    session.started = context.started;\n  }\n  if (session.ignoreDuration) {\n    session.duration = undefined;\n  } else if (typeof context.duration === 'number') {\n    session.duration = context.duration;\n  } else {\n    const duration = session.timestamp - session.started;\n    session.duration = duration >= 0 ? duration : 0;\n  }\n  if (context.release) {\n    session.release = context.release;\n  }\n  if (context.environment) {\n    session.environment = context.environment;\n  }\n  if (!session.ipAddress && context.ipAddress) {\n    session.ipAddress = context.ipAddress;\n  }\n  if (!session.userAgent && context.userAgent) {\n    session.userAgent = context.userAgent;\n  }\n  if (typeof context.errors === 'number') {\n    session.errors = context.errors;\n  }\n  if (context.status) {\n    session.status = context.status;\n  }\n}\n\n/**\n * Closes a session by setting its status and updating the session object with it.\n * Internally calls `updateSession` to update the passed session object.\n *\n * Note that this function mutates the passed session (@see updateSession for explanation).\n *\n * @param session the `Session` object to be closed\n * @param status the `SessionStatus` with which the session was closed. If you don't pass a status,\n *               this function will keep the previously set status, unless it was `'ok'` in which case\n *               it is changed to `'exited'`.\n */\nfunction closeSession(session, status) {\n  let context = {};\n  if (status) {\n    context = { status };\n  } else if (session.status === 'ok') {\n    context = { status: 'exited' };\n  }\n\n  updateSession(session, context);\n}\n\n/**\n * Serializes a passed session object to a JSON object with a slightly different structure.\n * This is necessary because the Sentry backend requires a slightly different schema of a session\n * than the one the JS SDKs use internally.\n *\n * @param session the session to be converted\n *\n * @returns a JSON object of the passed session\n */\nfunction sessionToJSON(session) {\n  return {\n    sid: `${session.sid}`,\n    init: session.init,\n    // Make sure that sec is converted to ms for date constructor\n    started: new Date(session.started * 1000).toISOString(),\n    timestamp: new Date(session.timestamp * 1000).toISOString(),\n    status: session.status,\n    errors: session.errors,\n    did: typeof session.did === 'number' || typeof session.did === 'string' ? `${session.did}` : undefined,\n    duration: session.duration,\n    abnormal_mechanism: session.abnormal_mechanism,\n    attrs: {\n      release: session.release,\n      environment: session.environment,\n      ip_address: session.ipAddress,\n      user_agent: session.userAgent,\n    },\n  };\n}\n\nexport { closeSession, makeSession, updateSession };\n//# sourceMappingURL=session.js.map\n", "/**\n * Shallow merge two objects.\n * Does not mutate the passed in objects.\n * Undefined/empty values in the merge object will overwrite existing values.\n *\n * By default, this merges 2 levels deep.\n */\nfunction merge(initialObj, mergeObj, levels = 2) {\n  // If the merge value is not an object, or we have no merge levels left,\n  // we just set the value to the merge value\n  if (!mergeObj || typeof mergeObj !== 'object' || levels <= 0) {\n    return mergeObj;\n  }\n\n  // If the merge object is an empty object, and the initial object is not undefined, we return the initial object\n  if (initialObj && Object.keys(mergeObj).length === 0) {\n    return initialObj;\n  }\n\n  // Clone object\n  const output = { ...initialObj };\n\n  // Merge values into output, resursively\n  for (const key in mergeObj) {\n    if (Object.prototype.hasOwnProperty.call(mergeObj, key)) {\n      output[key] = merge(output[key], mergeObj[key], levels - 1);\n    }\n  }\n\n  return output;\n}\n\nexport { merge };\n//# sourceMappingURL=merge.js.map\n", "import { addNonEnumerableProperty } from '../utils-hoist/object.js';\n\nconst SCOPE_SPAN_FIELD = '_sentrySpan';\n\n/**\n * Set the active span for a given scope.\n * NOTE: This should NOT be used directly, but is only used internally by the trace methods.\n */\nfunction _setSpanForScope(scope, span) {\n  if (span) {\n    addNonEnumerableProperty(scope , SCOPE_SPAN_FIELD, span);\n  } else {\n    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n    delete (scope )[SCOPE_SPAN_FIELD];\n  }\n}\n\n/**\n * Get the active span for a given scope.\n * NOTE: This should NOT be used directly, but is only used internally by the trace methods.\n */\nfunction _getSpanForScope(scope) {\n  return scope[SCOPE_SPAN_FIELD];\n}\n\nexport { _getSpanForScope, _setSpanForScope };\n//# sourceMappingURL=spanOnScope.js.map\n", "import { uuid4 } from './misc.js';\n\n/**\n * Generate a random, valid trace ID.\n */\nfunction generateTraceId() {\n  return uuid4();\n}\n\n/**\n * Generate a random, valid span ID.\n */\nfunction generateSpanId() {\n  return uuid4().substring(16);\n}\n\nexport { generateSpanId, generateTraceId };\n//# sourceMappingURL=propagationContext.js.map\n", "import { updateSession } from './session.js';\nimport { merge } from './utils/merge.js';\nimport { _setSpanForScope, _getSpanForScope } from './utils/spanOnScope.js';\nimport { isPlainObject } from './utils-hoist/is.js';\nimport { logger } from './utils-hoist/logger.js';\nimport { uuid4 } from './utils-hoist/misc.js';\nimport { generateTraceId } from './utils-hoist/propagationContext.js';\nimport { truncate } from './utils-hoist/string.js';\nimport { dateTimestampInSeconds } from './utils-hoist/time.js';\n\n/**\n * Default value for maximum number of breadcrumbs added to an event.\n */\nconst DEFAULT_MAX_BREADCRUMBS = 100;\n\n/**\n * A context to be used for capturing an event.\n * This can either be a Scope, or a partial ScopeContext,\n * or a callback that receives the current scope and returns a new scope to use.\n */\n\n/**\n * Holds additional event information.\n */\nclass Scope {\n  /** Flag if notifying is happening. */\n\n  /** Callback for client to receive scope changes. */\n\n  /** Callback list that will be called during event processing. */\n\n  /** Array of breadcrumbs. */\n\n  /** User */\n\n  /** Tags */\n\n  /** Extra */\n\n  /** Contexts */\n\n  /** Attachments */\n\n  /** Propagation Context for distributed tracing */\n\n  /**\n   * A place to stash data which is needed at some point in the SDK's event processing pipeline but which shouldn't get\n   * sent to Sentry\n   */\n\n  /** Fingerprint */\n\n  /** Severity */\n\n  /**\n   * Transaction Name\n   *\n   * IMPORTANT: The transaction name on the scope has nothing to do with root spans/transaction objects.\n   * It's purpose is to assign a transaction to the scope that's added to non-transaction events.\n   */\n\n  /** Session */\n\n  /** The client on this scope */\n\n  /** Contains the last event id of a captured event.  */\n\n  // NOTE: Any field which gets added here should get added not only to the constructor but also to the `clone` method.\n\n   constructor() {\n    this._notifyingListeners = false;\n    this._scopeListeners = [];\n    this._eventProcessors = [];\n    this._breadcrumbs = [];\n    this._attachments = [];\n    this._user = {};\n    this._tags = {};\n    this._extra = {};\n    this._contexts = {};\n    this._sdkProcessingMetadata = {};\n    this._propagationContext = {\n      traceId: generateTraceId(),\n      sampleRand: Math.random(),\n    };\n  }\n\n  /**\n   * Clone all data from this scope into a new scope.\n   */\n   clone() {\n    const newScope = new Scope();\n    newScope._breadcrumbs = [...this._breadcrumbs];\n    newScope._tags = { ...this._tags };\n    newScope._extra = { ...this._extra };\n    newScope._contexts = { ...this._contexts };\n    if (this._contexts.flags) {\n      // We need to copy the `values` array so insertions on a cloned scope\n      // won't affect the original array.\n      newScope._contexts.flags = {\n        values: [...this._contexts.flags.values],\n      };\n    }\n\n    newScope._user = this._user;\n    newScope._level = this._level;\n    newScope._session = this._session;\n    newScope._transactionName = this._transactionName;\n    newScope._fingerprint = this._fingerprint;\n    newScope._eventProcessors = [...this._eventProcessors];\n    newScope._attachments = [...this._attachments];\n    newScope._sdkProcessingMetadata = { ...this._sdkProcessingMetadata };\n    newScope._propagationContext = { ...this._propagationContext };\n    newScope._client = this._client;\n    newScope._lastEventId = this._lastEventId;\n\n    _setSpanForScope(newScope, _getSpanForScope(this));\n\n    return newScope;\n  }\n\n  /**\n   * Update the client assigned to this scope.\n   * Note that not every scope will have a client assigned - isolation scopes & the global scope will generally not have a client,\n   * as well as manually created scopes.\n   */\n   setClient(client) {\n    this._client = client;\n  }\n\n  /**\n   * Set the ID of the last captured error event.\n   * This is generally only captured on the isolation scope.\n   */\n   setLastEventId(lastEventId) {\n    this._lastEventId = lastEventId;\n  }\n\n  /**\n   * Get the client assigned to this scope.\n   */\n   getClient() {\n    return this._client ;\n  }\n\n  /**\n   * Get the ID of the last captured error event.\n   * This is generally only available on the isolation scope.\n   */\n   lastEventId() {\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n   addScopeListener(callback) {\n    this._scopeListeners.push(callback);\n  }\n\n  /**\n   * Add an event processor that will be called before an event is sent.\n   */\n   addEventProcessor(callback) {\n    this._eventProcessors.push(callback);\n    return this;\n  }\n\n  /**\n   * Set the user for this scope.\n   * Set to `null` to unset the user.\n   */\n   setUser(user) {\n    // If null is passed we want to unset everything, but still define keys,\n    // so that later down in the pipeline any existing values are cleared.\n    this._user = user || {\n      email: undefined,\n      id: undefined,\n      ip_address: undefined,\n      username: undefined,\n    };\n\n    if (this._session) {\n      updateSession(this._session, { user });\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Get the user from this scope.\n   */\n   getUser() {\n    return this._user;\n  }\n\n  /**\n   * Set an object that will be merged into existing tags on the scope,\n   * and will be sent as tags data with the event.\n   */\n   setTags(tags) {\n    this._tags = {\n      ...this._tags,\n      ...tags,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set a single tag that will be sent as tags data with the event.\n   */\n   setTag(key, value) {\n    this._tags = { ...this._tags, [key]: value };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set an object that will be merged into existing extra on the scope,\n   * and will be sent as extra data with the event.\n   */\n   setExtras(extras) {\n    this._extra = {\n      ...this._extra,\n      ...extras,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set a single key:value extra entry that will be sent as extra data with the event.\n   */\n   setExtra(key, extra) {\n    this._extra = { ...this._extra, [key]: extra };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the fingerprint on the scope to send with the events.\n   * @param {string[]} fingerprint Fingerprint to group events in Sentry.\n   */\n   setFingerprint(fingerprint) {\n    this._fingerprint = fingerprint;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the level on the scope for future events.\n   */\n   setLevel(level) {\n    this._level = level;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the transaction name on the scope so that the name of e.g. taken server route or\n   * the page location is attached to future events.\n   *\n   * IMPORTANT: Calling this function does NOT change the name of the currently active\n   * root span. If you want to change the name of the active root span, use\n   * `Sentry.updateSpanName(rootSpan, 'new name')` instead.\n   *\n   * By default, the SDK updates the scope's transaction name automatically on sensible\n   * occasions, such as a page navigation or when handling a new request on the server.\n   */\n   setTransactionName(name) {\n    this._transactionName = name;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets context data with the given name.\n   * Data passed as context will be normalized. You can also pass `null` to unset the context.\n   * Note that context data will not be merged - calling `setContext` will overwrite an existing context with the same key.\n   */\n   setContext(key, context) {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts[key] = context;\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set the session for the scope.\n   */\n   setSession(session) {\n    if (!session) {\n      delete this._session;\n    } else {\n      this._session = session;\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Get the session from the scope.\n   */\n   getSession() {\n    return this._session;\n  }\n\n  /**\n   * Updates the scope with provided data. Can work in three variations:\n   * - plain object containing updatable attributes\n   * - Scope instance that'll extract the attributes from\n   * - callback function that'll receive the current scope as an argument and allow for modifications\n   */\n   update(captureContext) {\n    if (!captureContext) {\n      return this;\n    }\n\n    const scopeToMerge = typeof captureContext === 'function' ? captureContext(this) : captureContext;\n\n    const scopeInstance =\n      scopeToMerge instanceof Scope\n        ? scopeToMerge.getScopeData()\n        : isPlainObject(scopeToMerge)\n          ? (captureContext )\n          : undefined;\n\n    const { tags, extra, user, contexts, level, fingerprint = [], propagationContext } = scopeInstance || {};\n\n    this._tags = { ...this._tags, ...tags };\n    this._extra = { ...this._extra, ...extra };\n    this._contexts = { ...this._contexts, ...contexts };\n\n    if (user && Object.keys(user).length) {\n      this._user = user;\n    }\n\n    if (level) {\n      this._level = level;\n    }\n\n    if (fingerprint.length) {\n      this._fingerprint = fingerprint;\n    }\n\n    if (propagationContext) {\n      this._propagationContext = propagationContext;\n    }\n\n    return this;\n  }\n\n  /**\n   * Clears the current scope and resets its properties.\n   * Note: The client will not be cleared.\n   */\n   clear() {\n    // client is not cleared here on purpose!\n    this._breadcrumbs = [];\n    this._tags = {};\n    this._extra = {};\n    this._user = {};\n    this._contexts = {};\n    this._level = undefined;\n    this._transactionName = undefined;\n    this._fingerprint = undefined;\n    this._session = undefined;\n    _setSpanForScope(this, undefined);\n    this._attachments = [];\n    this.setPropagationContext({ traceId: generateTraceId(), sampleRand: Math.random() });\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Adds a breadcrumb to the scope.\n   * By default, the last 100 breadcrumbs are kept.\n   */\n   addBreadcrumb(breadcrumb, maxBreadcrumbs) {\n    const maxCrumbs = typeof maxBreadcrumbs === 'number' ? maxBreadcrumbs : DEFAULT_MAX_BREADCRUMBS;\n\n    // No data has been changed, so don't notify scope listeners\n    if (maxCrumbs <= 0) {\n      return this;\n    }\n\n    const mergedBreadcrumb = {\n      timestamp: dateTimestampInSeconds(),\n      ...breadcrumb,\n      // Breadcrumb messages can theoretically be infinitely large and they're held in memory so we truncate them not to leak (too much) memory\n      message: breadcrumb.message ? truncate(breadcrumb.message, 2048) : breadcrumb.message,\n    };\n\n    this._breadcrumbs.push(mergedBreadcrumb);\n    if (this._breadcrumbs.length > maxCrumbs) {\n      this._breadcrumbs = this._breadcrumbs.slice(-maxCrumbs);\n      this._client?.recordDroppedEvent('buffer_overflow', 'log_item');\n    }\n\n    this._notifyScopeListeners();\n\n    return this;\n  }\n\n  /**\n   * Get the last breadcrumb of the scope.\n   */\n   getLastBreadcrumb() {\n    return this._breadcrumbs[this._breadcrumbs.length - 1];\n  }\n\n  /**\n   * Clear all breadcrumbs from the scope.\n   */\n   clearBreadcrumbs() {\n    this._breadcrumbs = [];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Add an attachment to the scope.\n   */\n   addAttachment(attachment) {\n    this._attachments.push(attachment);\n    return this;\n  }\n\n  /**\n   * Clear all attachments from the scope.\n   */\n   clearAttachments() {\n    this._attachments = [];\n    return this;\n  }\n\n  /**\n   * Get the data of this scope, which should be applied to an event during processing.\n   */\n   getScopeData() {\n    return {\n      breadcrumbs: this._breadcrumbs,\n      attachments: this._attachments,\n      contexts: this._contexts,\n      tags: this._tags,\n      extra: this._extra,\n      user: this._user,\n      level: this._level,\n      fingerprint: this._fingerprint || [],\n      eventProcessors: this._eventProcessors,\n      propagationContext: this._propagationContext,\n      sdkProcessingMetadata: this._sdkProcessingMetadata,\n      transactionName: this._transactionName,\n      span: _getSpanForScope(this),\n    };\n  }\n\n  /**\n   * Add data which will be accessible during event processing but won't get sent to Sentry.\n   */\n   setSDKProcessingMetadata(newData) {\n    this._sdkProcessingMetadata = merge(this._sdkProcessingMetadata, newData, 2);\n    return this;\n  }\n\n  /**\n   * Add propagation context to the scope, used for distributed tracing\n   */\n   setPropagationContext(context) {\n    this._propagationContext = context;\n    return this;\n  }\n\n  /**\n   * Get propagation context from the scope, used for distributed tracing\n   */\n   getPropagationContext() {\n    return this._propagationContext;\n  }\n\n  /**\n   * Capture an exception for this scope.\n   *\n   * @returns {string} The id of the captured Sentry event.\n   */\n   captureException(exception, hint) {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture exception!');\n      return eventId;\n    }\n\n    const syntheticException = new Error('Sentry syntheticException');\n\n    this._client.captureException(\n      exception,\n      {\n        originalException: exception,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * Capture a message for this scope.\n   *\n   * @returns {string} The id of the captured message.\n   */\n   captureMessage(message, level, hint) {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture message!');\n      return eventId;\n    }\n\n    const syntheticException = new Error(message);\n\n    this._client.captureMessage(\n      message,\n      level,\n      {\n        originalException: message,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * Capture a Sentry event for this scope.\n   *\n   * @returns {string} The id of the captured event.\n   */\n   captureEvent(event, hint) {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture event!');\n      return eventId;\n    }\n\n    this._client.captureEvent(event, { ...hint, event_id: eventId }, this);\n\n    return eventId;\n  }\n\n  /**\n   * This will be called on every set call.\n   */\n   _notifyScopeListeners() {\n    // We need this check for this._notifyingListeners to be able to work on scope during updates\n    // If this check is not here we'll produce endless recursion when something is done with the scope\n    // during the callback.\n    if (!this._notifyingListeners) {\n      this._notifyingListeners = true;\n      this._scopeListeners.forEach(callback => {\n        callback(this);\n      });\n      this._notifyingListeners = false;\n    }\n  }\n}\n\nexport { Scope };\n//# sourceMappingURL=scope.js.map\n", "import { getGlobalSingleton } from './carrier.js';\nimport { Scope } from './scope.js';\n\n/** Get the default current scope. */\nfunction getDefaultCurrentScope() {\n  return getGlobalSingleton('defaultCurrentScope', () => new Scope());\n}\n\n/** Get the default isolation scope. */\nfunction getDefaultIsolationScope() {\n  return getGlobalSingleton('defaultIsolationScope', () => new Scope());\n}\n\nexport { getDefaultCurrentScope, getDefaultIsolationScope };\n//# sourceMappingURL=defaultScopes.js.map\n", "import { getDefaultCurrentScope, getDefaultIsolationScope } from '../defaultScopes.js';\nimport { Scope } from '../scope.js';\nimport { isThenable } from '../utils-hoist/is.js';\nimport { getMainCarrier, getSentryCarrier } from '../carrier.js';\n\n/**\n * This is an object that holds a stack of scopes.\n */\nclass AsyncContextStack {\n\n   constructor(scope, isolationScope) {\n    let assignedScope;\n    if (!scope) {\n      assignedScope = new Scope();\n    } else {\n      assignedScope = scope;\n    }\n\n    let assignedIsolationScope;\n    if (!isolationScope) {\n      assignedIsolationScope = new Scope();\n    } else {\n      assignedIsolationScope = isolationScope;\n    }\n\n    // scope stack for domains or the process\n    this._stack = [{ scope: assignedScope }];\n    this._isolationScope = assignedIsolationScope;\n  }\n\n  /**\n   * Fork a scope for the stack.\n   */\n   withScope(callback) {\n    const scope = this._pushScope();\n\n    let maybePromiseResult;\n    try {\n      maybePromiseResult = callback(scope);\n    } catch (e) {\n      this._popScope();\n      throw e;\n    }\n\n    if (isThenable(maybePromiseResult)) {\n      // @ts-expect-error - isThenable returns the wrong type\n      return maybePromiseResult.then(\n        res => {\n          this._popScope();\n          return res;\n        },\n        e => {\n          this._popScope();\n          throw e;\n        },\n      );\n    }\n\n    this._popScope();\n    return maybePromiseResult;\n  }\n\n  /**\n   * Get the client of the stack.\n   */\n   getClient() {\n    return this.getStackTop().client ;\n  }\n\n  /**\n   * Returns the scope of the top stack.\n   */\n   getScope() {\n    return this.getStackTop().scope;\n  }\n\n  /**\n   * Get the isolation scope for the stack.\n   */\n   getIsolationScope() {\n    return this._isolationScope;\n  }\n\n  /**\n   * Returns the topmost scope layer in the order domain > local > process.\n   */\n   getStackTop() {\n    return this._stack[this._stack.length - 1] ;\n  }\n\n  /**\n   * Push a scope to the stack.\n   */\n   _pushScope() {\n    // We want to clone the content of prev scope\n    const scope = this.getScope().clone();\n    this._stack.push({\n      client: this.getClient(),\n      scope,\n    });\n    return scope;\n  }\n\n  /**\n   * Pop a scope from the stack.\n   */\n   _popScope() {\n    if (this._stack.length <= 1) return false;\n    return !!this._stack.pop();\n  }\n}\n\n/**\n * Get the global async context stack.\n * This will be removed during the v8 cycle and is only here to make migration easier.\n */\nfunction getAsyncContextStack() {\n  const registry = getMainCarrier();\n  const sentry = getSentryCarrier(registry);\n\n  return (sentry.stack = sentry.stack || new AsyncContextStack(getDefaultCurrentScope(), getDefaultIsolationScope()));\n}\n\nfunction withScope(callback) {\n  return getAsyncContextStack().withScope(callback);\n}\n\nfunction withSetScope(scope, callback) {\n  const stack = getAsyncContextStack() ;\n  return stack.withScope(() => {\n    stack.getStackTop().scope = scope;\n    return callback(scope);\n  });\n}\n\nfunction withIsolationScope(callback) {\n  return getAsyncContextStack().withScope(() => {\n    return callback(getAsyncContextStack().getIsolationScope());\n  });\n}\n\n/**\n * Get the stack-based async context strategy.\n */\nfunction getStackAsyncContextStrategy() {\n  return {\n    withIsolationScope,\n    withScope,\n    withSetScope,\n    withSetIsolationScope: (_isolationScope, callback) => {\n      return withIsolationScope(callback);\n    },\n    getCurrentScope: () => getAsyncContextStack().getScope(),\n    getIsolationScope: () => getAsyncContextStack().getIsolationScope(),\n  };\n}\n\nexport { AsyncContextStack, getStackAsyncContextStrategy };\n//# sourceMappingURL=stackStrategy.js.map\n", "import { getMainCarrier, getSentryCarrier } from '../carrier.js';\nimport { getStackAsyncContextStrategy } from './stackStrategy.js';\n\n/**\n * @private Private API with no semver guarantees!\n *\n * Sets the global async context strategy\n */\nfunction setAsyncContextStrategy(strategy) {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n  const sentry = getSentryCarrier(registry);\n  sentry.acs = strategy;\n}\n\n/**\n * Get the current async context strategy.\n * If none has been setup, the default will be used.\n */\nfunction getAsyncContextStrategy(carrier) {\n  const sentry = getSentryCarrier(carrier);\n\n  if (sentry.acs) {\n    return sentry.acs;\n  }\n\n  // Otherwise, use the default one (stack)\n  return getStackAsyncContextStrategy();\n}\n\nexport { getAsyncContextStrategy, setAsyncContextStrategy };\n//# sourceMappingURL=index.js.map\n", "import { getAsyncContextStrategy } from './asyncContext/index.js';\nimport { getMainCarrier, getGlobalSingleton } from './carrier.js';\nimport { Scope } from './scope.js';\nimport { generateSpanId } from './utils-hoist/propagationContext.js';\n\n/**\n * Get the currently active scope.\n */\nfunction getCurrentScope() {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  return acs.getCurrentScope();\n}\n\n/**\n * Get the currently active isolation scope.\n * The isolation scope is active for the current execution context.\n */\nfunction getIsolationScope() {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  return acs.getIsolationScope();\n}\n\n/**\n * Get the global scope.\n * This scope is applied to _all_ events.\n */\nfunction getGlobalScope() {\n  return getGlobalSingleton('globalScope', () => new Scope());\n}\n\n/**\n * Creates a new scope with and executes the given operation within.\n * The scope is automatically removed once the operation\n * finishes or throws.\n */\n\n/**\n * Either creates a new active scope, or sets the given scope as active scope in the given callback.\n */\nfunction withScope(\n  ...rest\n) {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n\n  // If a scope is defined, we want to make this the active scope instead of the default one\n  if (rest.length === 2) {\n    const [scope, callback] = rest;\n\n    if (!scope) {\n      return acs.withScope(callback);\n    }\n\n    return acs.withSetScope(scope, callback);\n  }\n\n  return acs.withScope(rest[0]);\n}\n\n/**\n * Attempts to fork the current isolation scope and the current scope based on the current async context strategy. If no\n * async context strategy is set, the isolation scope and the current scope will not be forked (this is currently the\n * case, for example, in the browser).\n *\n * Usage of this function in environments without async context strategy is discouraged and may lead to unexpected behaviour.\n *\n * This function is intended for Sentry SDK and SDK integration development. It is not recommended to be used in \"normal\"\n * applications directly because it comes with pitfalls. Use at your own risk!\n */\n\n/**\n * Either creates a new active isolation scope, or sets the given isolation scope as active scope in the given callback.\n */\nfunction withIsolationScope(\n  ...rest\n\n) {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n\n  // If a scope is defined, we want to make this the active scope instead of the default one\n  if (rest.length === 2) {\n    const [isolationScope, callback] = rest;\n\n    if (!isolationScope) {\n      return acs.withIsolationScope(callback);\n    }\n\n    return acs.withSetIsolationScope(isolationScope, callback);\n  }\n\n  return acs.withIsolationScope(rest[0]);\n}\n\n/**\n * Get the currently active client.\n */\nfunction getClient() {\n  return getCurrentScope().getClient();\n}\n\n/**\n * Get a trace context for the given scope.\n */\nfunction getTraceContextFromScope(scope) {\n  const propagationContext = scope.getPropagationContext();\n\n  const { traceId, parentSpanId, propagationSpanId } = propagationContext;\n\n  const traceContext = {\n    trace_id: traceId,\n    span_id: propagationSpanId || generateSpanId(),\n  };\n\n  if (parentSpanId) {\n    traceContext.parent_span_id = parentSpanId;\n  }\n\n  return traceContext;\n}\n\nexport { getClient, getCurrentScope, getGlobalScope, getIsolationScope, getTraceContextFromScope, withIsolationScope, withScope };\n//# sourceMappingURL=currentScopes.js.map\n", "/**\n * Use this attribute to represent the source of a span.\n * Should be one of: custom, url, route, view, component, task, unknown\n *\n */\nconst SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = 'sentry.source';\n\n/**\n * Attributes that holds the sample rate that was locally applied to a span.\n * If this attribute is not defined, it means that the span inherited a sampling decision.\n *\n * NOTE: Is only defined on root spans.\n */\nconst SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = 'sentry.sample_rate';\n\n/**\n * Attribute holding the sample rate of the previous trace.\n * This is used to sample consistently across subsequent traces in the browser SDK.\n *\n * Note: Only defined on root spans, if opted into consistent sampling\n */\nconst SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE = 'sentry.previous_trace_sample_rate';\n\n/**\n * Use this attribute to represent the operation of a span.\n */\nconst SEMANTIC_ATTRIBUTE_SENTRY_OP = 'sentry.op';\n\n/**\n * Use this attribute to represent the origin of a span.\n */\nconst SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = 'sentry.origin';\n\n/** The reason why an idle span finished. */\nconst SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON = 'sentry.idle_span_finish_reason';\n\n/** The unit of a measurement, which may be stored as a TimedEvent. */\nconst SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT = 'sentry.measurement_unit';\n\n/** The value of a measurement, which may be stored as a TimedEvent. */\nconst SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE = 'sentry.measurement_value';\n\n/**\n * A custom span name set by users guaranteed to be taken over any automatically\n * inferred name. This attribute is removed before the span is sent.\n *\n * @internal only meant for internal SDK usage\n * @hidden\n */\nconst SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME = 'sentry.custom_span_name';\n\n/**\n * The id of the profile that this span occurred in.\n */\nconst SEMANTIC_ATTRIBUTE_PROFILE_ID = 'sentry.profile_id';\n\nconst SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME = 'sentry.exclusive_time';\n\nconst SEMANTIC_ATTRIBUTE_CACHE_HIT = 'cache.hit';\n\nconst SEMANTIC_ATTRIBUTE_CACHE_KEY = 'cache.key';\n\nconst SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE = 'cache.item_size';\n\n/** TODO: Remove these once we update to latest semantic conventions */\nconst SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD = 'http.request.method';\nconst SEMANTIC_ATTRIBUTE_URL_FULL = 'url.full';\n\n/**\n * A span link attribute to mark the link as a special span link.\n *\n * Known values:\n * - `previous_trace`: The span links to the frontend root span of the previous trace.\n * - `next_trace`: The span links to the frontend root span of the next trace. (Not set by the SDK)\n *\n * Other values may be set as appropriate.\n * @see https://develop.sentry.dev/sdk/telemetry/traces/span-links/#link-types\n */\nconst SEMANTIC_LINK_ATTRIBUTE_LINK_TYPE = 'sentry.link.type';\n\nexport { SEMANTIC_ATTRIBUTE_CACHE_HIT, SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE, SEMANTIC_ATTRIBUTE_CACHE_KEY, SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME, SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD, SEMANTIC_ATTRIBUTE_PROFILE_ID, SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME, SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON, SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT, SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE, SEMANTIC_ATTRIBUTE_SENTRY_OP, SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE, SEMANTIC_ATTRIBUTE_URL_FULL, SEMANTIC_LINK_ATTRIBUTE_LINK_TYPE };\n//# sourceMappingURL=semanticAttributes.js.map\n", "const SPAN_STATUS_UNSET = 0;\nconst SPAN_STATUS_OK = 1;\nconst SPAN_STATUS_ERROR = 2;\n\n/**\n * Converts a HTTP status code into a sentry status with a message.\n *\n * @param httpStatus The HTTP response status code.\n * @returns The span status or unknown_error.\n */\n// https://develop.sentry.dev/sdk/event-payloads/span/\nfunction getSpanStatusFromHttpCode(httpStatus) {\n  if (httpStatus < 400 && httpStatus >= 100) {\n    return { code: SPAN_STATUS_OK };\n  }\n\n  if (httpStatus >= 400 && httpStatus < 500) {\n    switch (httpStatus) {\n      case 401:\n        return { code: SPAN_STATUS_ERROR, message: 'unauthenticated' };\n      case 403:\n        return { code: SPAN_STATUS_ERROR, message: 'permission_denied' };\n      case 404:\n        return { code: SPAN_STATUS_ERROR, message: 'not_found' };\n      case 409:\n        return { code: SPAN_STATUS_ERROR, message: 'already_exists' };\n      case 413:\n        return { code: SPAN_STATUS_ERROR, message: 'failed_precondition' };\n      case 429:\n        return { code: SPAN_STATUS_ERROR, message: 'resource_exhausted' };\n      case 499:\n        return { code: SPAN_STATUS_ERROR, message: 'cancelled' };\n      default:\n        return { code: SPAN_STATUS_ERROR, message: 'invalid_argument' };\n    }\n  }\n\n  if (httpStatus >= 500 && httpStatus < 600) {\n    switch (httpStatus) {\n      case 501:\n        return { code: SPAN_STATUS_ERROR, message: 'unimplemented' };\n      case 503:\n        return { code: SPAN_STATUS_ERROR, message: 'unavailable' };\n      case 504:\n        return { code: SPAN_STATUS_ERROR, message: 'deadline_exceeded' };\n      default:\n        return { code: SPAN_STATUS_ERROR, message: 'internal_error' };\n    }\n  }\n\n  return { code: SPAN_STATUS_ERROR, message: 'unknown_error' };\n}\n\n/**\n * Sets the Http status attributes on the current span based on the http code.\n * Additionally, the span's status is updated, depending on the http code.\n */\nfunction setHttpStatus(span, httpStatus) {\n  span.setAttribute('http.response.status_code', httpStatus);\n\n  const spanStatus = getSpanStatusFromHttpCode(httpStatus);\n  if (spanStatus.message !== 'unknown_error') {\n    span.setStatus(spanStatus);\n  }\n}\n\nexport { SPAN_STATUS_ERROR, SPAN_STATUS_OK, SPAN_STATUS_UNSET, getSpanStatusFromHttpCode, setHttpStatus };\n//# sourceMappingURL=spanstatus.js.map\n", "import { addNonEnumerableProperty } from '../utils-hoist/object.js';\n\nconst SCOPE_ON_START_SPAN_FIELD = '_sentryScope';\nconst ISOLATION_SCOPE_ON_START_SPAN_FIELD = '_sentryIsolationScope';\n\n/** Store the scope & isolation scope for a span, which can the be used when it is finished. */\nfunction setCapturedScopesOnSpan(span, scope, isolationScope) {\n  if (span) {\n    addNonEnumerableProperty(span, ISOLATION_SCOPE_ON_START_SPAN_FIELD, isolationScope);\n    addNonEnumerableProperty(span, SCOPE_ON_START_SPAN_FIELD, scope);\n  }\n}\n\n/**\n * Grabs the scope and isolation scope off a span that were active when the span was started.\n */\nfunction getCapturedScopesOnSpan(span) {\n  return {\n    scope: (span )[SCOPE_ON_START_SPAN_FIELD],\n    isolationScope: (span )[ISOLATION_SCOPE_ON_START_SPAN_FIELD],\n  };\n}\n\nexport { getCapturedScopesOnSpan, setCapturedScopesOnSpan };\n//# sourceMappingURL=utils.js.map\n", "import { DEBUG_BUILD } from '../debug-build.js';\nimport { isString } from './is.js';\nimport { logger } from './logger.js';\n\nconst SENTRY_BAGGAGE_KEY_PREFIX = 'sentry-';\n\nconst SENTRY_BAGGAGE_KEY_PREFIX_REGEX = /^sentry-/;\n\n/**\n * Max length of a serialized baggage string\n *\n * https://www.w3.org/TR/baggage/#limits\n */\nconst MAX_BAGGAGE_STRING_LENGTH = 8192;\n\n/**\n * Takes a baggage header and turns it into Dynamic Sampling Context, by extracting all the \"sentry-\" prefixed values\n * from it.\n *\n * @param baggageHeader A very bread definition of a baggage header as it might appear in various frameworks.\n * @returns The Dynamic Sampling Context that was found on `baggageHeader`, if there was any, `undefined` otherwise.\n */\nfunction baggageHeaderToDynamicSamplingContext(\n  // Very liberal definition of what any incoming header might look like\n  baggageHeader,\n) {\n  const baggageObject = parseBaggageHeader(baggageHeader);\n\n  if (!baggageObject) {\n    return undefined;\n  }\n\n  // Read all \"sentry-\" prefixed values out of the baggage object and put it onto a dynamic sampling context object.\n  const dynamicSamplingContext = Object.entries(baggageObject).reduce((acc, [key, value]) => {\n    if (key.match(SENTRY_BAGGAGE_KEY_PREFIX_REGEX)) {\n      const nonPrefixedKey = key.slice(SENTRY_BAGGAGE_KEY_PREFIX.length);\n      acc[nonPrefixedKey] = value;\n    }\n    return acc;\n  }, {});\n\n  // Only return a dynamic sampling context object if there are keys in it.\n  // A keyless object means there were no sentry values on the header, which means that there is no DSC.\n  if (Object.keys(dynamicSamplingContext).length > 0) {\n    return dynamicSamplingContext ;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Turns a Dynamic Sampling Object into a baggage header by prefixing all the keys on the object with \"sentry-\".\n *\n * @param dynamicSamplingContext The Dynamic Sampling Context to turn into a header. For convenience and compatibility\n * with the `getDynamicSamplingContext` method on the Transaction class ,this argument can also be `undefined`. If it is\n * `undefined` the function will return `undefined`.\n * @returns a baggage header, created from `dynamicSamplingContext`, or `undefined` either if `dynamicSamplingContext`\n * was `undefined`, or if `dynamicSamplingContext` didn't contain any values.\n */\nfunction dynamicSamplingContextToSentryBaggageHeader(\n  // this also takes undefined for convenience and bundle size in other places\n  dynamicSamplingContext,\n) {\n  if (!dynamicSamplingContext) {\n    return undefined;\n  }\n\n  // Prefix all DSC keys with \"sentry-\" and put them into a new object\n  const sentryPrefixedDSC = Object.entries(dynamicSamplingContext).reduce(\n    (acc, [dscKey, dscValue]) => {\n      if (dscValue) {\n        acc[`${SENTRY_BAGGAGE_KEY_PREFIX}${dscKey}`] = dscValue;\n      }\n      return acc;\n    },\n    {},\n  );\n\n  return objectToBaggageHeader(sentryPrefixedDSC);\n}\n\n/**\n * Take a baggage header and parse it into an object.\n */\nfunction parseBaggageHeader(\n  baggageHeader,\n) {\n  if (!baggageHeader || (!isString(baggageHeader) && !Array.isArray(baggageHeader))) {\n    return undefined;\n  }\n\n  if (Array.isArray(baggageHeader)) {\n    // Combine all baggage headers into one object containing the baggage values so we can later read the Sentry-DSC-values from it\n    return baggageHeader.reduce((acc, curr) => {\n      const currBaggageObject = baggageHeaderToObject(curr);\n      Object.entries(currBaggageObject).forEach(([key, value]) => {\n        acc[key] = value;\n      });\n      return acc;\n    }, {});\n  }\n\n  return baggageHeaderToObject(baggageHeader);\n}\n\n/**\n * Will parse a baggage header, which is a simple key-value map, into a flat object.\n *\n * @param baggageHeader The baggage header to parse.\n * @returns a flat object containing all the key-value pairs from `baggageHeader`.\n */\nfunction baggageHeaderToObject(baggageHeader) {\n  return baggageHeader\n    .split(',')\n    .map(baggageEntry =>\n      baggageEntry.split('=').map(keyOrValue => {\n        try {\n          return decodeURIComponent(keyOrValue.trim());\n        } catch {\n          // We ignore errors here, e.g. if the value cannot be URL decoded.\n          // This will then be skipped in the next step\n          return;\n        }\n      }),\n    )\n    .reduce((acc, [key, value]) => {\n      if (key && value) {\n        acc[key] = value;\n      }\n      return acc;\n    }, {});\n}\n\n/**\n * Turns a flat object (key-value pairs) into a baggage header, which is also just key-value pairs.\n *\n * @param object The object to turn into a baggage header.\n * @returns a baggage header string, or `undefined` if the object didn't have any values, since an empty baggage header\n * is not spec compliant.\n */\nfunction objectToBaggageHeader(object) {\n  if (Object.keys(object).length === 0) {\n    // An empty baggage header is not spec compliant: We return undefined.\n    return undefined;\n  }\n\n  return Object.entries(object).reduce((baggageHeader, [objectKey, objectValue], currentIndex) => {\n    const baggageEntry = `${encodeURIComponent(objectKey)}=${encodeURIComponent(objectValue)}`;\n    const newBaggageHeader = currentIndex === 0 ? baggageEntry : `${baggageHeader},${baggageEntry}`;\n    if (newBaggageHeader.length > MAX_BAGGAGE_STRING_LENGTH) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Not adding key: ${objectKey} with val: ${objectValue} to baggage header due to exceeding baggage size limits.`,\n        );\n      return baggageHeader;\n    } else {\n      return newBaggageHeader;\n    }\n  }, '');\n}\n\nexport { MAX_BAGGAGE_STRING_LENGTH, SENTRY_BAGGAGE_KEY_PREFIX, SENTRY_BAGGAGE_KEY_PREFIX_REGEX, baggageHeaderToDynamicSamplingContext, dynamicSamplingContextToSentryBaggageHeader, objectToBaggageHeader, parseBaggageHeader };\n//# sourceMappingURL=baggage.js.map\n", "import { getAsyncContextStrategy } from '../asyncContext/index.js';\nimport { getMainCarrier } from '../carrier.js';\nimport { getCurrentScope } from '../currentScopes.js';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, SEMANTIC_ATTRIBUTE_SENTRY_OP, SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE } from '../semanticAttributes.js';\nimport { SPAN_STATUS_UNSET, SPAN_STATUS_OK } from '../tracing/spanstatus.js';\nimport { getCapturedScopesOnSpan } from '../tracing/utils.js';\nimport { consoleSandbox } from '../utils-hoist/logger.js';\nimport { addNonEnumerableProperty } from '../utils-hoist/object.js';\nimport { generateSpanId } from '../utils-hoist/propagationContext.js';\nimport { timestampInSeconds } from '../utils-hoist/time.js';\nimport { generateSentryTraceHeader } from '../utils-hoist/tracing.js';\nimport { _getSpanForScope } from './spanOnScope.js';\n\n// These are aligned with OpenTelemetry trace flags\nconst TRACE_FLAG_NONE = 0x0;\nconst TRACE_FLAG_SAMPLED = 0x1;\n\nlet hasShownSpanDropWarning = false;\n\n/**\n * Convert a span to a trace context, which can be sent as the `trace` context in an event.\n * By default, this will only include trace_id, span_id & parent_span_id.\n * If `includeAllData` is true, it will also include data, op, status & origin.\n */\nfunction spanToTransactionTraceContext(span) {\n  const { spanId: span_id, traceId: trace_id } = span.spanContext();\n  const { data, op, parent_span_id, status, origin, links } = spanToJSON(span);\n\n  return {\n    parent_span_id,\n    span_id,\n    trace_id,\n    data,\n    op,\n    status,\n    origin,\n    links,\n  };\n}\n\n/**\n * Convert a span to a trace context, which can be sent as the `trace` context in a non-transaction event.\n */\nfunction spanToTraceContext(span) {\n  const { spanId, traceId: trace_id, isRemote } = span.spanContext();\n\n  // If the span is remote, we use a random/virtual span as span_id to the trace context,\n  // and the remote span as parent_span_id\n  const parent_span_id = isRemote ? spanId : spanToJSON(span).parent_span_id;\n  const scope = getCapturedScopesOnSpan(span).scope;\n\n  const span_id = isRemote ? scope?.getPropagationContext().propagationSpanId || generateSpanId() : spanId;\n\n  return {\n    parent_span_id,\n    span_id,\n    trace_id,\n  };\n}\n\n/**\n * Convert a Span to a Sentry trace header.\n */\nfunction spanToTraceHeader(span) {\n  const { traceId, spanId } = span.spanContext();\n  const sampled = spanIsSampled(span);\n  return generateSentryTraceHeader(traceId, spanId, sampled);\n}\n\n/**\n *  Converts the span links array to a flattened version to be sent within an envelope.\n *\n *  If the links array is empty, it returns `undefined` so the empty value can be dropped before it's sent.\n */\nfunction convertSpanLinksForEnvelope(links) {\n  if (links && links.length > 0) {\n    return links.map(({ context: { spanId, traceId, traceFlags, ...restContext }, attributes }) => ({\n      span_id: spanId,\n      trace_id: traceId,\n      sampled: traceFlags === TRACE_FLAG_SAMPLED,\n      attributes,\n      ...restContext,\n    }));\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Convert a span time input into a timestamp in seconds.\n */\nfunction spanTimeInputToSeconds(input) {\n  if (typeof input === 'number') {\n    return ensureTimestampInSeconds(input);\n  }\n\n  if (Array.isArray(input)) {\n    // See {@link HrTime} for the array-based time format\n    return input[0] + input[1] / 1e9;\n  }\n\n  if (input instanceof Date) {\n    return ensureTimestampInSeconds(input.getTime());\n  }\n\n  return timestampInSeconds();\n}\n\n/**\n * Converts a timestamp to second, if it was in milliseconds, or keeps it as second.\n */\nfunction ensureTimestampInSeconds(timestamp) {\n  const isMs = timestamp > 9999999999;\n  return isMs ? timestamp / 1000 : timestamp;\n}\n\n/**\n * Convert a span to a JSON representation.\n */\n// Note: Because of this, we currently have a circular type dependency (which we opted out of in package.json).\n// This is not avoidable as we need `spanToJSON` in `spanUtils.ts`, which in turn is needed by `span.ts` for backwards compatibility.\n// And `spanToJSON` needs the Span class from `span.ts` to check here.\nfunction spanToJSON(span) {\n  if (spanIsSentrySpan(span)) {\n    return span.getSpanJSON();\n  }\n\n  const { spanId: span_id, traceId: trace_id } = span.spanContext();\n\n  // Handle a span from @opentelemetry/sdk-base-trace's `Span` class\n  if (spanIsOpenTelemetrySdkTraceBaseSpan(span)) {\n    const { attributes, startTime, name, endTime, status, links } = span;\n\n    // In preparation for the next major of OpenTelemetry, we want to support\n    // looking up the parent span id according to the new API\n    // In OTel v1, the parent span id is accessed as `parentSpanId`\n    // In OTel v2, the parent span id is accessed as `spanId` on the `parentSpanContext`\n    const parentSpanId =\n      'parentSpanId' in span\n        ? span.parentSpanId\n        : 'parentSpanContext' in span\n          ? (span.parentSpanContext )?.spanId\n          : undefined;\n\n    return {\n      span_id,\n      trace_id,\n      data: attributes,\n      description: name,\n      parent_span_id: parentSpanId,\n      start_timestamp: spanTimeInputToSeconds(startTime),\n      // This is [0,0] by default in OTEL, in which case we want to interpret this as no end time\n      timestamp: spanTimeInputToSeconds(endTime) || undefined,\n      status: getStatusMessage(status),\n      op: attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP],\n      origin: attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] ,\n      links: convertSpanLinksForEnvelope(links),\n    };\n  }\n\n  // Finally, at least we have `spanContext()`....\n  // This should not actually happen in reality, but we need to handle it for type safety.\n  return {\n    span_id,\n    trace_id,\n    start_timestamp: 0,\n    data: {},\n  };\n}\n\nfunction spanIsOpenTelemetrySdkTraceBaseSpan(span) {\n  const castSpan = span ;\n  return !!castSpan.attributes && !!castSpan.startTime && !!castSpan.name && !!castSpan.endTime && !!castSpan.status;\n}\n\n/** Exported only for tests. */\n\n/**\n * Sadly, due to circular dependency checks we cannot actually import the Span class here and check for instanceof.\n * :( So instead we approximate this by checking if it has the `getSpanJSON` method.\n */\nfunction spanIsSentrySpan(span) {\n  return typeof (span ).getSpanJSON === 'function';\n}\n\n/**\n * Returns true if a span is sampled.\n * In most cases, you should just use `span.isRecording()` instead.\n * However, this has a slightly different semantic, as it also returns false if the span is finished.\n * So in the case where this distinction is important, use this method.\n */\nfunction spanIsSampled(span) {\n  // We align our trace flags with the ones OpenTelemetry use\n  // So we also check for sampled the same way they do.\n  const { traceFlags } = span.spanContext();\n  return traceFlags === TRACE_FLAG_SAMPLED;\n}\n\n/** Get the status message to use for a JSON representation of a span. */\nfunction getStatusMessage(status) {\n  if (!status || status.code === SPAN_STATUS_UNSET) {\n    return undefined;\n  }\n\n  if (status.code === SPAN_STATUS_OK) {\n    return 'ok';\n  }\n\n  return status.message || 'unknown_error';\n}\n\nconst CHILD_SPANS_FIELD = '_sentryChildSpans';\nconst ROOT_SPAN_FIELD = '_sentryRootSpan';\n\n/**\n * Adds an opaque child span reference to a span.\n */\nfunction addChildSpanToSpan(span, childSpan) {\n  // We store the root span reference on the child span\n  // We need this for `getRootSpan()` to work\n  const rootSpan = span[ROOT_SPAN_FIELD] || span;\n  addNonEnumerableProperty(childSpan , ROOT_SPAN_FIELD, rootSpan);\n\n  // We store a list of child spans on the parent span\n  // We need this for `getSpanDescendants()` to work\n  if (span[CHILD_SPANS_FIELD]) {\n    span[CHILD_SPANS_FIELD].add(childSpan);\n  } else {\n    addNonEnumerableProperty(span, CHILD_SPANS_FIELD, new Set([childSpan]));\n  }\n}\n\n/** This is only used internally by Idle Spans. */\nfunction removeChildSpanFromSpan(span, childSpan) {\n  if (span[CHILD_SPANS_FIELD]) {\n    span[CHILD_SPANS_FIELD].delete(childSpan);\n  }\n}\n\n/**\n * Returns an array of the given span and all of its descendants.\n */\nfunction getSpanDescendants(span) {\n  const resultSet = new Set();\n\n  function addSpanChildren(span) {\n    // This exit condition is required to not infinitely loop in case of a circular dependency.\n    if (resultSet.has(span)) {\n      return;\n      // We want to ignore unsampled spans (e.g. non recording spans)\n    } else if (spanIsSampled(span)) {\n      resultSet.add(span);\n      const childSpans = span[CHILD_SPANS_FIELD] ? Array.from(span[CHILD_SPANS_FIELD]) : [];\n      for (const childSpan of childSpans) {\n        addSpanChildren(childSpan);\n      }\n    }\n  }\n\n  addSpanChildren(span);\n\n  return Array.from(resultSet);\n}\n\n/**\n * Returns the root span of a given span.\n */\nfunction getRootSpan(span) {\n  return span[ROOT_SPAN_FIELD] || span;\n}\n\n/**\n * Returns the currently active span.\n */\nfunction getActiveSpan() {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  if (acs.getActiveSpan) {\n    return acs.getActiveSpan();\n  }\n\n  return _getSpanForScope(getCurrentScope());\n}\n\n/**\n * Logs a warning once if `beforeSendSpan` is used to drop spans.\n */\nfunction showSpanDropWarning() {\n  if (!hasShownSpanDropWarning) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn(\n        '[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.',\n      );\n    });\n    hasShownSpanDropWarning = true;\n  }\n}\n\n/**\n * Updates the name of the given span and ensures that the span name is not\n * overwritten by the Sentry SDK.\n *\n * Use this function instead of `span.updateName()` if you want to make sure that\n * your name is kept. For some spans, for example root `http.server` spans the\n * Sentry SDK would otherwise overwrite the span name with a high-quality name\n * it infers when the span ends.\n *\n * Use this function in server code or when your span is started on the server\n * and on the client (browser). If you only update a span name on the client,\n * you can also use `span.updateName()` the SDK does not overwrite the name.\n *\n * @param span - The span to update the name of.\n * @param name - The name to set on the span.\n */\nfunction updateSpanName(span, name) {\n  span.updateName(name);\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'custom',\n    [SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME]: name,\n  });\n}\n\nexport { TRACE_FLAG_NONE, TRACE_FLAG_SAMPLED, addChildSpanToSpan, convertSpanLinksForEnvelope, getActiveSpan, getRootSpan, getSpanDescendants, getStatusMessage, removeChildSpanFromSpan, showSpanDropWarning, spanIsSampled, spanTimeInputToSeconds, spanToJSON, spanToTraceContext, spanToTraceHeader, spanToTransactionTraceContext, updateSpanName };\n//# sourceMappingURL=spanUtils.js.map\n", "const STACKTRACE_FRAME_LIMIT = 50;\nconst UNKNOWN_FUNCTION = '?';\n// Used to sanitize webpack (error: *) wrapped stack errors\nconst WEBPACK_ERROR_REGEXP = /\\(error: (.*)\\)/;\nconst STRIP_FRAME_REGEXP = /captureMessage|captureException/;\n\n/**\n * Creates a stack parser with the supplied line parsers\n *\n * StackFrames are returned in the correct order for Sentry Exception\n * frames and with Sentry SDK internal frames removed from the top and bottom\n *\n */\nfunction createStackParser(...parsers) {\n  const sortedParsers = parsers.sort((a, b) => a[0] - b[0]).map(p => p[1]);\n\n  return (stack, skipFirstLines = 0, framesToPop = 0) => {\n    const frames = [];\n    const lines = stack.split('\\n');\n\n    for (let i = skipFirstLines; i < lines.length; i++) {\n      const line = lines[i] ;\n      // Ignore lines over 1kb as they are unlikely to be stack frames.\n      // Many of the regular expressions use backtracking which results in run time that increases exponentially with\n      // input size. Huge strings can result in hangs/Denial of Service:\n      // https://github.com/getsentry/sentry-javascript/issues/2286\n      if (line.length > 1024) {\n        continue;\n      }\n\n      // https://github.com/getsentry/sentry-javascript/issues/5459\n      // Remove webpack (error: *) wrappers\n      const cleanedLine = WEBPACK_ERROR_REGEXP.test(line) ? line.replace(WEBPACK_ERROR_REGEXP, '$1') : line;\n\n      // https://github.com/getsentry/sentry-javascript/issues/7813\n      // Skip Error: lines\n      if (cleanedLine.match(/\\S*Error: /)) {\n        continue;\n      }\n\n      for (const parser of sortedParsers) {\n        const frame = parser(cleanedLine);\n\n        if (frame) {\n          frames.push(frame);\n          break;\n        }\n      }\n\n      if (frames.length >= STACKTRACE_FRAME_LIMIT + framesToPop) {\n        break;\n      }\n    }\n\n    return stripSentryFramesAndReverse(frames.slice(framesToPop));\n  };\n}\n\n/**\n * Gets a stack parser implementation from Options.stackParser\n * @see Options\n *\n * If options contains an array of line parsers, it is converted into a parser\n */\nfunction stackParserFromStackParserOptions(stackParser) {\n  if (Array.isArray(stackParser)) {\n    return createStackParser(...stackParser);\n  }\n  return stackParser;\n}\n\n/**\n * Removes Sentry frames from the top and bottom of the stack if present and enforces a limit of max number of frames.\n * Assumes stack input is ordered from top to bottom and returns the reverse representation so call site of the\n * function that caused the crash is the last frame in the array.\n * @hidden\n */\nfunction stripSentryFramesAndReverse(stack) {\n  if (!stack.length) {\n    return [];\n  }\n\n  const localStack = Array.from(stack);\n\n  // If stack starts with one of our API calls, remove it (starts, meaning it's the top of the stack - aka last call)\n  if (/sentryWrapped/.test(getLastStackFrame(localStack).function || '')) {\n    localStack.pop();\n  }\n\n  // Reversing in the middle of the procedure allows us to just pop the values off the stack\n  localStack.reverse();\n\n  // If stack ends with one of our internal API calls, remove it (ends, meaning it's the bottom of the stack - aka top-most call)\n  if (STRIP_FRAME_REGEXP.test(getLastStackFrame(localStack).function || '')) {\n    localStack.pop();\n\n    // When using synthetic events, we will have a 2 levels deep stack, as `new Error('Sentry syntheticException')`\n    // is produced within the scope itself, making it:\n    //\n    //   Sentry.captureException()\n    //   scope.captureException()\n    //\n    // instead of just the top `Sentry` call itself.\n    // This forces us to possibly strip an additional frame in the exact same was as above.\n    if (STRIP_FRAME_REGEXP.test(getLastStackFrame(localStack).function || '')) {\n      localStack.pop();\n    }\n  }\n\n  return localStack.slice(0, STACKTRACE_FRAME_LIMIT).map(frame => ({\n    ...frame,\n    filename: frame.filename || getLastStackFrame(localStack).filename,\n    function: frame.function || UNKNOWN_FUNCTION,\n  }));\n}\n\nfunction getLastStackFrame(arr) {\n  return arr[arr.length - 1] || {};\n}\n\nconst defaultFunctionName = '<anonymous>';\n\n/**\n * Safely extract function name from itself\n */\nfunction getFunctionName(fn) {\n  try {\n    if (!fn || typeof fn !== 'function') {\n      return defaultFunctionName;\n    }\n    return fn.name || defaultFunctionName;\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    return defaultFunctionName;\n  }\n}\n\n/**\n * Get's stack frames from an event without needing to check for undefined properties.\n */\nfunction getFramesFromEvent(event) {\n  const exception = event.exception;\n\n  if (exception) {\n    const frames = [];\n    try {\n      // @ts-expect-error Object could be undefined\n      exception.values.forEach(value => {\n        // @ts-expect-error Value could be undefined\n        if (value.stacktrace.frames) {\n          // @ts-expect-error Value could be undefined\n          frames.push(...value.stacktrace.frames);\n        }\n      });\n      return frames;\n    } catch (_oO) {\n      return undefined;\n    }\n  }\n  return undefined;\n}\n\nexport { UNKNOWN_FUNCTION, createStackParser, getFramesFromEvent, getFunctionName, stackParserFromStackParserOptions, stripSentryFramesAndReverse };\n//# sourceMappingURL=stacktrace.js.map\n", "import { getClient } from '../currentScopes.js';\n\n// Treeshakable guard to remove all code related to tracing\n\n/**\n * Determines if span recording is currently enabled.\n *\n * Spans are recorded when at least one of `tracesSampleRate` and `tracesSampler`\n * is defined in the SDK config. This function does not make any assumption about\n * sampling decisions, it only checks if the SDK is configured to record spans.\n *\n * Important: This function only determines if span recording is enabled. Trace\n * continuation and propagation is separately controlled and not covered by this function.\n * If this function returns `false`, traces can still be propagated (which is what\n * we refer to by \"Tracing without Performance\")\n * @see https://develop.sentry.dev/sdk/telemetry/traces/tracing-without-performance/\n *\n * @param maybeOptions An SDK options object to be passed to this function.\n * If this option is not provided, the function will use the current client's options.\n */\nfunction hasSpansEnabled(\n  maybeOptions,\n) {\n  if (typeof __SENTRY_TRACING__ === 'boolean' && !__SENTRY_TRACING__) {\n    return false;\n  }\n\n  const options = maybeOptions || getClient()?.getOptions();\n  return (\n    !!options &&\n    // Note: This check is `!= null`, meaning \"nullish\". `0` is not \"nullish\", `undefined` and `null` are. (This comment was brought to you by 15 minutes of questioning life)\n    (options.tracesSampleRate != null || !!options.tracesSampler)\n  );\n}\n\n/**\n * @see JSDoc of `hasSpansEnabled`\n * @deprecated Use `hasSpansEnabled` instead, which is a more accurately named version of this function.\n * This function will be removed in the next major version of the SDK.\n */\n// TODO(v10): Remove this export\nconst hasTracingEnabled = hasSpansEnabled;\n\nexport { hasSpansEnabled, hasTracingEnabled };\n//# sourceMappingURL=hasSpansEnabled.js.map\n", "const DEFAULT_ENVIRONMENT = 'production';\n\nexport { DEFAULT_ENVIRONMENT };\n//# sourceMappingURL=constants.js.map\n", "import { DEBUG_BUILD } from '../debug-build.js';\nimport { consoleSandbox, logger } from './logger.js';\n\n/** Regular expression used to extract org ID from a DSN host. */\nconst ORG_ID_REGEX = /^o(\\d+)\\./;\n\n/** Regular expression used to parse a Dsn. */\nconst DSN_REGEX = /^(?:(\\w+):)\\/\\/(?:(\\w+)(?::(\\w+)?)?@)([\\w.-]+)(?::(\\d+))?\\/(.+)/;\n\nfunction isValidProtocol(protocol) {\n  return protocol === 'http' || protocol === 'https';\n}\n\n/**\n * Renders the string representation of this Dsn.\n *\n * By default, this will render the public representation without the password\n * component. To get the deprecated private representation, set `withPassword`\n * to true.\n *\n * @param withPassword When set to true, the password will be included.\n */\nfunction dsnToString(dsn, withPassword = false) {\n  const { host, path, pass, port, projectId, protocol, publicKey } = dsn;\n  return (\n    `${protocol}://${publicKey}${withPassword && pass ? `:${pass}` : ''}` +\n    `@${host}${port ? `:${port}` : ''}/${path ? `${path}/` : path}${projectId}`\n  );\n}\n\n/**\n * Parses a Dsn from a given string.\n *\n * @param str A Dsn as string\n * @returns Dsn as DsnComponents or undefined if @param str is not a valid DSN string\n */\nfunction dsnFromString(str) {\n  const match = DSN_REGEX.exec(str);\n\n  if (!match) {\n    // This should be logged to the console\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.error(`Invalid Sentry Dsn: ${str}`);\n    });\n    return undefined;\n  }\n\n  const [protocol, publicKey, pass = '', host = '', port = '', lastPath = ''] = match.slice(1);\n  let path = '';\n  let projectId = lastPath;\n\n  const split = projectId.split('/');\n  if (split.length > 1) {\n    path = split.slice(0, -1).join('/');\n    projectId = split.pop() ;\n  }\n\n  if (projectId) {\n    const projectMatch = projectId.match(/^\\d+/);\n    if (projectMatch) {\n      projectId = projectMatch[0];\n    }\n  }\n\n  return dsnFromComponents({ host, pass, path, projectId, port, protocol: protocol , publicKey });\n}\n\nfunction dsnFromComponents(components) {\n  return {\n    protocol: components.protocol,\n    publicKey: components.publicKey || '',\n    pass: components.pass || '',\n    host: components.host,\n    port: components.port || '',\n    path: components.path || '',\n    projectId: components.projectId,\n  };\n}\n\nfunction validateDsn(dsn) {\n  if (!DEBUG_BUILD) {\n    return true;\n  }\n\n  const { port, projectId, protocol } = dsn;\n\n  const requiredComponents = ['protocol', 'publicKey', 'host', 'projectId'];\n  const hasMissingRequiredComponent = requiredComponents.find(component => {\n    if (!dsn[component]) {\n      logger.error(`Invalid Sentry Dsn: ${component} missing`);\n      return true;\n    }\n    return false;\n  });\n\n  if (hasMissingRequiredComponent) {\n    return false;\n  }\n\n  if (!projectId.match(/^\\d+$/)) {\n    logger.error(`Invalid Sentry Dsn: Invalid projectId ${projectId}`);\n    return false;\n  }\n\n  if (!isValidProtocol(protocol)) {\n    logger.error(`Invalid Sentry Dsn: Invalid protocol ${protocol}`);\n    return false;\n  }\n\n  if (port && isNaN(parseInt(port, 10))) {\n    logger.error(`Invalid Sentry Dsn: Invalid port ${port}`);\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Extract the org ID from a DSN host.\n *\n * @param host The host from a DSN\n * @returns The org ID if found, undefined otherwise\n */\nfunction extractOrgIdFromDsnHost(host) {\n  const match = host.match(ORG_ID_REGEX);\n\n  return match?.[1];\n}\n\n/**\n * Creates a valid Sentry Dsn object, identifying a Sentry instance and project.\n * @returns a valid DsnComponents object or `undefined` if @param from is an invalid DSN source\n */\nfunction makeDsn(from) {\n  const components = typeof from === 'string' ? dsnFromString(from) : dsnFromComponents(from);\n  if (!components || !validateDsn(components)) {\n    return undefined;\n  }\n  return components;\n}\n\nexport { dsnFromString, dsnToString, extractOrgIdFromDsnHost, makeDsn };\n//# sourceMappingURL=dsn.js.map\n", "import { DEFAULT_ENVIRONMENT } from '../constants.js';\nimport { getClient } from '../currentScopes.js';\nimport { SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE, SEMANTIC_ATTRIBUTE_SENTRY_SOURCE } from '../semanticAttributes.js';\nimport { hasSpansEnabled } from '../utils/hasSpansEnabled.js';\nimport { getRootSpan, spanToJSON, spanIsSampled } from '../utils/spanUtils.js';\nimport { baggageHeaderToDynamicSamplingContext, dynamicSamplingContextToSentryBaggageHeader } from '../utils-hoist/baggage.js';\nimport { extractOrgIdFromDsnHost } from '../utils-hoist/dsn.js';\nimport { addNonEnumerableProperty } from '../utils-hoist/object.js';\nimport { getCapturedScopesOnSpan } from './utils.js';\n\n/**\n * If you change this value, also update the terser plugin config to\n * avoid minification of the object property!\n */\nconst FROZEN_DSC_FIELD = '_frozenDsc';\n\n/**\n * Freeze the given DSC on the given span.\n */\nfunction freezeDscOnSpan(span, dsc) {\n  const spanWithMaybeDsc = span ;\n  addNonEnumerableProperty(spanWithMaybeDsc, FROZEN_DSC_FIELD, dsc);\n}\n\n/**\n * Creates a dynamic sampling context from a client.\n *\n * Dispatches the `createDsc` lifecycle hook as a side effect.\n */\nfunction getDynamicSamplingContextFromClient(trace_id, client) {\n  const options = client.getOptions();\n\n  const { publicKey: public_key, host } = client.getDsn() || {};\n\n  let org_id;\n  if (options.orgId) {\n    org_id = String(options.orgId);\n  } else if (host) {\n    org_id = extractOrgIdFromDsnHost(host);\n  }\n\n  // Instead of conditionally adding non-undefined values, we add them and then remove them if needed\n  // otherwise, the order of baggage entries changes, which \"breaks\" a bunch of tests etc.\n  const dsc = {\n    environment: options.environment || DEFAULT_ENVIRONMENT,\n    release: options.release,\n    public_key,\n    trace_id,\n    org_id,\n  };\n\n  client.emit('createDsc', dsc);\n\n  return dsc;\n}\n\n/**\n * Get the dynamic sampling context for the currently active scopes.\n */\nfunction getDynamicSamplingContextFromScope(client, scope) {\n  const propagationContext = scope.getPropagationContext();\n  return propagationContext.dsc || getDynamicSamplingContextFromClient(propagationContext.traceId, client);\n}\n\n/**\n * Creates a dynamic sampling context from a span (and client and scope)\n *\n * @param span the span from which a few values like the root span name and sample rate are extracted.\n *\n * @returns a dynamic sampling context\n */\nfunction getDynamicSamplingContextFromSpan(span) {\n  const client = getClient();\n  if (!client) {\n    return {};\n  }\n\n  const rootSpan = getRootSpan(span);\n  const rootSpanJson = spanToJSON(rootSpan);\n  const rootSpanAttributes = rootSpanJson.data;\n  const traceState = rootSpan.spanContext().traceState;\n\n  // The span sample rate that was locally applied to the root span should also always be applied to the DSC, even if the DSC is frozen.\n  // This is so that the downstream traces/services can use parentSampleRate in their `tracesSampler` to make consistent sampling decisions across the entire trace.\n  const rootSpanSampleRate =\n    traceState?.get('sentry.sample_rate') ??\n    rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE] ??\n    rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE];\n\n  function applyLocalSampleRateToDsc(dsc) {\n    if (typeof rootSpanSampleRate === 'number' || typeof rootSpanSampleRate === 'string') {\n      dsc.sample_rate = `${rootSpanSampleRate}`;\n    }\n    return dsc;\n  }\n\n  // For core implementation, we freeze the DSC onto the span as a non-enumerable property\n  const frozenDsc = (rootSpan )[FROZEN_DSC_FIELD];\n  if (frozenDsc) {\n    return applyLocalSampleRateToDsc(frozenDsc);\n  }\n\n  // For OpenTelemetry, we freeze the DSC on the trace state\n  const traceStateDsc = traceState?.get('sentry.dsc');\n\n  // If the span has a DSC, we want it to take precedence\n  const dscOnTraceState = traceStateDsc && baggageHeaderToDynamicSamplingContext(traceStateDsc);\n\n  if (dscOnTraceState) {\n    return applyLocalSampleRateToDsc(dscOnTraceState);\n  }\n\n  // Else, we generate it from the span\n  const dsc = getDynamicSamplingContextFromClient(span.spanContext().traceId, client);\n\n  // We don't want to have a transaction name in the DSC if the source is \"url\" because URLs might contain PII\n  const source = rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n\n  // after JSON conversion, txn.name becomes jsonSpan.description\n  const name = rootSpanJson.description;\n  if (source !== 'url' && name) {\n    dsc.transaction = name;\n  }\n\n  // How can we even land here with hasSpansEnabled() returning false?\n  // Otel creates a Non-recording span in Tracing Without Performance mode when handling incoming requests\n  // So we end up with an active span that is not sampled (neither positively nor negatively)\n  if (hasSpansEnabled()) {\n    dsc.sampled = String(spanIsSampled(rootSpan));\n    dsc.sample_rand =\n      // In OTEL we store the sample rand on the trace state because we cannot access scopes for NonRecordingSpans\n      // The Sentry OTEL SpanSampler takes care of writing the sample rand on the root span\n      traceState?.get('sentry.sample_rand') ??\n      // On all other platforms we can actually get the scopes from a root span (we use this as a fallback)\n      getCapturedScopesOnSpan(rootSpan).scope?.getPropagationContext().sampleRand.toString();\n  }\n\n  applyLocalSampleRateToDsc(dsc);\n\n  client.emit('createDsc', dsc, rootSpan);\n\n  return dsc;\n}\n\n/**\n * Convert a Span to a baggage header.\n */\nfunction spanToBaggageHeader(span) {\n  const dsc = getDynamicSamplingContextFromSpan(span);\n  return dynamicSamplingContextToSentryBaggageHeader(dsc);\n}\n\nexport { freezeDscOnSpan, getDynamicSamplingContextFromClient, getDynamicSamplingContextFromScope, getDynamicSamplingContextFromSpan, spanToBaggageHeader };\n//# sourceMappingURL=dynamicSamplingContext.js.map\n", "import { isVueViewModel, isSyntheticEvent } from './is.js';\nimport { convertToPlainObject } from './object.js';\nimport { getFunctionName } from './stacktrace.js';\n\n/**\n * Recursively normalizes the given object.\n *\n * - Creates a copy to prevent original input mutation\n * - Skips non-enumerable properties\n * - When stringifying, calls `toJSON` if implemented\n * - Removes circular references\n * - Translates non-serializable values (`undefined`/`NaN`/functions) to serializable format\n * - Translates known global objects/classes to a string representations\n * - Takes care of `Error` object serialization\n * - Optionally limits depth of final output\n * - Optionally limits number of properties/elements included in any single object/array\n *\n * @param input The object to be normalized.\n * @param depth The max depth to which to normalize the object. (Anything deeper stringified whole.)\n * @param maxProperties The max number of elements or properties to be included in any single array or\n * object in the normalized output.\n * @returns A normalized version of the object, or `\"**non-serializable**\"` if any errors are thrown during normalization.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction normalize(input, depth = 100, maxProperties = +Infinity) {\n  try {\n    // since we're at the outermost level, we don't provide a key\n    return visit('', input, depth, maxProperties);\n  } catch (err) {\n    return { ERROR: `**non-serializable** (${err})` };\n  }\n}\n\n/** JSDoc */\nfunction normalizeToSize(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  object,\n  // Default Node.js REPL depth\n  depth = 3,\n  // 100kB, as 200kB is max payload size, so half sounds reasonable\n  maxSize = 100 * 1024,\n) {\n  const normalized = normalize(object, depth);\n\n  if (jsonSize(normalized) > maxSize) {\n    return normalizeToSize(object, depth - 1, maxSize);\n  }\n\n  return normalized ;\n}\n\n/**\n * Visits a node to perform normalization on it\n *\n * @param key The key corresponding to the given node\n * @param value The node to be visited\n * @param depth Optional number indicating the maximum recursion depth\n * @param maxProperties Optional maximum number of properties/elements included in any single object/array\n * @param memo Optional Memo class handling decycling\n */\nfunction visit(\n  key,\n  value,\n  depth = +Infinity,\n  maxProperties = +Infinity,\n  memo = memoBuilder(),\n) {\n  const [memoize, unmemoize] = memo;\n\n  // Get the simple cases out of the way first\n  if (\n    value == null || // this matches null and undefined -> eqeq not eqeqeq\n    ['boolean', 'string'].includes(typeof value) ||\n    (typeof value === 'number' && Number.isFinite(value))\n  ) {\n    return value ;\n  }\n\n  const stringified = stringifyValue(key, value);\n\n  // Anything we could potentially dig into more (objects or arrays) will have come back as `\"[object XXXX]\"`.\n  // Everything else will have already been serialized, so if we don't see that pattern, we're done.\n  if (!stringified.startsWith('[object ')) {\n    return stringified;\n  }\n\n  // From here on, we can assert that `value` is either an object or an array.\n\n  // Do not normalize objects that we know have already been normalized. As a general rule, the\n  // \"__sentry_skip_normalization__\" property should only be used sparingly and only should only be set on objects that\n  // have already been normalized.\n  if ((value )['__sentry_skip_normalization__']) {\n    return value ;\n  }\n\n  // We can set `__sentry_override_normalization_depth__` on an object to ensure that from there\n  // We keep a certain amount of depth.\n  // This should be used sparingly, e.g. we use it for the redux integration to ensure we get a certain amount of state.\n  const remainingDepth =\n    typeof (value )['__sentry_override_normalization_depth__'] === 'number'\n      ? ((value )['__sentry_override_normalization_depth__'] )\n      : depth;\n\n  // We're also done if we've reached the max depth\n  if (remainingDepth === 0) {\n    // At this point we know `serialized` is a string of the form `\"[object XXXX]\"`. Clean it up so it's just `\"[XXXX]\"`.\n    return stringified.replace('object ', '');\n  }\n\n  // If we've already visited this branch, bail out, as it's circular reference. If not, note that we're seeing it now.\n  if (memoize(value)) {\n    return '[Circular ~]';\n  }\n\n  // If the value has a `toJSON` method, we call it to extract more information\n  const valueWithToJSON = value ;\n  if (valueWithToJSON && typeof valueWithToJSON.toJSON === 'function') {\n    try {\n      const jsonValue = valueWithToJSON.toJSON();\n      // We need to normalize the return value of `.toJSON()` in case it has circular references\n      return visit('', jsonValue, remainingDepth - 1, maxProperties, memo);\n    } catch (err) {\n      // pass (The built-in `toJSON` failed, but we can still try to do it ourselves)\n    }\n  }\n\n  // At this point we know we either have an object or an array, we haven't seen it before, and we're going to recurse\n  // because we haven't yet reached the max depth. Create an accumulator to hold the results of visiting each\n  // property/entry, and keep track of the number of items we add to it.\n  const normalized = (Array.isArray(value) ? [] : {}) ;\n  let numAdded = 0;\n\n  // Before we begin, convert`Error` and`Event` instances into plain objects, since some of each of their relevant\n  // properties are non-enumerable and otherwise would get missed.\n  const visitable = convertToPlainObject(value );\n\n  for (const visitKey in visitable) {\n    // Avoid iterating over fields in the prototype if they've somehow been exposed to enumeration.\n    if (!Object.prototype.hasOwnProperty.call(visitable, visitKey)) {\n      continue;\n    }\n\n    if (numAdded >= maxProperties) {\n      normalized[visitKey] = '[MaxProperties ~]';\n      break;\n    }\n\n    // Recursively visit all the child nodes\n    const visitValue = visitable[visitKey];\n    normalized[visitKey] = visit(visitKey, visitValue, remainingDepth - 1, maxProperties, memo);\n\n    numAdded++;\n  }\n\n  // Once we've visited all the branches, remove the parent from memo storage\n  unmemoize(value);\n\n  // Return accumulated values\n  return normalized;\n}\n\n/* eslint-disable complexity */\n/**\n * Stringify the given value. Handles various known special values and types.\n *\n * Not meant to be used on simple primitives which already have a string representation, as it will, for example, turn\n * the number 1231 into \"[Object Number]\", nor on `null`, as it will throw.\n *\n * @param value The value to stringify\n * @returns A stringified representation of the given value\n */\nfunction stringifyValue(\n  key,\n  // this type is a tiny bit of a cheat, since this function does handle NaN (which is technically a number), but for\n  // our internal use, it'll do\n  value,\n) {\n  try {\n    if (key === 'domain' && value && typeof value === 'object' && (value )._events) {\n      return '[Domain]';\n    }\n\n    if (key === 'domainEmitter') {\n      return '[DomainEmitter]';\n    }\n\n    // It's safe to use `global`, `window`, and `document` here in this manner, as we are asserting using `typeof` first\n    // which won't throw if they are not present.\n\n    if (typeof global !== 'undefined' && value === global) {\n      return '[Global]';\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof window !== 'undefined' && value === window) {\n      return '[Window]';\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof document !== 'undefined' && value === document) {\n      return '[Document]';\n    }\n\n    if (isVueViewModel(value)) {\n      return '[VueViewModel]';\n    }\n\n    // React's SyntheticEvent thingy\n    if (isSyntheticEvent(value)) {\n      return '[SyntheticEvent]';\n    }\n\n    if (typeof value === 'number' && !Number.isFinite(value)) {\n      return `[${value}]`;\n    }\n\n    if (typeof value === 'function') {\n      return `[Function: ${getFunctionName(value)}]`;\n    }\n\n    if (typeof value === 'symbol') {\n      return `[${String(value)}]`;\n    }\n\n    // stringified BigInts are indistinguishable from regular numbers, so we need to label them to avoid confusion\n    if (typeof value === 'bigint') {\n      return `[BigInt: ${String(value)}]`;\n    }\n\n    // Now that we've knocked out all the special cases and the primitives, all we have left are objects. Simply casting\n    // them to strings means that instances of classes which haven't defined their `toStringTag` will just come out as\n    // `\"[object Object]\"`. If we instead look at the constructor's name (which is the same as the name of the class),\n    // we can make sure that only plain objects come out that way.\n    const objName = getConstructorName(value);\n\n    // Handle HTML Elements\n    if (/^HTML(\\w*)Element$/.test(objName)) {\n      return `[HTMLElement: ${objName}]`;\n    }\n\n    return `[object ${objName}]`;\n  } catch (err) {\n    return `**non-serializable** (${err})`;\n  }\n}\n/* eslint-enable complexity */\n\nfunction getConstructorName(value) {\n  const prototype = Object.getPrototypeOf(value);\n\n  return prototype?.constructor ? prototype.constructor.name : 'null prototype';\n}\n\n/** Calculates bytes size of input string */\nfunction utf8Length(value) {\n  // eslint-disable-next-line no-bitwise\n  return ~-encodeURI(value).split(/%..|./).length;\n}\n\n/** Calculates bytes size of input object */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction jsonSize(value) {\n  return utf8Length(JSON.stringify(value));\n}\n\n/**\n * Normalizes URLs in exceptions and stacktraces to a base path so Sentry can fingerprint\n * across platforms and working directory.\n *\n * @param url The URL to be normalized.\n * @param basePath The application base path.\n * @returns The normalized URL.\n */\nfunction normalizeUrlToBase(url, basePath) {\n  const escapedBase = basePath\n    // Backslash to forward\n    .replace(/\\\\/g, '/')\n    // Escape RegExp special characters\n    .replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&');\n\n  let newUrl = url;\n  try {\n    newUrl = decodeURI(url);\n  } catch (_Oo) {\n    // Sometime this breaks\n  }\n  return (\n    newUrl\n      .replace(/\\\\/g, '/')\n      .replace(/webpack:\\/?/g, '') // Remove intermediate base path\n      // eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor\n      .replace(new RegExp(`(file://)?/*${escapedBase}/*`, 'ig'), 'app:///')\n  );\n}\n\n/**\n * Helper to decycle json objects\n */\nfunction memoBuilder() {\n  const inner = new WeakSet();\n  function memoize(obj) {\n    if (inner.has(obj)) {\n      return true;\n    }\n    inner.add(obj);\n    return false;\n  }\n\n  function unmemoize(obj) {\n    inner.delete(obj);\n  }\n  return [memoize, unmemoize];\n}\n\nexport { normalize, normalizeToSize, normalizeUrlToBase };\n//# sourceMappingURL=normalize.js.map\n", "import { isThenable } from './is.js';\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\n/** SyncPromise internal states */\nvar States; (function (States) {\n  /** Pending */\n  const PENDING = 0; States[States[\"PENDING\"] = PENDING] = \"PENDING\";\n  /** Resolved / OK */\n  const RESOLVED = 1; States[States[\"RESOLVED\"] = RESOLVED] = \"RESOLVED\";\n  /** Rejected / Error */\n  const REJECTED = 2; States[States[\"REJECTED\"] = REJECTED] = \"REJECTED\";\n})(States || (States = {}));\n\n// Overloads so we can call resolvedSyncPromise without arguments and generic argument\n\n/**\n * Creates a resolved sync promise.\n *\n * @param value the value to resolve the promise with\n * @returns the resolved sync promise\n */\nfunction resolvedSyncPromise(value) {\n  return new SyncPromise(resolve => {\n    resolve(value);\n  });\n}\n\n/**\n * Creates a rejected sync promise.\n *\n * @param value the value to reject the promise with\n * @returns the rejected sync promise\n */\nfunction rejectedSyncPromise(reason) {\n  return new SyncPromise((_, reject) => {\n    reject(reason);\n  });\n}\n\n/**\n * Thenable class that behaves like a Promise and follows it's interface\n * but is not async internally\n */\nclass SyncPromise {\n\n   constructor(executor) {\n    this._state = States.PENDING;\n    this._handlers = [];\n\n    this._runExecutor(executor);\n  }\n\n  /** @inheritdoc */\n   then(\n    onfulfilled,\n    onrejected,\n  ) {\n    return new SyncPromise((resolve, reject) => {\n      this._handlers.push([\n        false,\n        result => {\n          if (!onfulfilled) {\n            // TODO: ¯\\_(ツ)_/¯\n            // TODO: FIXME\n            resolve(result );\n          } else {\n            try {\n              resolve(onfulfilled(result));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n        reason => {\n          if (!onrejected) {\n            reject(reason);\n          } else {\n            try {\n              resolve(onrejected(reason));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n      ]);\n      this._executeHandlers();\n    });\n  }\n\n  /** @inheritdoc */\n   catch(\n    onrejected,\n  ) {\n    return this.then(val => val, onrejected);\n  }\n\n  /** @inheritdoc */\n   finally(onfinally) {\n    return new SyncPromise((resolve, reject) => {\n      let val;\n      let isRejected;\n\n      return this.then(\n        value => {\n          isRejected = false;\n          val = value;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n        reason => {\n          isRejected = true;\n          val = reason;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n      ).then(() => {\n        if (isRejected) {\n          reject(val);\n          return;\n        }\n\n        resolve(val );\n      });\n    });\n  }\n\n  /** Excute the resolve/reject handlers. */\n   _executeHandlers() {\n    if (this._state === States.PENDING) {\n      return;\n    }\n\n    const cachedHandlers = this._handlers.slice();\n    this._handlers = [];\n\n    cachedHandlers.forEach(handler => {\n      if (handler[0]) {\n        return;\n      }\n\n      if (this._state === States.RESOLVED) {\n        handler[1](this._value );\n      }\n\n      if (this._state === States.REJECTED) {\n        handler[2](this._value);\n      }\n\n      handler[0] = true;\n    });\n  }\n\n  /** Run the executor for the SyncPromise. */\n   _runExecutor(executor) {\n    const setResult = (state, value) => {\n      if (this._state !== States.PENDING) {\n        return;\n      }\n\n      if (isThenable(value)) {\n        void (value ).then(resolve, reject);\n        return;\n      }\n\n      this._state = state;\n      this._value = value;\n\n      this._executeHandlers();\n    };\n\n    const resolve = (value) => {\n      setResult(States.RESOLVED, value);\n    };\n\n    const reject = (reason) => {\n      setResult(States.REJECTED, reason);\n    };\n\n    try {\n      executor(resolve, reject);\n    } catch (e) {\n      reject(e);\n    }\n  }\n}\n\nexport { SyncPromise, rejectedSyncPromise, resolvedSyncPromise };\n//# sourceMappingURL=syncpromise.js.map\n", "import { DEBUG_BUILD } from './debug-build.js';\nimport { isThenable } from './utils-hoist/is.js';\nimport { logger } from './utils-hoist/logger.js';\nimport { SyncPromise } from './utils-hoist/syncpromise.js';\n\n/**\n * Process an array of event processors, returning the processed event (or `null` if the event was dropped).\n */\nfunction notifyEventProcessors(\n  processors,\n  event,\n  hint,\n  index = 0,\n) {\n  return new SyncPromise((resolve, reject) => {\n    const processor = processors[index];\n    if (event === null || typeof processor !== 'function') {\n      resolve(event);\n    } else {\n      const result = processor({ ...event }, hint) ;\n\n      DEBUG_BUILD && processor.id && result === null && logger.log(`Event processor \"${processor.id}\" dropped event`);\n\n      if (isThenable(result)) {\n        void result\n          .then(final => notifyEventProcessors(processors, final, hint, index + 1).then(resolve))\n          .then(null, reject);\n      } else {\n        void notifyEventProcessors(processors, result, hint, index + 1)\n          .then(resolve)\n          .then(null, reject);\n      }\n    }\n  });\n}\n\nexport { notifyEventProcessors };\n//# sourceMappingURL=eventProcessors.js.map\n", "import { GLOBAL_OBJ } from './worldwide.js';\n\nlet parsedStackResults;\nlet lastKeysCount;\nlet cachedFilenameDebugIds;\n\n/**\n * Returns a map of filenames to debug identifiers.\n */\nfunction getFilenameToDebugIdMap(stackParser) {\n  const debugIdMap = GLOBAL_OBJ._sentryDebugIds;\n  if (!debugIdMap) {\n    return {};\n  }\n\n  const debugIdKeys = Object.keys(debugIdMap);\n\n  // If the count of registered globals hasn't changed since the last call, we\n  // can just return the cached result.\n  if (cachedFilenameDebugIds && debugIdKeys.length === lastKeysCount) {\n    return cachedFilenameDebugIds;\n  }\n\n  lastKeysCount = debugIdKeys.length;\n\n  // Build a map of filename -> debug_id.\n  cachedFilenameDebugIds = debugIdKeys.reduce((acc, stackKey) => {\n    if (!parsedStackResults) {\n      parsedStackResults = {};\n    }\n\n    const result = parsedStackResults[stackKey];\n\n    if (result) {\n      acc[result[0]] = result[1];\n    } else {\n      const parsedStack = stackParser(stackKey);\n\n      for (let i = parsedStack.length - 1; i >= 0; i--) {\n        const stackFrame = parsedStack[i];\n        const filename = stackFrame?.filename;\n        const debugId = debugIdMap[stackKey];\n\n        if (filename && debugId) {\n          acc[filename] = debugId;\n          parsedStackResults[stackKey] = [filename, debugId];\n          break;\n        }\n      }\n    }\n\n    return acc;\n  }, {});\n\n  return cachedFilenameDebugIds;\n}\n\n/**\n * Returns a list of debug images for the given resources.\n */\nfunction getDebugImagesForResources(\n  stackParser,\n  resource_paths,\n) {\n  const filenameDebugIdMap = getFilenameToDebugIdMap(stackParser);\n\n  if (!filenameDebugIdMap) {\n    return [];\n  }\n\n  const images = [];\n  for (const path of resource_paths) {\n    if (path && filenameDebugIdMap[path]) {\n      images.push({\n        type: 'sourcemap',\n        code_file: path,\n        debug_id: filenameDebugIdMap[path] ,\n      });\n    }\n  }\n\n  return images;\n}\n\nexport { getDebugImagesForResources, getFilenameToDebugIdMap };\n//# sourceMappingURL=debug-ids.js.map\n", "import { getDynamicSamplingContextFromSpan } from '../tracing/dynamicSamplingContext.js';\nimport { merge } from './merge.js';\nimport { spanToTraceContext, getRootSpan, spanToJSON } from './spanUtils.js';\n\n/**\n * Applies data from the scope to the event and runs all event processors on it.\n */\nfunction applyScopeDataToEvent(event, data) {\n  const { fingerprint, span, breadcrumbs, sdkProcessingMetadata } = data;\n\n  // Apply general data\n  applyDataToEvent(event, data);\n\n  // We want to set the trace context for normal events only if there isn't already\n  // a trace context on the event. There is a product feature in place where we link\n  // errors with transaction and it relies on that.\n  if (span) {\n    applySpanToEvent(event, span);\n  }\n\n  applyFingerprintToEvent(event, fingerprint);\n  applyBreadcrumbsToEvent(event, breadcrumbs);\n  applySdkMetadataToEvent(event, sdkProcessingMetadata);\n}\n\n/** Merge data of two scopes together. */\nfunction mergeScopeData(data, mergeData) {\n  const {\n    extra,\n    tags,\n    user,\n    contexts,\n    level,\n    sdkProcessingMetadata,\n    breadcrumbs,\n    fingerprint,\n    eventProcessors,\n    attachments,\n    propagationContext,\n    transactionName,\n    span,\n  } = mergeData;\n\n  mergeAndOverwriteScopeData(data, 'extra', extra);\n  mergeAndOverwriteScopeData(data, 'tags', tags);\n  mergeAndOverwriteScopeData(data, 'user', user);\n  mergeAndOverwriteScopeData(data, 'contexts', contexts);\n\n  data.sdkProcessingMetadata = merge(data.sdkProcessingMetadata, sdkProcessingMetadata, 2);\n\n  if (level) {\n    data.level = level;\n  }\n\n  if (transactionName) {\n    data.transactionName = transactionName;\n  }\n\n  if (span) {\n    data.span = span;\n  }\n\n  if (breadcrumbs.length) {\n    data.breadcrumbs = [...data.breadcrumbs, ...breadcrumbs];\n  }\n\n  if (fingerprint.length) {\n    data.fingerprint = [...data.fingerprint, ...fingerprint];\n  }\n\n  if (eventProcessors.length) {\n    data.eventProcessors = [...data.eventProcessors, ...eventProcessors];\n  }\n\n  if (attachments.length) {\n    data.attachments = [...data.attachments, ...attachments];\n  }\n\n  data.propagationContext = { ...data.propagationContext, ...propagationContext };\n}\n\n/**\n * Merges certain scope data. Undefined values will overwrite any existing values.\n * Exported only for tests.\n */\nfunction mergeAndOverwriteScopeData\n\n(data, prop, mergeVal) {\n  data[prop] = merge(data[prop], mergeVal, 1);\n}\n\nfunction applyDataToEvent(event, data) {\n  const { extra, tags, user, contexts, level, transactionName } = data;\n\n  if (Object.keys(extra).length) {\n    event.extra = { ...extra, ...event.extra };\n  }\n\n  if (Object.keys(tags).length) {\n    event.tags = { ...tags, ...event.tags };\n  }\n\n  if (Object.keys(user).length) {\n    event.user = { ...user, ...event.user };\n  }\n\n  if (Object.keys(contexts).length) {\n    event.contexts = { ...contexts, ...event.contexts };\n  }\n\n  if (level) {\n    event.level = level;\n  }\n\n  // transaction events get their `transaction` from the root span name\n  if (transactionName && event.type !== 'transaction') {\n    event.transaction = transactionName;\n  }\n}\n\nfunction applyBreadcrumbsToEvent(event, breadcrumbs) {\n  const mergedBreadcrumbs = [...(event.breadcrumbs || []), ...breadcrumbs];\n  event.breadcrumbs = mergedBreadcrumbs.length ? mergedBreadcrumbs : undefined;\n}\n\nfunction applySdkMetadataToEvent(event, sdkProcessingMetadata) {\n  event.sdkProcessingMetadata = {\n    ...event.sdkProcessingMetadata,\n    ...sdkProcessingMetadata,\n  };\n}\n\nfunction applySpanToEvent(event, span) {\n  event.contexts = {\n    trace: spanToTraceContext(span),\n    ...event.contexts,\n  };\n\n  event.sdkProcessingMetadata = {\n    dynamicSamplingContext: getDynamicSamplingContextFromSpan(span),\n    ...event.sdkProcessingMetadata,\n  };\n\n  const rootSpan = getRootSpan(span);\n  const transactionName = spanToJSON(rootSpan).description;\n  if (transactionName && !event.transaction && event.type === 'transaction') {\n    event.transaction = transactionName;\n  }\n}\n\n/**\n * Applies fingerprint from the scope to the event if there's one,\n * uses message if there's one instead or get rid of empty fingerprint\n */\nfunction applyFingerprintToEvent(event, fingerprint) {\n  // Make sure it's an array first and we actually have something in place\n  event.fingerprint = event.fingerprint\n    ? Array.isArray(event.fingerprint)\n      ? event.fingerprint\n      : [event.fingerprint]\n    : [];\n\n  // If we have something on the scope, then merge it with event\n  if (fingerprint) {\n    event.fingerprint = event.fingerprint.concat(fingerprint);\n  }\n\n  // If we have no data at all, remove empty array default\n  if (!event.fingerprint.length) {\n    delete event.fingerprint;\n  }\n}\n\nexport { applyScopeDataToEvent, mergeAndOverwriteScopeData, mergeScopeData };\n//# sourceMappingURL=applyScopeDataToEvent.js.map\n", "import { DEFAULT_ENVIRONMENT } from '../constants.js';\nimport { getGlobalScope } from '../currentScopes.js';\nimport { notifyEventProcessors } from '../eventProcessors.js';\nimport { Scope } from '../scope.js';\nimport { getFilenameToDebugIdMap } from '../utils-hoist/debug-ids.js';\nimport { uuid4, addExceptionMechanism } from '../utils-hoist/misc.js';\nimport { normalize } from '../utils-hoist/normalize.js';\nimport { truncate } from '../utils-hoist/string.js';\nimport { dateTimestampInSeconds } from '../utils-hoist/time.js';\nimport { mergeScopeData, applyScopeDataToEvent } from './applyScopeDataToEvent.js';\n\n/**\n * This type makes sure that we get either a CaptureContext, OR an EventHint.\n * It does not allow mixing them, which could lead to unexpected outcomes, e.g. this is disallowed:\n * { user: { id: '123' }, mechanism: { handled: false } }\n */\n\n/**\n * Adds common information to events.\n *\n * The information includes release and environment from `options`,\n * breadcrumbs and context (extra, tags and user) from the scope.\n *\n * Information that is already present in the event is never overwritten. For\n * nested objects, such as the context, keys are merged.\n *\n * @param event The original event.\n * @param hint May contain additional information about the original exception.\n * @param scope A scope containing event metadata.\n * @returns A new event with more information.\n * @hidden\n */\nfunction prepareEvent(\n  options,\n  event,\n  hint,\n  scope,\n  client,\n  isolationScope,\n) {\n  const { normalizeDepth = 3, normalizeMaxBreadth = 1000 } = options;\n  const prepared = {\n    ...event,\n    event_id: event.event_id || hint.event_id || uuid4(),\n    timestamp: event.timestamp || dateTimestampInSeconds(),\n  };\n  const integrations = hint.integrations || options.integrations.map(i => i.name);\n\n  applyClientOptions(prepared, options);\n  applyIntegrationsMetadata(prepared, integrations);\n\n  if (client) {\n    client.emit('applyFrameMetadata', event);\n  }\n\n  // Only put debug IDs onto frames for error events.\n  if (event.type === undefined) {\n    applyDebugIds(prepared, options.stackParser);\n  }\n\n  // If we have scope given to us, use it as the base for further modifications.\n  // This allows us to prevent unnecessary copying of data if `captureContext` is not provided.\n  const finalScope = getFinalScope(scope, hint.captureContext);\n\n  if (hint.mechanism) {\n    addExceptionMechanism(prepared, hint.mechanism);\n  }\n\n  const clientEventProcessors = client ? client.getEventProcessors() : [];\n\n  // This should be the last thing called, since we want that\n  // {@link Scope.addEventProcessor} gets the finished prepared event.\n  // Merge scope data together\n  const data = getGlobalScope().getScopeData();\n\n  if (isolationScope) {\n    const isolationData = isolationScope.getScopeData();\n    mergeScopeData(data, isolationData);\n  }\n\n  if (finalScope) {\n    const finalScopeData = finalScope.getScopeData();\n    mergeScopeData(data, finalScopeData);\n  }\n\n  const attachments = [...(hint.attachments || []), ...data.attachments];\n  if (attachments.length) {\n    hint.attachments = attachments;\n  }\n\n  applyScopeDataToEvent(prepared, data);\n\n  const eventProcessors = [\n    ...clientEventProcessors,\n    // Run scope event processors _after_ all other processors\n    ...data.eventProcessors,\n  ];\n\n  const result = notifyEventProcessors(eventProcessors, prepared, hint);\n\n  return result.then(evt => {\n    if (evt) {\n      // We apply the debug_meta field only after all event processors have ran, so that if any event processors modified\n      // file names (e.g.the RewriteFrames integration) the filename -> debug ID relationship isn't destroyed.\n      // This should not cause any PII issues, since we're only moving data that is already on the event and not adding\n      // any new data\n      applyDebugMeta(evt);\n    }\n\n    if (typeof normalizeDepth === 'number' && normalizeDepth > 0) {\n      return normalizeEvent(evt, normalizeDepth, normalizeMaxBreadth);\n    }\n    return evt;\n  });\n}\n\n/**\n * Enhances event using the client configuration.\n * It takes care of all \"static\" values like environment, release and `dist`,\n * as well as truncating overly long values.\n *\n * Only exported for tests.\n *\n * @param event event instance to be enhanced\n */\nfunction applyClientOptions(event, options) {\n  const { environment, release, dist, maxValueLength = 250 } = options;\n\n  // empty strings do not make sense for environment, release, and dist\n  // so we handle them the same as if they were not provided\n  event.environment = event.environment || environment || DEFAULT_ENVIRONMENT;\n\n  if (!event.release && release) {\n    event.release = release;\n  }\n\n  if (!event.dist && dist) {\n    event.dist = dist;\n  }\n\n  const request = event.request;\n  if (request?.url) {\n    request.url = truncate(request.url, maxValueLength);\n  }\n}\n\n/**\n * Puts debug IDs into the stack frames of an error event.\n */\nfunction applyDebugIds(event, stackParser) {\n  // Build a map of filename -> debug_id\n  const filenameDebugIdMap = getFilenameToDebugIdMap(stackParser);\n\n  event.exception?.values?.forEach(exception => {\n    exception.stacktrace?.frames?.forEach(frame => {\n      if (frame.filename) {\n        frame.debug_id = filenameDebugIdMap[frame.filename];\n      }\n    });\n  });\n}\n\n/**\n * Moves debug IDs from the stack frames of an error event into the debug_meta field.\n */\nfunction applyDebugMeta(event) {\n  // Extract debug IDs and filenames from the stack frames on the event.\n  const filenameDebugIdMap = {};\n  event.exception?.values?.forEach(exception => {\n    exception.stacktrace?.frames?.forEach(frame => {\n      if (frame.debug_id) {\n        if (frame.abs_path) {\n          filenameDebugIdMap[frame.abs_path] = frame.debug_id;\n        } else if (frame.filename) {\n          filenameDebugIdMap[frame.filename] = frame.debug_id;\n        }\n        delete frame.debug_id;\n      }\n    });\n  });\n\n  if (Object.keys(filenameDebugIdMap).length === 0) {\n    return;\n  }\n\n  // Fill debug_meta information\n  event.debug_meta = event.debug_meta || {};\n  event.debug_meta.images = event.debug_meta.images || [];\n  const images = event.debug_meta.images;\n  Object.entries(filenameDebugIdMap).forEach(([filename, debug_id]) => {\n    images.push({\n      type: 'sourcemap',\n      code_file: filename,\n      debug_id,\n    });\n  });\n}\n\n/**\n * This function adds all used integrations to the SDK info in the event.\n * @param event The event that will be filled with all integrations.\n */\nfunction applyIntegrationsMetadata(event, integrationNames) {\n  if (integrationNames.length > 0) {\n    event.sdk = event.sdk || {};\n    event.sdk.integrations = [...(event.sdk.integrations || []), ...integrationNames];\n  }\n}\n\n/**\n * Applies `normalize` function on necessary `Event` attributes to make them safe for serialization.\n * Normalized keys:\n * - `breadcrumbs.data`\n * - `user`\n * - `contexts`\n * - `extra`\n * @param event Event\n * @returns Normalized event\n */\nfunction normalizeEvent(event, depth, maxBreadth) {\n  if (!event) {\n    return null;\n  }\n\n  const normalized = {\n    ...event,\n    ...(event.breadcrumbs && {\n      breadcrumbs: event.breadcrumbs.map(b => ({\n        ...b,\n        ...(b.data && {\n          data: normalize(b.data, depth, maxBreadth),\n        }),\n      })),\n    }),\n    ...(event.user && {\n      user: normalize(event.user, depth, maxBreadth),\n    }),\n    ...(event.contexts && {\n      contexts: normalize(event.contexts, depth, maxBreadth),\n    }),\n    ...(event.extra && {\n      extra: normalize(event.extra, depth, maxBreadth),\n    }),\n  };\n\n  // event.contexts.trace stores information about a Transaction. Similarly,\n  // event.spans[] stores information about child Spans. Given that a\n  // Transaction is conceptually a Span, normalization should apply to both\n  // Transactions and Spans consistently.\n  // For now the decision is to skip normalization of Transactions and Spans,\n  // so this block overwrites the normalized event to add back the original\n  // Transaction information prior to normalization.\n  if (event.contexts?.trace && normalized.contexts) {\n    normalized.contexts.trace = event.contexts.trace;\n\n    // event.contexts.trace.data may contain circular/dangerous data so we need to normalize it\n    if (event.contexts.trace.data) {\n      normalized.contexts.trace.data = normalize(event.contexts.trace.data, depth, maxBreadth);\n    }\n  }\n\n  // event.spans[].data may contain circular/dangerous data so we need to normalize it\n  if (event.spans) {\n    normalized.spans = event.spans.map(span => {\n      return {\n        ...span,\n        ...(span.data && {\n          data: normalize(span.data, depth, maxBreadth),\n        }),\n      };\n    });\n  }\n\n  // event.contexts.flags (FeatureFlagContext) stores context for our feature\n  // flag integrations. It has a greater nesting depth than our other typed\n  // Contexts, so we re-normalize with a fixed depth of 3 here. We do not want\n  // to skip this in case of conflicting, user-provided context.\n  if (event.contexts?.flags && normalized.contexts) {\n    normalized.contexts.flags = normalize(event.contexts.flags, 3, maxBreadth);\n  }\n\n  return normalized;\n}\n\nfunction getFinalScope(scope, captureContext) {\n  if (!captureContext) {\n    return scope;\n  }\n\n  const finalScope = scope ? scope.clone() : new Scope();\n  finalScope.update(captureContext);\n  return finalScope;\n}\n\n/**\n * Parse either an `EventHint` directly, or convert a `CaptureContext` to an `EventHint`.\n * This is used to allow to update method signatures that used to accept a `CaptureContext` but should now accept an `EventHint`.\n */\nfunction parseEventHintOrCaptureContext(\n  hint,\n) {\n  if (!hint) {\n    return undefined;\n  }\n\n  // If you pass a Scope or `() => Scope` as CaptureContext, we just return this as captureContext\n  if (hintIsScopeOrFunction(hint)) {\n    return { captureContext: hint };\n  }\n\n  if (hintIsScopeContext(hint)) {\n    return {\n      captureContext: hint,\n    };\n  }\n\n  return hint;\n}\n\nfunction hintIsScopeOrFunction(hint) {\n  return hint instanceof Scope || typeof hint === 'function';\n}\n\nconst captureContextKeys = [\n  'user',\n  'level',\n  'extra',\n  'contexts',\n  'tags',\n  'fingerprint',\n  'propagationContext',\n] ;\n\nfunction hintIsScopeContext(hint) {\n  return Object.keys(hint).some(key => captureContextKeys.includes(key ));\n}\n\nexport { applyClientOptions, applyDebugIds, applyDebugMeta, parseEventHintOrCaptureContext, prepareEvent };\n//# sourceMappingURL=prepareEvent.js.map\n", "import { getCurrentScope, getClient, withIsolationScope, getIsolationScope } from './currentScopes.js';\nimport { DEBUG_BUILD } from './debug-build.js';\nimport { makeSession, updateSession, closeSession } from './session.js';\nimport { parseEventHintOrCaptureContext } from './utils/prepareEvent.js';\nimport { isThenable } from './utils-hoist/is.js';\nimport { logger } from './utils-hoist/logger.js';\nimport { uuid4 } from './utils-hoist/misc.js';\nimport { timestampInSeconds } from './utils-hoist/time.js';\nimport { GLOBAL_OBJ } from './utils-hoist/worldwide.js';\n\n/**\n * Captures an exception event and sends it to Sentry.\n *\n * @param exception The exception to capture.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured Sentry event.\n */\nfunction captureException(exception, hint) {\n  return getCurrentScope().captureException(exception, parseEventHintOrCaptureContext(hint));\n}\n\n/**\n * Captures a message event and sends it to Sentry.\n *\n * @param message The message to send to Sentry.\n * @param captureContext Define the level of the message or pass in additional data to attach to the message.\n * @returns the id of the captured message.\n */\nfunction captureMessage(message, captureContext) {\n  // This is necessary to provide explicit scopes upgrade, without changing the original\n  // arity of the `captureMessage(message, level)` method.\n  const level = typeof captureContext === 'string' ? captureContext : undefined;\n  const context = typeof captureContext !== 'string' ? { captureContext } : undefined;\n  return getCurrentScope().captureMessage(message, level, context);\n}\n\n/**\n * Captures a manually created event and sends it to Sentry.\n *\n * @param event The event to send to Sentry.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured event.\n */\nfunction captureEvent(event, hint) {\n  return getCurrentScope().captureEvent(event, hint);\n}\n\n/**\n * Sets context data with the given name.\n * @param name of the context\n * @param context Any kind of data. This data will be normalized.\n */\nfunction setContext(name, context) {\n  getIsolationScope().setContext(name, context);\n}\n\n/**\n * Set an object that will be merged sent as extra data with the event.\n * @param extras Extras object to merge into current context.\n */\nfunction setExtras(extras) {\n  getIsolationScope().setExtras(extras);\n}\n\n/**\n * Set key:value that will be sent as extra data with the event.\n * @param key String of extra\n * @param extra Any kind of data. This data will be normalized.\n */\nfunction setExtra(key, extra) {\n  getIsolationScope().setExtra(key, extra);\n}\n\n/**\n * Set an object that will be merged sent as tags data with the event.\n * @param tags Tags context object to merge into current context.\n */\nfunction setTags(tags) {\n  getIsolationScope().setTags(tags);\n}\n\n/**\n * Set key:value that will be sent as tags data with the event.\n *\n * Can also be used to unset a tag, by passing `undefined`.\n *\n * @param key String key of tag\n * @param value Value of tag\n */\nfunction setTag(key, value) {\n  getIsolationScope().setTag(key, value);\n}\n\n/**\n * Updates user context information for future events.\n *\n * @param user User context object to be set in the current context. Pass `null` to unset the user.\n */\nfunction setUser(user) {\n  getIsolationScope().setUser(user);\n}\n\n/**\n * The last error event id of the isolation scope.\n *\n * Warning: This function really returns the last recorded error event id on the current\n * isolation scope. If you call this function after handling a certain error and another error\n * is captured in between, the last one is returned instead of the one you might expect.\n * Also, ids of events that were never sent to Sentry (for example because\n * they were dropped in `beforeSend`) could be returned.\n *\n * @returns The last event id of the isolation scope.\n */\nfunction lastEventId() {\n  return getIsolationScope().lastEventId();\n}\n\n/**\n * Create a cron monitor check in and send it to Sentry.\n *\n * @param checkIn An object that describes a check in.\n * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n * to create a monitor automatically when sending a check in.\n */\nfunction captureCheckIn(checkIn, upsertMonitorConfig) {\n  const scope = getCurrentScope();\n  const client = getClient();\n  if (!client) {\n    DEBUG_BUILD && logger.warn('Cannot capture check-in. No client defined.');\n  } else if (!client.captureCheckIn) {\n    DEBUG_BUILD && logger.warn('Cannot capture check-in. Client does not support sending check-ins.');\n  } else {\n    return client.captureCheckIn(checkIn, upsertMonitorConfig, scope);\n  }\n\n  return uuid4();\n}\n\n/**\n * Wraps a callback with a cron monitor check in. The check in will be sent to Sentry when the callback finishes.\n *\n * @param monitorSlug The distinct slug of the monitor.\n * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n * to create a monitor automatically when sending a check in.\n */\nfunction withMonitor(\n  monitorSlug,\n  callback,\n  upsertMonitorConfig,\n) {\n  const checkInId = captureCheckIn({ monitorSlug, status: 'in_progress' }, upsertMonitorConfig);\n  const now = timestampInSeconds();\n\n  function finishCheckIn(status) {\n    captureCheckIn({ monitorSlug, status, checkInId, duration: timestampInSeconds() - now });\n  }\n\n  return withIsolationScope(() => {\n    let maybePromiseResult;\n    try {\n      maybePromiseResult = callback();\n    } catch (e) {\n      finishCheckIn('error');\n      throw e;\n    }\n\n    if (isThenable(maybePromiseResult)) {\n      Promise.resolve(maybePromiseResult).then(\n        () => {\n          finishCheckIn('ok');\n        },\n        e => {\n          finishCheckIn('error');\n          throw e;\n        },\n      );\n    } else {\n      finishCheckIn('ok');\n    }\n\n    return maybePromiseResult;\n  });\n}\n\n/**\n * Call `flush()` on the current client, if there is one. See {@link Client.flush}.\n *\n * @param timeout Maximum time in ms the client should wait to flush its event queue. Omitting this parameter will cause\n * the client to wait until all events are sent before resolving the promise.\n * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it\n * doesn't (or if there's no client defined).\n */\nasync function flush(timeout) {\n  const client = getClient();\n  if (client) {\n    return client.flush(timeout);\n  }\n  DEBUG_BUILD && logger.warn('Cannot flush events. No client defined.');\n  return Promise.resolve(false);\n}\n\n/**\n * Call `close()` on the current client, if there is one. See {@link Client.close}.\n *\n * @param timeout Maximum time in ms the client should wait to flush its event queue before shutting down. Omitting this\n * parameter will cause the client to wait until all events are sent before disabling itself.\n * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it\n * doesn't (or if there's no client defined).\n */\nasync function close(timeout) {\n  const client = getClient();\n  if (client) {\n    return client.close(timeout);\n  }\n  DEBUG_BUILD && logger.warn('Cannot flush events and disable SDK. No client defined.');\n  return Promise.resolve(false);\n}\n\n/**\n * Returns true if Sentry has been properly initialized.\n */\nfunction isInitialized() {\n  return !!getClient();\n}\n\n/** If the SDK is initialized & enabled. */\nfunction isEnabled() {\n  const client = getClient();\n  return client?.getOptions().enabled !== false && !!client?.getTransport();\n}\n\n/**\n * Add an event processor.\n * This will be added to the current isolation scope, ensuring any event that is processed in the current execution\n * context will have the processor applied.\n */\nfunction addEventProcessor(callback) {\n  getIsolationScope().addEventProcessor(callback);\n}\n\n/**\n * Start a session on the current isolation scope.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns the new active session\n */\nfunction startSession(context) {\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n\n  // Will fetch userAgent if called from browser sdk\n  const { userAgent } = GLOBAL_OBJ.navigator || {};\n\n  const session = makeSession({\n    user: currentScope.getUser() || isolationScope.getUser(),\n    ...(userAgent && { userAgent }),\n    ...context,\n  });\n\n  // End existing session if there's one\n  const currentSession = isolationScope.getSession();\n  if (currentSession?.status === 'ok') {\n    updateSession(currentSession, { status: 'exited' });\n  }\n\n  endSession();\n\n  // Afterwards we set the new session on the scope\n  isolationScope.setSession(session);\n\n  return session;\n}\n\n/**\n * End the session on the current isolation scope.\n */\nfunction endSession() {\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n\n  const session = currentScope.getSession() || isolationScope.getSession();\n  if (session) {\n    closeSession(session);\n  }\n  _sendSessionUpdate();\n\n  // the session is over; take it off of the scope\n  isolationScope.setSession();\n}\n\n/**\n * Sends the current Session on the scope\n */\nfunction _sendSessionUpdate() {\n  const isolationScope = getIsolationScope();\n  const client = getClient();\n  const session = isolationScope.getSession();\n  if (session && client) {\n    client.captureSession(session);\n  }\n}\n\n/**\n * Sends the current session on the scope to Sentry\n *\n * @param end If set the session will be marked as exited and removed from the scope.\n *            Defaults to `false`.\n */\nfunction captureSession(end = false) {\n  // both send the update and pull the session from the scope\n  if (end) {\n    endSession();\n    return;\n  }\n\n  // only send the update\n  _sendSessionUpdate();\n}\n\nexport { addEventProcessor, captureCheckIn, captureEvent, captureException, captureMessage, captureSession, close, endSession, flush, isEnabled, isInitialized, lastEventId, setContext, setExtra, setExtras, setTag, setTags, setUser, startSession, withMonitor };\n//# sourceMappingURL=exports.js.map\n"], "mappings": ";AAKK,IAAC,cAAe,OAAO,qBAAqB,eAAe;ACH3D,IAAC,cAAc;ACCf,IAAC,aAAa;ACYnB,SAAS,iBAAiB;AAExB,mBAAiB,UAAU;AAC3B,SAAO;AACT;AAGA,SAAS,iBAAiB,SAAS;AACjC,QAAM,aAAc,QAAQ,aAAa,QAAQ,cAAc,CAAA;AAG/D,aAAW,UAAU,WAAW,WAAW;AAI3C,SAAQ,WAAW,WAAW,IAAI,WAAW,WAAW,KAAK,CAAA;AAC/D;AAaA,SAAS,mBACP,MACA,SACA,MAAM,YACN;AACA,QAAM,aAAc,IAAI,aAAa,IAAI,cAAc,CAAA;AACvD,QAAM,UAAW,WAAW,WAAW,IAAI,WAAW,WAAW,KAAK,CAAA;AAEtE,SAAO,QAAQ,IAAI,MAAM,QAAQ,IAAI,IAAI,QAAO;AAClD;ACpDA,IAAM,iBAAiB,OAAO,UAAU;AASxC,SAAS,QAAQ,KAAK;AACpB,UAAQ,eAAe,KAAK,GAAG,GAAC;IAC9B,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAO;IACT;AACE,aAAO,aAAa,KAAK,KAAK;EACpC;AACA;AAQA,SAAS,UAAU,KAAK,WAAW;AACjC,SAAO,eAAe,KAAK,GAAG,MAAM,WAAW,SAAS;AAC1D;AASA,SAAS,aAAa,KAAK;AACzB,SAAO,UAAU,KAAK,YAAY;AACpC;AASA,SAAS,WAAW,KAAK;AACvB,SAAO,UAAU,KAAK,UAAU;AAClC;AASA,SAAS,eAAe,KAAK;AAC3B,SAAO,UAAU,KAAK,cAAc;AACtC;AASA,SAAS,SAAS,KAAK;AACrB,SAAO,UAAU,KAAK,QAAQ;AAChC;AASA,SAAS,sBAAsB,KAAK;AAClC,SACE,OAAO,QAAQ,YACf,QAAQ,QACR,gCAAgC,OAChC,gCAAgC;AAEpC;AASA,SAAS,YAAY,KAAK;AACxB,SAAO,QAAQ,QAAQ,sBAAsB,GAAG,KAAM,OAAO,QAAQ,YAAY,OAAO,QAAQ;AAClG;AASA,SAAS,cAAc,KAAK;AAC1B,SAAO,UAAU,KAAK,QAAQ;AAChC;AASA,SAAS,QAAQ,KAAK;AACpB,SAAO,OAAO,UAAU,eAAe,aAAa,KAAK,KAAK;AAChE;AASA,SAAS,UAAU,KAAK;AACtB,SAAO,OAAO,YAAY,eAAe,aAAa,KAAK,OAAO;AACpE;AASA,SAAS,SAAS,KAAK;AACrB,SAAO,UAAU,KAAK,QAAQ;AAChC;AAMA,SAAS,WAAW,KAAK;AAEvB,SAAO,SAAQ,2BAAK,SAAQ,OAAO,IAAI,SAAS,UAAU;AAC5D;AASA,SAAS,iBAAiB,KAAK;AAC7B,SAAO,cAAc,GAAG,KAAK,iBAAiB,OAAO,oBAAoB,OAAO,qBAAqB;AACvG;AAUA,SAAS,aAAa,KAAK,MAAM;AAC/B,MAAI;AACF,WAAO,eAAe;EAC1B,SAAW,IAAI;AACX,WAAO;EACX;AACA;AAQA,SAAS,eAAe,KAAK;AAE3B,SAAO,CAAC,EAAE,OAAO,QAAQ,YAAY,QAAQ,SAAU,IAAM,WAAY,IAAM;AACjF;AAOA,SAAS,UAAU,SAAS;AAC1B,SAAO,OAAO,YAAY,eAAe,aAAa,SAAS,OAAO;AACxE;ACtMA,IAAM,SAAS;AAEf,IAAM,4BAA4B;AAQlC,SAAS,iBACP,MACA,UAAU,CAAA,GACV;AACA,MAAI,CAAC,MAAM;AACT,WAAO;EACX;AAME,MAAI;AACF,QAAI,cAAc;AAClB,UAAM,sBAAsB;AAC5B,UAAM,MAAM,CAAA;AACZ,QAAI,SAAS;AACb,QAAI,MAAM;AACV,UAAM,YAAY;AAClB,UAAM,YAAY,UAAU;AAC5B,QAAI;AACJ,UAAM,WAAW,MAAM,QAAQ,OAAO,IAAI,UAAU,QAAQ;AAC5D,UAAM,kBAAmB,CAAC,MAAM,QAAQ,OAAO,KAAK,QAAQ,mBAAoB;AAEhF,WAAO,eAAe,WAAW,qBAAqB;AACpD,gBAAU,qBAAqB,aAAa,QAAQ;AAKpD,UAAI,YAAY,UAAW,SAAS,KAAK,MAAM,IAAI,SAAS,YAAY,QAAQ,UAAU,iBAAkB;AAC1G;MACR;AAEM,UAAI,KAAK,OAAO;AAEhB,aAAO,QAAQ;AACf,oBAAc,YAAY;IAChC;AAEI,WAAO,IAAI,QAAO,EAAG,KAAK,SAAS;EACvC,SAAW,KAAK;AACZ,WAAO;EACX;AACA;AAOA,SAAS,qBAAqB,IAAI,UAAU;AAC1C,QAAM,OAAO;AAIb,QAAM,MAAM,CAAA;AAEZ,MAAI,EAAC,6BAAM,UAAS;AAClB,WAAO;EACX;AAGE,MAAI,OAAO,aAAa;AAEtB,QAAI,gBAAgB,eAAe,KAAK,SAAS;AAC/C,UAAI,KAAK,QAAQ,iBAAiB,GAAG;AACnC,eAAO,KAAK,QAAQ,iBAAiB;MAC7C;AACM,UAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,eAAO,KAAK,QAAQ,eAAe;MAC3C;IACA;EACA;AAEE,MAAI,KAAK,KAAK,QAAQ,YAAW,CAAE;AAGnC,QAAM,gBAAe,qCAAU,UAC3B,SAAS,OAAO,aAAW,KAAK,aAAa,OAAO,CAAC,EAAE,IAAI,aAAW,CAAC,SAAS,KAAK,aAAa,OAAO,CAAC,CAAC,IAC3G;AAEJ,MAAI,6CAAc,QAAQ;AACxB,iBAAa,QAAQ,iBAAe;AAClC,UAAI,KAAK,IAAI,YAAY,CAAC,CAAC,KAAK,YAAY,CAAC,CAAC,IAAI;IACxD,CAAK;EACL,OAAS;AACL,QAAI,KAAK,IAAI;AACX,UAAI,KAAK,IAAI,KAAK,EAAE,EAAE;IAC5B;AAEI,UAAM,YAAY,KAAK;AACvB,QAAI,aAAa,SAAS,SAAS,GAAG;AACpC,YAAM,UAAU,UAAU,MAAM,KAAK;AACrC,iBAAW,KAAK,SAAS;AACvB,YAAI,KAAK,IAAI,CAAC,EAAE;MACxB;IACA;EACA;AACE,QAAM,eAAe,CAAC,cAAc,QAAQ,QAAQ,SAAS,KAAK;AAClE,aAAW,KAAK,cAAc;AAC5B,UAAM,OAAO,KAAK,aAAa,CAAC;AAChC,QAAI,MAAM;AACR,UAAI,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI;IACjC;EACA;AAEE,SAAO,IAAI,KAAK,EAAE;AACpB;AAKA,SAAS,kBAAkB;AACzB,MAAI;AACF,WAAO,OAAO,SAAS,SAAS;EACpC,SAAW,IAAI;AACX,WAAO;EACX;AACA;AASA,SAAS,iBAAiB,MAAM;AAE9B,MAAI,CAAC,OAAO,aAAa;AACvB,WAAO;EACX;AAEE,MAAI,cAAc;AAClB,QAAM,sBAAsB;AAC5B,WAAS,IAAI,GAAG,IAAI,qBAAqB,KAAK;AAC5C,QAAI,CAAC,aAAa;AAChB,aAAO;IACb;AAEI,QAAI,uBAAuB,aAAa;AACtC,UAAI,YAAY,QAAQ,iBAAiB,GAAG;AAC1C,eAAO,YAAY,QAAQ,iBAAiB;MACpD;AACM,UAAI,YAAY,QAAQ,eAAe,GAAG;AACxC,eAAO,YAAY,QAAQ,eAAe;MAClD;IACA;AAEI,kBAAc,YAAY;EAC9B;AAEE,SAAO;AACT;AClKA,IAAM,SAAS;AAEV,IAAC,iBAAiB;EACrB;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAGK,IAAC,yBAEH,CAAA;AAUH,SAAS,eAAe,UAAU;AAChC,MAAI,EAAE,aAAa,aAAa;AAC9B,WAAO,SAAQ;EACnB;AAEE,QAAMA,WAAU,WAAW;AAC3B,QAAM,eAAe,CAAA;AAErB,QAAM,gBAAgB,OAAO,KAAK,sBAAsB;AAGxD,gBAAc,QAAQ,WAAS;AAC7B,UAAM,wBAAwB,uBAAuB,KAAK;AAC1D,iBAAa,KAAK,IAAIA,SAAQ,KAAK;AACnC,IAAAA,SAAQ,KAAK,IAAI;EACrB,CAAG;AAED,MAAI;AACF,WAAO,SAAQ;EACnB,UAAG;AAEC,kBAAc,QAAQ,WAAS;AAC7B,MAAAA,SAAQ,KAAK,IAAI,aAAa,KAAK;IACzC,CAAK;EACL;AACA;AAEA,SAAS,aAAa;AACpB,MAAI,UAAU;AACd,QAAMC,UAAS;IACb,QAAQ,MAAM;AACZ,gBAAU;IAChB;IACI,SAAS,MAAM;AACb,gBAAU;IAChB;IACI,WAAW,MAAM;EACrB;AAEE,MAAI,aAAa;AACf,mBAAe,QAAQ,UAAQ;AAC7B,MAAAA,QAAO,IAAI,IAAI,IAAI,SAAS;AAC1B,YAAI,SAAS;AACX,yBAAe,MAAM;AACnB,uBAAW,QAAQ,IAAI,EAAE,GAAG,MAAM,IAAI,IAAI,MAAM,GAAG,IAAI;UACnE,CAAW;QACX;MACA;IACA,CAAK;EACL,OAAS;AACL,mBAAe,QAAQ,UAAQ;AAC7B,MAAAA,QAAO,IAAI,IAAI,MAAM;IAC3B,CAAK;EACL;AAEE,SAAOA;AACT;AAMK,IAAC,SAAS,mBAAmB,UAAU,UAAU;ACnFtD,SAAS,SAAS,KAAK,MAAM,GAAG;AAC9B,MAAI,OAAO,QAAQ,YAAY,QAAQ,GAAG;AACxC,WAAO;EACX;AACE,SAAO,IAAI,UAAU,MAAM,MAAM,GAAG,IAAI,MAAM,GAAG,GAAG,CAAC;AACvD;AAmDA,SAAS,SAAS,OAAO,WAAW;AAClC,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,WAAO;EACX;AAEE,QAAM,SAAS,CAAA;AAEf,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,QAAQ,MAAM,CAAC;AACrB,QAAI;AAMF,UAAI,eAAe,KAAK,GAAG;AACzB,eAAO,KAAK,gBAAgB;MACpC,OAAa;AACL,eAAO,KAAK,OAAO,KAAK,CAAC;MACjC;IACA,SAAa,GAAG;AACV,aAAO,KAAK,8BAA8B;IAChD;EACA;AAEE,SAAO,OAAO,KAAK,SAAS;AAC9B;AAUA,SAAS,kBACP,OACA,SACA,0BAA0B,OAC1B;AACA,MAAI,CAAC,SAAS,KAAK,GAAG;AACpB,WAAO;EACX;AAEE,MAAI,SAAS,OAAO,GAAG;AACrB,WAAO,QAAQ,KAAK,KAAK;EAC7B;AACE,MAAI,SAAS,OAAO,GAAG;AACrB,WAAO,0BAA0B,UAAU,UAAU,MAAM,SAAS,OAAO;EAC/E;AAEE,SAAO;AACT;AAYA,SAAS,yBACP,YACA,WAAW,CAAA,GACX,0BAA0B,OAC1B;AACA,SAAO,SAAS,KAAK,aAAW,kBAAkB,YAAY,SAAS,uBAAuB,CAAC;AACjG;ACrHA,SAAS,KAAK,QAAQ,MAAM,oBAAoB;AAC9C,MAAI,EAAE,QAAQ,SAAS;AACrB;EACJ;AAGE,QAAM,WAAW,OAAO,IAAI;AAE5B,MAAI,OAAO,aAAa,YAAY;AAClC;EACJ;AAEE,QAAM,UAAU,mBAAmB,QAAQ;AAI3C,MAAI,OAAO,YAAY,YAAY;AACjC,wBAAoB,SAAS,QAAQ;EACzC;AAEE,MAAI;AACF,WAAO,IAAI,IAAI;EACnB,QAAU;AACN,mBAAe,OAAO,IAAI,6BAA6B,IAAI,eAAe,MAAM;EACpF;AACA;AASA,SAAS,yBAAyB,KAAK,MAAM,OAAO;AAClD,MAAI;AACF,WAAO,eAAe,KAAK,MAAM;;MAE/B;MACA,UAAU;MACV,cAAc;IACpB,CAAK;EACL,SAAW,KAAK;AACZ,mBAAe,OAAO,IAAI,0CAA0C,IAAI,eAAe,GAAG;EAC9F;AACA;AASA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI;AACF,UAAM,QAAQ,SAAS,aAAa,CAAA;AACpC,YAAQ,YAAY,SAAS,YAAY;AACzC,6BAAyB,SAAS,uBAAuB,QAAQ;EACrE,SAAW,KAAK;EAAA;AAChB;AAUA,SAAS,oBAAoB,MAAM;AACjC,SAAO,KAAK;AACd;AAUA,SAAS,qBAAqB,OAE7B;AACC,MAAI,QAAQ,KAAK,GAAG;AAClB,WAAO;MACL,SAAS,MAAM;MACf,MAAM,MAAM;MACZ,OAAO,MAAM;MACb,GAAG,iBAAiB,KAAK;IAC/B;EACA,WAAa,QAAQ,KAAK,GAAG;AACzB,UAAM,SAEP;MACG,MAAM,MAAM;MACZ,QAAQ,qBAAqB,MAAM,MAAM;MACzC,eAAe,qBAAqB,MAAM,aAAa;MACvD,GAAG,iBAAiB,KAAK;IAC/B;AAEI,QAAI,OAAO,gBAAgB,eAAe,aAAa,OAAO,WAAW,GAAG;AAC1E,aAAO,SAAS,MAAM;IAC5B;AAEI,WAAO;EACX,OAAS;AACL,WAAO;EACX;AACA;AAGA,SAAS,qBAAqB,QAAQ;AACpC,MAAI;AACF,WAAO,UAAU,MAAM,IAAI,iBAAiB,MAAM,IAAI,OAAO,UAAU,SAAS,KAAK,MAAM;EAC/F,SAAW,KAAK;AACZ,WAAO;EACX;AACA;AAGA,SAAS,iBAAiB,KAAK;AAC7B,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,UAAM,iBAAiB,CAAA;AACvB,eAAW,YAAY,KAAK;AAC1B,UAAI,OAAO,UAAU,eAAe,KAAK,KAAK,QAAQ,GAAG;AACvD,uBAAe,QAAQ,IAAK,IAAM,QAAQ;MAClD;IACA;AACI,WAAO;EACX,OAAS;AACL,WAAO,CAAA;EACX;AACA;AAOA,SAAS,+BAA+B,WAAW,YAAY,IAAI;AACjE,QAAM,OAAO,OAAO,KAAK,qBAAqB,SAAS,CAAC;AACxD,OAAK,KAAI;AAET,QAAM,WAAW,KAAK,CAAC;AAEvB,MAAI,CAAC,UAAU;AACb,WAAO;EACX;AAEE,MAAI,SAAS,UAAU,WAAW;AAChC,WAAO,SAAS,UAAU,SAAS;EACvC;AAEE,WAAS,eAAe,KAAK,QAAQ,eAAe,GAAG,gBAAgB;AACrE,UAAM,aAAa,KAAK,MAAM,GAAG,YAAY,EAAE,KAAK,IAAI;AACxD,QAAI,WAAW,SAAS,WAAW;AACjC;IACN;AACI,QAAI,iBAAiB,KAAK,QAAQ;AAChC,aAAO;IACb;AACI,WAAO,SAAS,YAAY,SAAS;EACzC;AAEE,SAAO;AACT;ACtLA,SAAS,YAAY;AACnB,QAAM,MAAM;AACZ,SAAO,IAAI,UAAU,IAAI;AAC3B;AAOA,SAAS,MAAM,SAAS,UAAS,GAAI;AACnC,MAAI,gBAAgB,MAAM,KAAK,OAAM,IAAK;AAC1C,MAAI;AACF,QAAI,iCAAQ,YAAY;AACtB,aAAO,OAAO,WAAU,EAAG,QAAQ,MAAM,EAAE;IACjD;AACI,QAAI,iCAAQ,iBAAiB;AAC3B,sBAAgB,MAAM;AAKpB,cAAM,aAAa,IAAI,WAAW,CAAC;AACnC,eAAO,gBAAgB,UAAU;AAEjC,eAAO,WAAW,CAAC;MAC3B;IACA;EACA,SAAW,GAAG;EAGd;AAIE,UAAS,CAAC,GAAG,IAAM,MAAM,MAAM,MAAM,MAAM;IAAQ;IAAU;;OAEzD,KAAQ,cAAa,IAAK,OAAS,IAAM,GAAK,SAAS,EAAE;;EAC/D;AACA;AAEA,SAAS,kBAAkB,OAAO;AT7ClC;AS8CE,UAAO,iBAAM,cAAN,mBAAiB,WAAjB,mBAA0B;AACnC;AAMA,SAAS,oBAAoB,OAAO;AAClC,QAAM,EAAE,SAAS,UAAU,QAAO,IAAK;AACvC,MAAI,SAAS;AACX,WAAO;EACX;AAEE,QAAM,iBAAiB,kBAAkB,KAAK;AAC9C,MAAI,gBAAgB;AAClB,QAAI,eAAe,QAAQ,eAAe,OAAO;AAC/C,aAAO,GAAG,eAAe,IAAI,KAAK,eAAe,KAAK;IAC5D;AACI,WAAO,eAAe,QAAQ,eAAe,SAAS,WAAW;EACrE;AACE,SAAO,WAAW;AACpB;AASA,SAAS,sBAAsB,OAAO,OAAO,MAAM;AACjD,QAAM,YAAa,MAAM,YAAY,MAAM,aAAa,CAAA;AACxD,QAAM,SAAU,UAAU,SAAS,UAAU,UAAU,CAAA;AACvD,QAAM,iBAAkB,OAAO,CAAC,IAAI,OAAO,CAAC,KAAK,CAAA;AACjD,MAAI,CAAC,eAAe,OAAO;AACzB,mBAAe,QAAQ,SAAS;EACpC;AACE,MAAI,CAAC,eAAe,MAAM;AACxB,mBAAe,OAAe;EAClC;AACA;AASA,SAAS,sBAAsB,OAAO,cAAc;AAClD,QAAM,iBAAiB,kBAAkB,KAAK;AAC9C,MAAI,CAAC,gBAAgB;AACnB;EACJ;AAEE,QAAM,mBAAmB,EAAE,MAAM,WAAW,SAAS,KAAI;AACzD,QAAM,mBAAmB,eAAe;AACxC,iBAAe,YAAY,EAAE,GAAG,kBAAkB,GAAG,kBAAkB,GAAG,aAAY;AAEtF,MAAI,gBAAgB,UAAU,cAAc;AAC1C,UAAM,aAAa,EAAE,GAAG,qDAAkB,MAAM,GAAG,aAAa,KAAI;AACpE,mBAAe,UAAU,OAAO;EACpC;AACA;AAoFA,SAAS,wBAAwB,WAAW;AAC1C,MAAI,kBAAkB,SAAS,GAAG;AAChC,WAAO;EACX;AAEE,MAAI;AAGF,6BAAyB,WAAY,uBAAuB,IAAI;EACpE,SAAW,KAAK;EAEhB;AAEE,SAAO;AACT;AAEA,SAAS,kBAAkB,WAAW;AACpC,MAAI;AACF,WAAQ,UAAY;EACxB,QAAU;EAAA;AACV;ACnNA,IAAM,mBAAmB;AAUzB,SAAS,yBAAyB;AAChC,SAAO,KAAK,IAAG,IAAK;AACtB;AAQA,SAAS,mCAAmC;AAC1C,QAAM,EAAE,YAAW,IAAK;AACxB,MAAI,EAAC,2CAAa,MAAK;AACrB,WAAO;EACX;AAIE,QAAM,2BAA2B,KAAK,IAAG,IAAK,YAAY,IAAG;AAC7D,QAAM,aAAa,YAAY,cAAc,SAAY,2BAA2B,YAAY;AAWhG,SAAO,MAAM;AACX,YAAQ,aAAa,YAAY,IAAG,KAAM;EAC9C;AACA;AAWK,IAAC,qBAAqB,iCAAgC;AC7C3D,SAAS,YAAY,SAAS;AAE5B,QAAM,eAAe,mBAAkB;AAEvC,QAAM,UAAU;IACd,KAAK,MAAK;IACV,MAAM;IACN,WAAW;IACX,SAAS;IACT,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,gBAAgB;IAChB,QAAQ,MAAM,cAAc,OAAO;EACvC;AAEE,MAAI,SAAS;AACX,kBAAc,SAAS,OAAO;EAClC;AAEE,SAAO;AACT;AAcA,SAAS,cAAc,SAAS,UAAU,CAAA,GAAI;AAC5C,MAAI,QAAQ,MAAM;AAChB,QAAI,CAAC,QAAQ,aAAa,QAAQ,KAAK,YAAY;AACjD,cAAQ,YAAY,QAAQ,KAAK;IACvC;AAEI,QAAI,CAAC,QAAQ,OAAO,CAAC,QAAQ,KAAK;AAChC,cAAQ,MAAM,QAAQ,KAAK,MAAM,QAAQ,KAAK,SAAS,QAAQ,KAAK;IAC1E;EACA;AAEE,UAAQ,YAAY,QAAQ,aAAa,mBAAkB;AAE3D,MAAI,QAAQ,oBAAoB;AAC9B,YAAQ,qBAAqB,QAAQ;EACzC;AAEE,MAAI,QAAQ,gBAAgB;AAC1B,YAAQ,iBAAiB,QAAQ;EACrC;AACE,MAAI,QAAQ,KAAK;AAEf,YAAQ,MAAM,QAAQ,IAAI,WAAW,KAAK,QAAQ,MAAM,MAAK;EACjE;AACE,MAAI,QAAQ,SAAS,QAAW;AAC9B,YAAQ,OAAO,QAAQ;EAC3B;AACE,MAAI,CAAC,QAAQ,OAAO,QAAQ,KAAK;AAC/B,YAAQ,MAAM,GAAG,QAAQ,GAAG;EAChC;AACE,MAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,YAAQ,UAAU,QAAQ;EAC9B;AACE,MAAI,QAAQ,gBAAgB;AAC1B,YAAQ,WAAW;EACvB,WAAa,OAAO,QAAQ,aAAa,UAAU;AAC/C,YAAQ,WAAW,QAAQ;EAC/B,OAAS;AACL,UAAM,WAAW,QAAQ,YAAY,QAAQ;AAC7C,YAAQ,WAAW,YAAY,IAAI,WAAW;EAClD;AACE,MAAI,QAAQ,SAAS;AACnB,YAAQ,UAAU,QAAQ;EAC9B;AACE,MAAI,QAAQ,aAAa;AACvB,YAAQ,cAAc,QAAQ;EAClC;AACE,MAAI,CAAC,QAAQ,aAAa,QAAQ,WAAW;AAC3C,YAAQ,YAAY,QAAQ;EAChC;AACE,MAAI,CAAC,QAAQ,aAAa,QAAQ,WAAW;AAC3C,YAAQ,YAAY,QAAQ;EAChC;AACE,MAAI,OAAO,QAAQ,WAAW,UAAU;AACtC,YAAQ,SAAS,QAAQ;EAC7B;AACE,MAAI,QAAQ,QAAQ;AAClB,YAAQ,SAAS,QAAQ;EAC7B;AACA;AAaA,SAAS,aAAa,SAAS,QAAQ;AACrC,MAAI,UAAU,CAAA;AAGP,MAAI,QAAQ,WAAW,MAAM;AAClC,cAAU,EAAE,QAAQ,SAAQ;EAChC;AAEE,gBAAc,SAAS,OAAO;AAChC;AAWA,SAAS,cAAc,SAAS;AAC9B,SAAO;IACL,KAAK,GAAG,QAAQ,GAAG;IACnB,MAAM,QAAQ;;IAEd,SAAS,IAAI,KAAK,QAAQ,UAAU,GAAI,EAAE,YAAW;IACrD,WAAW,IAAI,KAAK,QAAQ,YAAY,GAAI,EAAE,YAAW;IACzD,QAAQ,QAAQ;IAChB,QAAQ,QAAQ;IAChB,KAAK,OAAO,QAAQ,QAAQ,YAAY,OAAO,QAAQ,QAAQ,WAAW,GAAG,QAAQ,GAAG,KAAK;IAC7F,UAAU,QAAQ;IAClB,oBAAoB,QAAQ;IAC5B,OAAO;MACL,SAAS,QAAQ;MACjB,aAAa,QAAQ;MACrB,YAAY,QAAQ;MACpB,YAAY,QAAQ;IAC1B;EACA;AACA;ACtJA,SAAS,MAAM,YAAY,UAAU,SAAS,GAAG;AAG/C,MAAI,CAAC,YAAY,OAAO,aAAa,YAAY,UAAU,GAAG;AAC5D,WAAO;EACX;AAGE,MAAI,cAAc,OAAO,KAAK,QAAQ,EAAE,WAAW,GAAG;AACpD,WAAO;EACX;AAGE,QAAM,SAAS,EAAE,GAAG,WAAU;AAG9B,aAAW,OAAO,UAAU;AAC1B,QAAI,OAAO,UAAU,eAAe,KAAK,UAAU,GAAG,GAAG;AACvD,aAAO,GAAG,IAAI,MAAM,OAAO,GAAG,GAAG,SAAS,GAAG,GAAG,SAAS,CAAC;IAChE;EACA;AAEE,SAAO;AACT;AC5BA,IAAM,mBAAmB;AAMzB,SAAS,iBAAiB,OAAO,MAAM;AACrC,MAAI,MAAM;AACR,6BAAyB,OAAQ,kBAAkB,IAAI;EAC3D,OAAS;AAEL,WAAQ,MAAQ,gBAAgB;EACpC;AACA;AAMA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,MAAM,gBAAgB;AAC/B;AClBA,SAAS,kBAAkB;AACzB,SAAO,MAAK;AACd;AAKA,SAAS,iBAAiB;AACxB,SAAO,MAAK,EAAG,UAAU,EAAE;AAC7B;ACDA,IAAM,0BAA0B;AAWhC,IAAM,QAAN,MAAM,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6CT,cAAc;AACb,SAAK,sBAAsB;AAC3B,SAAK,kBAAkB,CAAA;AACvB,SAAK,mBAAmB,CAAA;AACxB,SAAK,eAAe,CAAA;AACpB,SAAK,eAAe,CAAA;AACpB,SAAK,QAAQ,CAAA;AACb,SAAK,QAAQ,CAAA;AACb,SAAK,SAAS,CAAA;AACd,SAAK,YAAY,CAAA;AACjB,SAAK,yBAAyB,CAAA;AAC9B,SAAK,sBAAsB;MACzB,SAAS,gBAAe;MACxB,YAAY,KAAK,OAAM;IAC7B;EACA;;;;EAKG,QAAQ;AACP,UAAM,WAAW,IAAI,OAAK;AAC1B,aAAS,eAAe,CAAC,GAAG,KAAK,YAAY;AAC7C,aAAS,QAAQ,EAAE,GAAG,KAAK,MAAK;AAChC,aAAS,SAAS,EAAE,GAAG,KAAK,OAAM;AAClC,aAAS,YAAY,EAAE,GAAG,KAAK,UAAS;AACxC,QAAI,KAAK,UAAU,OAAO;AAGxB,eAAS,UAAU,QAAQ;QACzB,QAAQ,CAAC,GAAG,KAAK,UAAU,MAAM,MAAM;MAC/C;IACA;AAEI,aAAS,QAAQ,KAAK;AACtB,aAAS,SAAS,KAAK;AACvB,aAAS,WAAW,KAAK;AACzB,aAAS,mBAAmB,KAAK;AACjC,aAAS,eAAe,KAAK;AAC7B,aAAS,mBAAmB,CAAC,GAAG,KAAK,gBAAgB;AACrD,aAAS,eAAe,CAAC,GAAG,KAAK,YAAY;AAC7C,aAAS,yBAAyB,EAAE,GAAG,KAAK,uBAAsB;AAClE,aAAS,sBAAsB,EAAE,GAAG,KAAK,oBAAmB;AAC5D,aAAS,UAAU,KAAK;AACxB,aAAS,eAAe,KAAK;AAE7B,qBAAiB,UAAU,iBAAiB,IAAI,CAAC;AAEjD,WAAO;EACX;;;;;;EAOG,UAAU,QAAQ;AACjB,SAAK,UAAU;EACnB;;;;;EAMG,eAAe,aAAa;AAC3B,SAAK,eAAe;EACxB;;;;EAKG,YAAY;AACX,WAAO,KAAK;EAChB;;;;;EAMG,cAAc;AACb,WAAO,KAAK;EAChB;;;;EAKG,iBAAiB,UAAU;AAC1B,SAAK,gBAAgB,KAAK,QAAQ;EACtC;;;;EAKG,kBAAkB,UAAU;AAC3B,SAAK,iBAAiB,KAAK,QAAQ;AACnC,WAAO;EACX;;;;;EAMG,QAAQ,MAAM;AAGb,SAAK,QAAQ,QAAQ;MACnB,OAAO;MACP,IAAI;MACJ,YAAY;MACZ,UAAU;IAChB;AAEI,QAAI,KAAK,UAAU;AACjB,oBAAc,KAAK,UAAU,EAAE,KAAI,CAAE;IAC3C;AAEI,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;EAKG,UAAU;AACT,WAAO,KAAK;EAChB;;;;;EAMG,QAAQ,MAAM;AACb,SAAK,QAAQ;MACX,GAAG,KAAK;MACR,GAAG;IACT;AACI,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;EAKG,OAAO,KAAK,OAAO;AAClB,SAAK,QAAQ,EAAE,GAAG,KAAK,OAAO,CAAC,GAAG,GAAG,MAAK;AAC1C,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;;EAMG,UAAU,QAAQ;AACjB,SAAK,SAAS;MACZ,GAAG,KAAK;MACR,GAAG;IACT;AACI,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;EAKG,SAAS,KAAK,OAAO;AACpB,SAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,CAAC,GAAG,GAAG,MAAK;AAC5C,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;;EAMG,eAAe,aAAa;AAC3B,SAAK,eAAe;AACpB,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;EAKG,SAAS,OAAO;AACf,SAAK,SAAS;AACd,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;;;;;;;;;EAaG,mBAAmB,MAAM;AACxB,SAAK,mBAAmB;AACxB,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;;;EAOG,WAAW,KAAK,SAAS;AACxB,QAAI,YAAY,MAAM;AAEpB,aAAO,KAAK,UAAU,GAAG;IAC/B,OAAW;AACL,WAAK,UAAU,GAAG,IAAI;IAC5B;AAEI,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;EAKG,WAAW,SAAS;AACnB,QAAI,CAAC,SAAS;AACZ,aAAO,KAAK;IAClB,OAAW;AACL,WAAK,WAAW;IACtB;AACI,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;EAKG,aAAa;AACZ,WAAO,KAAK;EAChB;;;;;;;EAQG,OAAO,gBAAgB;AACtB,QAAI,CAAC,gBAAgB;AACnB,aAAO;IACb;AAEI,UAAM,eAAe,OAAO,mBAAmB,aAAa,eAAe,IAAI,IAAI;AAEnF,UAAM,gBACJ,wBAAwB,SACpB,aAAa,aAAY,IACzB,cAAc,YAAY,IACvB,iBACD;AAER,UAAM,EAAE,MAAM,OAAO,MAAM,UAAU,OAAO,cAAc,CAAA,GAAI,mBAAkB,IAAK,iBAAiB,CAAA;AAEtG,SAAK,QAAQ,EAAE,GAAG,KAAK,OAAO,GAAG,KAAI;AACrC,SAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,GAAG,MAAK;AACxC,SAAK,YAAY,EAAE,GAAG,KAAK,WAAW,GAAG,SAAQ;AAEjD,QAAI,QAAQ,OAAO,KAAK,IAAI,EAAE,QAAQ;AACpC,WAAK,QAAQ;IACnB;AAEI,QAAI,OAAO;AACT,WAAK,SAAS;IACpB;AAEI,QAAI,YAAY,QAAQ;AACtB,WAAK,eAAe;IAC1B;AAEI,QAAI,oBAAoB;AACtB,WAAK,sBAAsB;IACjC;AAEI,WAAO;EACX;;;;;EAMG,QAAQ;AAEP,SAAK,eAAe,CAAA;AACpB,SAAK,QAAQ,CAAA;AACb,SAAK,SAAS,CAAA;AACd,SAAK,QAAQ,CAAA;AACb,SAAK,YAAY,CAAA;AACjB,SAAK,SAAS;AACd,SAAK,mBAAmB;AACxB,SAAK,eAAe;AACpB,SAAK,WAAW;AAChB,qBAAiB,MAAM,MAAS;AAChC,SAAK,eAAe,CAAA;AACpB,SAAK,sBAAsB,EAAE,SAAS,gBAAe,GAAI,YAAY,KAAK,OAAM,EAAE,CAAE;AAEpF,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;;EAMG,cAAc,YAAY,gBAAgB;AfjY7C;AekYI,UAAM,YAAY,OAAO,mBAAmB,WAAW,iBAAiB;AAGxE,QAAI,aAAa,GAAG;AAClB,aAAO;IACb;AAEI,UAAM,mBAAmB;MACvB,WAAW,uBAAsB;MACjC,GAAG;;MAEH,SAAS,WAAW,UAAU,SAAS,WAAW,SAAS,IAAI,IAAI,WAAW;IACpF;AAEI,SAAK,aAAa,KAAK,gBAAgB;AACvC,QAAI,KAAK,aAAa,SAAS,WAAW;AACxC,WAAK,eAAe,KAAK,aAAa,MAAM,CAAC,SAAS;AACtD,iBAAK,YAAL,mBAAc,mBAAmB,mBAAmB;IAC1D;AAEI,SAAK,sBAAqB;AAE1B,WAAO;EACX;;;;EAKG,oBAAoB;AACnB,WAAO,KAAK,aAAa,KAAK,aAAa,SAAS,CAAC;EACzD;;;;EAKG,mBAAmB;AAClB,SAAK,eAAe,CAAA;AACpB,SAAK,sBAAqB;AAC1B,WAAO;EACX;;;;EAKG,cAAc,YAAY;AACzB,SAAK,aAAa,KAAK,UAAU;AACjC,WAAO;EACX;;;;EAKG,mBAAmB;AAClB,SAAK,eAAe,CAAA;AACpB,WAAO;EACX;;;;EAKG,eAAe;AACd,WAAO;MACL,aAAa,KAAK;MAClB,aAAa,KAAK;MAClB,UAAU,KAAK;MACf,MAAM,KAAK;MACX,OAAO,KAAK;MACZ,MAAM,KAAK;MACX,OAAO,KAAK;MACZ,aAAa,KAAK,gBAAgB,CAAA;MAClC,iBAAiB,KAAK;MACtB,oBAAoB,KAAK;MACzB,uBAAuB,KAAK;MAC5B,iBAAiB,KAAK;MACtB,MAAM,iBAAiB,IAAI;IACjC;EACA;;;;EAKG,yBAAyB,SAAS;AACjC,SAAK,yBAAyB,MAAM,KAAK,wBAAwB,SAAS,CAAC;AAC3E,WAAO;EACX;;;;EAKG,sBAAsB,SAAS;AAC9B,SAAK,sBAAsB;AAC3B,WAAO;EACX;;;;EAKG,wBAAwB;AACvB,WAAO,KAAK;EAChB;;;;;;EAOG,iBAAiB,WAAW,MAAM;AACjC,UAAM,WAAU,6BAAM,aAAY,MAAK;AAEvC,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO,KAAK,6DAA6D;AACzE,aAAO;IACb;AAEI,UAAM,qBAAqB,IAAI,MAAM,2BAA2B;AAEhE,SAAK,QAAQ;MACX;MACA;QACE,mBAAmB;QACnB;QACA,GAAG;QACH,UAAU;MAClB;MACM;IACN;AAEI,WAAO;EACX;;;;;;EAOG,eAAe,SAAS,OAAO,MAAM;AACpC,UAAM,WAAU,6BAAM,aAAY,MAAK;AAEvC,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO,KAAK,2DAA2D;AACvE,aAAO;IACb;AAEI,UAAM,qBAAqB,IAAI,MAAM,OAAO;AAE5C,SAAK,QAAQ;MACX;MACA;MACA;QACE,mBAAmB;QACnB;QACA,GAAG;QACH,UAAU;MAClB;MACM;IACN;AAEI,WAAO;EACX;;;;;;EAOG,aAAa,OAAO,MAAM;AACzB,UAAM,WAAU,6BAAM,aAAY,MAAK;AAEvC,QAAI,CAAC,KAAK,SAAS;AACjB,aAAO,KAAK,yDAAyD;AACrE,aAAO;IACb;AAEI,SAAK,QAAQ,aAAa,OAAO,EAAE,GAAG,MAAM,UAAU,QAAO,GAAI,IAAI;AAErE,WAAO;EACX;;;;EAKG,wBAAwB;AAIvB,QAAI,CAAC,KAAK,qBAAqB;AAC7B,WAAK,sBAAsB;AAC3B,WAAK,gBAAgB,QAAQ,cAAY;AACvC,iBAAS,IAAI;MACrB,CAAO;AACD,WAAK,sBAAsB;IACjC;EACA;AACA;AC/jBA,SAAS,yBAAyB;AAChC,SAAO,mBAAmB,uBAAuB,MAAM,IAAI,MAAK,CAAE;AACpE;AAGA,SAAS,2BAA2B;AAClC,SAAO,mBAAmB,yBAAyB,MAAM,IAAI,MAAK,CAAE;AACtE;ACHA,IAAM,oBAAN,MAAwB;EAErB,YAAY,OAAO,gBAAgB;AAClC,QAAI;AACJ,QAAI,CAAC,OAAO;AACV,sBAAgB,IAAI,MAAK;IAC/B,OAAW;AACL,sBAAgB;IACtB;AAEI,QAAI;AACJ,QAAI,CAAC,gBAAgB;AACnB,+BAAyB,IAAI,MAAK;IACxC,OAAW;AACL,+BAAyB;IAC/B;AAGI,SAAK,SAAS,CAAC,EAAE,OAAO,cAAa,CAAE;AACvC,SAAK,kBAAkB;EAC3B;;;;EAKG,UAAU,UAAU;AACnB,UAAM,QAAQ,KAAK,WAAU;AAE7B,QAAI;AACJ,QAAI;AACF,2BAAqB,SAAS,KAAK;IACzC,SAAa,GAAG;AACV,WAAK,UAAS;AACd,YAAM;IACZ;AAEI,QAAI,WAAW,kBAAkB,GAAG;AAElC,aAAO,mBAAmB;QACxB,SAAO;AACL,eAAK,UAAS;AACd,iBAAO;QACjB;QACQ,OAAK;AACH,eAAK,UAAS;AACd,gBAAM;QAChB;MACA;IACA;AAEI,SAAK,UAAS;AACd,WAAO;EACX;;;;EAKG,YAAY;AACX,WAAO,KAAK,YAAW,EAAG;EAC9B;;;;EAKG,WAAW;AACV,WAAO,KAAK,YAAW,EAAG;EAC9B;;;;EAKG,oBAAoB;AACnB,WAAO,KAAK;EAChB;;;;EAKG,cAAc;AACb,WAAO,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;EAC7C;;;;EAKG,aAAa;AAEZ,UAAM,QAAQ,KAAK,SAAQ,EAAG,MAAK;AACnC,SAAK,OAAO,KAAK;MACf,QAAQ,KAAK,UAAS;MACtB;IACN,CAAK;AACD,WAAO;EACX;;;;EAKG,YAAY;AACX,QAAI,KAAK,OAAO,UAAU;AAAG,aAAO;AACpC,WAAO,CAAC,CAAC,KAAK,OAAO,IAAG;EAC5B;AACA;AAMA,SAAS,uBAAuB;AAC9B,QAAM,WAAW,eAAc;AAC/B,QAAM,SAAS,iBAAiB,QAAQ;AAExC,SAAQ,OAAO,QAAQ,OAAO,SAAS,IAAI,kBAAkB,uBAAsB,GAAI,yBAAwB,CAAE;AACnH;AAEA,SAASC,YAAU,UAAU;AAC3B,SAAO,qBAAoB,EAAG,UAAU,QAAQ;AAClD;AAEA,SAAS,aAAa,OAAO,UAAU;AACrC,QAAM,QAAQ,qBAAoB;AAClC,SAAO,MAAM,UAAU,MAAM;AAC3B,UAAM,YAAW,EAAG,QAAQ;AAC5B,WAAO,SAAS,KAAK;EACzB,CAAG;AACH;AAEA,SAAS,mBAAmB,UAAU;AACpC,SAAO,qBAAoB,EAAG,UAAU,MAAM;AAC5C,WAAO,SAAS,qBAAoB,EAAG,kBAAiB,CAAE;EAC9D,CAAG;AACH;AAKA,SAAS,+BAA+B;AACtC,SAAO;IACL;IACJ,WAAIA;IACA;IACA,uBAAuB,CAAC,iBAAiB,aAAa;AACpD,aAAO,mBAAmB,QAAQ;IACxC;IACI,iBAAiB,MAAM,qBAAoB,EAAG,SAAQ;IACtD,mBAAmB,MAAM,qBAAoB,EAAG,kBAAiB;EACrE;AACA;ACxIA,SAAS,wBAAwB,SAAS;AACxC,QAAM,SAAS,iBAAiB,OAAO;AAEvC,MAAI,OAAO,KAAK;AACd,WAAO,OAAO;EAClB;AAGE,SAAO,6BAA4B;AACrC;ACpBA,SAAS,kBAAkB;AACzB,QAAM,UAAU,eAAc;AAC9B,QAAM,MAAM,wBAAwB,OAAO;AAC3C,SAAO,IAAI,gBAAe;AAC5B;AAMA,SAAS,oBAAoB;AAC3B,QAAM,UAAU,eAAc;AAC9B,QAAM,MAAM,wBAAwB,OAAO;AAC3C,SAAO,IAAI,kBAAiB;AAC9B;AAMA,SAAS,iBAAiB;AACxB,SAAO,mBAAmB,eAAe,MAAM,IAAI,MAAK,CAAE;AAC5D;AAWA,SAAS,aACJ,MACH;AACA,QAAM,UAAU,eAAc;AAC9B,QAAM,MAAM,wBAAwB,OAAO;AAG3C,MAAI,KAAK,WAAW,GAAG;AACrB,UAAM,CAAC,OAAO,QAAQ,IAAI;AAE1B,QAAI,CAAC,OAAO;AACV,aAAO,IAAI,UAAU,QAAQ;IACnC;AAEI,WAAO,IAAI,aAAa,OAAO,QAAQ;EAC3C;AAEE,SAAO,IAAI,UAAU,KAAK,CAAC,CAAC;AAC9B;AAwCA,SAAS,YAAY;AACnB,SAAO,gBAAe,EAAG,UAAS;AACpC;AAKA,SAAS,yBAAyB,OAAO;AACvC,QAAM,qBAAqB,MAAM,sBAAqB;AAEtD,QAAM,EAAE,SAAS,cAAc,kBAAiB,IAAK;AAErD,QAAM,eAAe;IACnB,UAAU;IACV,SAAS,qBAAqB,eAAc;EAChD;AAEE,MAAI,cAAc;AAChB,iBAAa,iBAAiB;EAClC;AAEE,SAAO;AACT;ACpHA,IAAM,mCAAmC;AAQzC,IAAM,wCAAwC;AAQ9C,IAAM,uDAAuD;AAK7D,IAAM,+BAA+B;AAKrC,IAAM,mCAAmC;AAuBpC,IAAC,gCAAgC;AAEjC,IAAC,oCAAoC;ACxD1C,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB;ACCvB,IAAM,4BAA4B;AAClC,IAAM,sCAAsC;AAa5C,SAAS,wBAAwB,MAAM;AACrC,SAAO;IACL,OAAQ,KAAO,yBAAyB;IACxC,gBAAiB,KAAO,mCAAmC;EAC/D;AACA;ACjBA,IAAM,4BAA4B;AAElC,IAAM,kCAAkC;AAgBxC,SAAS,sCAEP,eACA;AACA,QAAM,gBAAgB,mBAAmB,aAAa;AAEtD,MAAI,CAAC,eAAe;AAClB,WAAO;EACX;AAGE,QAAM,yBAAyB,OAAO,QAAQ,aAAa,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACzF,QAAI,IAAI,MAAM,+BAA+B,GAAG;AAC9C,YAAM,iBAAiB,IAAI,MAAM,0BAA0B,MAAM;AACjE,UAAI,cAAc,IAAI;IAC5B;AACI,WAAO;EACX,GAAK,CAAA,CAAE;AAIL,MAAI,OAAO,KAAK,sBAAsB,EAAE,SAAS,GAAG;AAClD,WAAO;EACX,OAAS;AACL,WAAO;EACX;AACA;AAoCA,SAAS,mBACP,eACA;AACA,MAAI,CAAC,iBAAkB,CAAC,SAAS,aAAa,KAAK,CAAC,MAAM,QAAQ,aAAa,GAAI;AACjF,WAAO;EACX;AAEE,MAAI,MAAM,QAAQ,aAAa,GAAG;AAEhC,WAAO,cAAc,OAAO,CAAC,KAAK,SAAS;AACzC,YAAM,oBAAoB,sBAAsB,IAAI;AACpD,aAAO,QAAQ,iBAAiB,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC1D,YAAI,GAAG,IAAI;MACnB,CAAO;AACD,aAAO;IACb,GAAO,CAAA,CAAE;EACT;AAEE,SAAO,sBAAsB,aAAa;AAC5C;AAQA,SAAS,sBAAsB,eAAe;AAC5C,SAAO,cACJ,MAAM,GAAG,EACT;IAAI,kBACH,aAAa,MAAM,GAAG,EAAE,IAAI,gBAAc;AACxC,UAAI;AACF,eAAO,mBAAmB,WAAW,KAAI,CAAE;MACrD,QAAgB;AAGN;MACV;IACA,CAAO;EACP,EACK,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AAC7B,QAAI,OAAO,OAAO;AAChB,UAAI,GAAG,IAAI;IACnB;AACM,WAAO;EACb,GAAO,CAAA,CAAE;AACT;ACpHA,IAAM,qBAAqB;AAE3B,IAAI,0BAA0B;AA0B9B,SAAS,mBAAmB,MAAM;AAChC,QAAM,EAAE,QAAQ,SAAS,UAAU,SAAQ,IAAK,KAAK,YAAW;AAIhE,QAAM,iBAAiB,WAAW,SAAS,WAAW,IAAI,EAAE;AAC5D,QAAM,QAAQ,wBAAwB,IAAI,EAAE;AAE5C,QAAM,UAAU,YAAW,+BAAO,wBAAwB,sBAAqB,eAAc,IAAK;AAElG,SAAO;IACL;IACA;IACA;EACJ;AACA;AAgBA,SAAS,4BAA4B,OAAO;AAC1C,MAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,WAAO,MAAM,IAAI,CAAC,EAAE,SAAS,EAAE,QAAQ,SAAS,YAAY,GAAG,YAAW,GAAI,WAAU,OAAQ;MAC9F,SAAS;MACT,UAAU;MACV,SAAS,eAAe;MACxB;MACA,GAAG;IACT,EAAM;EACN,OAAS;AACL,WAAO;EACX;AACA;AAKA,SAAS,uBAAuB,OAAO;AACrC,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,yBAAyB,KAAK;EACzC;AAEE,MAAI,MAAM,QAAQ,KAAK,GAAG;AAExB,WAAO,MAAM,CAAC,IAAI,MAAM,CAAC,IAAI;EACjC;AAEE,MAAI,iBAAiB,MAAM;AACzB,WAAO,yBAAyB,MAAM,QAAO,CAAE;EACnD;AAEE,SAAO,mBAAkB;AAC3B;AAKA,SAAS,yBAAyB,WAAW;AAC3C,QAAM,OAAO,YAAY;AACzB,SAAO,OAAO,YAAY,MAAO;AACnC;AAQA,SAAS,WAAW,MAAM;AxB1H1B;AwB2HE,MAAI,iBAAiB,IAAI,GAAG;AAC1B,WAAO,KAAK,YAAW;EAC3B;AAEE,QAAM,EAAE,QAAQ,SAAS,SAAS,SAAQ,IAAK,KAAK,YAAW;AAG/D,MAAI,oCAAoC,IAAI,GAAG;AAC7C,UAAM,EAAE,YAAY,WAAW,MAAM,SAAS,QAAQ,MAAK,IAAK;AAMhE,UAAM,eACJ,kBAAkB,OACd,KAAK,eACL,uBAAuB,QACpB,UAAK,sBAAL,mBAA0B,SAC3B;AAER,WAAO;MACL;MACA;MACA,MAAM;MACN,aAAa;MACb,gBAAgB;MAChB,iBAAiB,uBAAuB,SAAS;;MAEjD,WAAW,uBAAuB,OAAO,KAAK;MAC9C,QAAQ,iBAAiB,MAAM;MAC/B,IAAI,WAAW,4BAA4B;MAC3C,QAAQ,WAAW,gCAAgC;MACnD,OAAO,4BAA4B,KAAK;IAC9C;EACA;AAIE,SAAO;IACL;IACA;IACA,iBAAiB;IACjB,MAAM,CAAA;EACV;AACA;AAEA,SAAS,oCAAoC,MAAM;AACjD,QAAM,WAAW;AACjB,SAAO,CAAC,CAAC,SAAS,cAAc,CAAC,CAAC,SAAS,aAAa,CAAC,CAAC,SAAS,QAAQ,CAAC,CAAC,SAAS,WAAW,CAAC,CAAC,SAAS;AAC9G;AAQA,SAAS,iBAAiB,MAAM;AAC9B,SAAO,OAAQ,KAAO,gBAAgB;AACxC;AAQA,SAAS,cAAc,MAAM;AAG3B,QAAM,EAAE,WAAU,IAAK,KAAK,YAAW;AACvC,SAAO,eAAe;AACxB;AAGA,SAAS,iBAAiB,QAAQ;AAChC,MAAI,CAAC,UAAU,OAAO,SAAS,mBAAmB;AAChD,WAAO;EACX;AAEE,MAAI,OAAO,SAAS,gBAAgB;AAClC,WAAO;EACX;AAEE,SAAO,OAAO,WAAW;AAC3B;AAGA,IAAM,kBAAkB;AAuDxB,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,eAAe,KAAK;AAClC;AAkBA,SAAS,sBAAsB;AAC7B,MAAI,CAAC,yBAAyB;AAC5B,mBAAe,MAAM;AAEnB,cAAQ;QACN;MACR;IACA,CAAK;AACD,8BAA0B;EAC9B;AACA;ACzSA,IAAM,yBAAyB;AAC1B,IAAC,mBAAmB;AAEzB,IAAM,uBAAuB;AAC7B,IAAM,qBAAqB;AAS3B,SAAS,qBAAqB,SAAS;AACrC,QAAM,gBAAgB,QAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,OAAK,EAAE,CAAC,CAAC;AAEvE,SAAO,CAAC,OAAO,iBAAiB,GAAG,cAAc,MAAM;AACrD,UAAM,SAAS,CAAA;AACf,UAAM,QAAQ,MAAM,MAAM,IAAI;AAE9B,aAAS,IAAI,gBAAgB,IAAI,MAAM,QAAQ,KAAK;AAClD,YAAM,OAAO,MAAM,CAAC;AAKpB,UAAI,KAAK,SAAS,MAAM;AACtB;MACR;AAIM,YAAM,cAAc,qBAAqB,KAAK,IAAI,IAAI,KAAK,QAAQ,sBAAsB,IAAI,IAAI;AAIjG,UAAI,YAAY,MAAM,YAAY,GAAG;AACnC;MACR;AAEM,iBAAW,UAAU,eAAe;AAClC,cAAM,QAAQ,OAAO,WAAW;AAEhC,YAAI,OAAO;AACT,iBAAO,KAAK,KAAK;AACjB;QACV;MACA;AAEM,UAAI,OAAO,UAAU,yBAAyB,aAAa;AACzD;MACR;IACA;AAEI,WAAO,4BAA4B,OAAO,MAAM,WAAW,CAAC;EAChE;AACA;AAQA,SAAS,kCAAkC,aAAa;AACtD,MAAI,MAAM,QAAQ,WAAW,GAAG;AAC9B,WAAO,kBAAkB,GAAG,WAAW;EAC3C;AACE,SAAO;AACT;AAQA,SAAS,4BAA4B,OAAO;AAC1C,MAAI,CAAC,MAAM,QAAQ;AACjB,WAAO,CAAA;EACX;AAEE,QAAM,aAAa,MAAM,KAAK,KAAK;AAGnC,MAAI,gBAAgB,KAAK,kBAAkB,UAAU,EAAE,YAAY,EAAE,GAAG;AACtE,eAAW,IAAG;EAClB;AAGE,aAAW,QAAO;AAGlB,MAAI,mBAAmB,KAAK,kBAAkB,UAAU,EAAE,YAAY,EAAE,GAAG;AACzE,eAAW,IAAG;AAUd,QAAI,mBAAmB,KAAK,kBAAkB,UAAU,EAAE,YAAY,EAAE,GAAG;AACzE,iBAAW,IAAG;IACpB;EACA;AAEE,SAAO,WAAW,MAAM,GAAG,sBAAsB,EAAE,IAAI,YAAU;IAC/D,GAAG;IACH,UAAU,MAAM,YAAY,kBAAkB,UAAU,EAAE;IAC1D,UAAU,MAAM,YAAY;EAChC,EAAI;AACJ;AAEA,SAAS,kBAAkB,KAAK;AAC9B,SAAO,IAAI,IAAI,SAAS,CAAC,KAAK,CAAA;AAChC;AAEA,IAAM,sBAAsB;AAK5B,SAAS,gBAAgB,IAAI;AAC3B,MAAI;AACF,QAAI,CAAC,MAAM,OAAO,OAAO,YAAY;AACnC,aAAO;IACb;AACI,WAAO,GAAG,QAAQ;EACtB,SAAW,GAAG;AAGV,WAAO;EACX;AACA;AAKA,SAAS,mBAAmB,OAAO;AACjC,QAAM,YAAY,MAAM;AAExB,MAAI,WAAW;AACb,UAAM,SAAS,CAAA;AACf,QAAI;AAEF,gBAAU,OAAO,QAAQ,WAAS;AAEhC,YAAI,MAAM,WAAW,QAAQ;AAE3B,iBAAO,KAAK,GAAG,MAAM,WAAW,MAAM;QAChD;MACA,CAAO;AACD,aAAO;IACb,SAAa,KAAK;AACZ,aAAO;IACb;EACA;AACE,SAAO;AACT;AC7IA,SAAS,gBACP,cACA;A1BtBF;A0BuBE,MAAI,OAAO,uBAAuB,aAAa,CAAC,oBAAoB;AAClE,WAAO;EACX;AAEE,QAAM,UAAU,kBAAgB,eAAS,MAAT,mBAAa;AAC7C,SACE,CAAC,CAAC;GAED,QAAQ,oBAAoB,QAAQ,CAAC,CAAC,QAAQ;AAEnD;ACjCK,IAAC,sBAAsB;ACI5B,IAAM,eAAe;AAGrB,IAAM,YAAY;AAElB,SAAS,gBAAgB,UAAU;AACjC,SAAO,aAAa,UAAU,aAAa;AAC7C;AAWA,SAAS,YAAY,KAAK,eAAe,OAAO;AAC9C,QAAM,EAAE,MAAM,MAAM,MAAM,MAAM,WAAW,UAAU,UAAS,IAAK;AACnE,SACE,GAAG,QAAQ,MAAM,SAAS,GAAG,gBAAgB,OAAO,IAAI,IAAI,KAAK,EAAE,IAC/D,IAAI,GAAG,OAAO,IAAI,IAAI,KAAK,EAAE,IAAI,OAAO,GAAG,IAAI,MAAM,IAAI,GAAG,SAAS;AAE7E;AAQA,SAAS,cAAc,KAAK;AAC1B,QAAM,QAAQ,UAAU,KAAK,GAAG;AAEhC,MAAI,CAAC,OAAO;AAEV,mBAAe,MAAM;AAEnB,cAAQ,MAAM,uBAAuB,GAAG,EAAE;IAChD,CAAK;AACD,WAAO;EACX;AAEE,QAAM,CAAC,UAAU,WAAW,OAAO,IAAI,OAAO,IAAI,OAAO,IAAI,WAAW,EAAE,IAAI,MAAM,MAAM,CAAC;AAC3F,MAAI,OAAO;AACX,MAAI,YAAY;AAEhB,QAAM,QAAQ,UAAU,MAAM,GAAG;AACjC,MAAI,MAAM,SAAS,GAAG;AACpB,WAAO,MAAM,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG;AAClC,gBAAY,MAAM,IAAG;EACzB;AAEE,MAAI,WAAW;AACb,UAAM,eAAe,UAAU,MAAM,MAAM;AAC3C,QAAI,cAAc;AAChB,kBAAY,aAAa,CAAC;IAChC;EACA;AAEE,SAAO,kBAAkB,EAAE,MAAM,MAAM,MAAM,WAAW,MAAM,UAAqB,UAAS,CAAE;AAChG;AAEA,SAAS,kBAAkB,YAAY;AACrC,SAAO;IACL,UAAU,WAAW;IACrB,WAAW,WAAW,aAAa;IACnC,MAAM,WAAW,QAAQ;IACzB,MAAM,WAAW;IACjB,MAAM,WAAW,QAAQ;IACzB,MAAM,WAAW,QAAQ;IACzB,WAAW,WAAW;EAC1B;AACA;AAEA,SAAS,YAAY,KAAK;AACxB,MAAI,CAAC,aAAa;AAChB,WAAO;EACX;AAEE,QAAM,EAAE,MAAM,WAAW,SAAQ,IAAK;AAEtC,QAAM,qBAAqB,CAAC,YAAY,aAAa,QAAQ,WAAW;AACxE,QAAM,8BAA8B,mBAAmB,KAAK,eAAa;AACvE,QAAI,CAAC,IAAI,SAAS,GAAG;AACnB,aAAO,MAAM,uBAAuB,SAAS,UAAU;AACvD,aAAO;IACb;AACI,WAAO;EACX,CAAG;AAED,MAAI,6BAA6B;AAC/B,WAAO;EACX;AAEE,MAAI,CAAC,UAAU,MAAM,OAAO,GAAG;AAC7B,WAAO,MAAM,yCAAyC,SAAS,EAAE;AACjE,WAAO;EACX;AAEE,MAAI,CAAC,gBAAgB,QAAQ,GAAG;AAC9B,WAAO,MAAM,wCAAwC,QAAQ,EAAE;AAC/D,WAAO;EACX;AAEE,MAAI,QAAQ,MAAM,SAAS,MAAM,EAAE,CAAC,GAAG;AACrC,WAAO,MAAM,oCAAoC,IAAI,EAAE;AACvD,WAAO;EACX;AAEE,SAAO;AACT;AAQA,SAAS,wBAAwB,MAAM;AACrC,QAAM,QAAQ,KAAK,MAAM,YAAY;AAErC,SAAO,+BAAQ;AACjB;AAMA,SAAS,QAAQ,MAAM;AACrB,QAAM,aAAa,OAAO,SAAS,WAAW,cAAc,IAAI,IAAI,kBAAkB,IAAI;AAC1F,MAAI,CAAC,cAAc,CAAC,YAAY,UAAU,GAAG;AAC3C,WAAO;EACX;AACE,SAAO;AACT;AC9HA,IAAM,mBAAmB;AAezB,SAAS,oCAAoC,UAAU,QAAQ;AAC7D,QAAM,UAAU,OAAO,WAAU;AAEjC,QAAM,EAAE,WAAW,YAAY,KAAI,IAAK,OAAO,OAAM,KAAM,CAAA;AAE3D,MAAI;AACJ,MAAI,QAAQ,OAAO;AACjB,aAAS,OAAO,QAAQ,KAAK;EACjC,WAAa,MAAM;AACf,aAAS,wBAAwB,IAAI;EACzC;AAIE,QAAM,MAAM;IACV,aAAa,QAAQ,eAAe;IACpC,SAAS,QAAQ;IACjB;IACA;IACA;EACJ;AAEE,SAAO,KAAK,aAAa,GAAG;AAE5B,SAAO;AACT;AAKA,SAAS,mCAAmC,QAAQ,OAAO;AACzD,QAAM,qBAAqB,MAAM,sBAAqB;AACtD,SAAO,mBAAmB,OAAO,oCAAoC,mBAAmB,SAAS,MAAM;AACzG;AASA,SAAS,kCAAkC,MAAM;A7BvEjD;A6BwEE,QAAM,SAAS,UAAS;AACxB,MAAI,CAAC,QAAQ;AACX,WAAO,CAAA;EACX;AAEE,QAAM,WAAW,YAAY,IAAI;AACjC,QAAM,eAAe,WAAW,QAAQ;AACxC,QAAM,qBAAqB,aAAa;AACxC,QAAM,aAAa,SAAS,YAAW,EAAG;AAI1C,QAAM,sBACJ,yCAAY,IAAI,0BAChB,mBAAmB,qCAAqC,KACxD,mBAAmB,oDAAoD;AAEzE,WAAS,0BAA0BC,MAAK;AACtC,QAAI,OAAO,uBAAuB,YAAY,OAAO,uBAAuB,UAAU;AACpF,MAAAA,KAAI,cAAc,GAAG,kBAAkB;IAC7C;AACI,WAAOA;EACX;AAGE,QAAM,YAAa,SAAW,gBAAgB;AAC9C,MAAI,WAAW;AACb,WAAO,0BAA0B,SAAS;EAC9C;AAGE,QAAM,gBAAgB,yCAAY,IAAI;AAGtC,QAAM,kBAAkB,iBAAiB,sCAAsC,aAAa;AAE5F,MAAI,iBAAiB;AACnB,WAAO,0BAA0B,eAAe;EACpD;AAGE,QAAM,MAAM,oCAAoC,KAAK,YAAW,EAAG,SAAS,MAAM;AAGlF,QAAM,SAAS,mBAAmB,gCAAgC;AAGlE,QAAM,OAAO,aAAa;AAC1B,MAAI,WAAW,SAAS,MAAM;AAC5B,QAAI,cAAc;EACtB;AAKE,MAAI,gBAAe,GAAI;AACrB,QAAI,UAAU,OAAO,cAAc,QAAQ,CAAC;AAC5C,QAAI;;KAGF,yCAAY,IAAI;MAEhB,6BAAwB,QAAQ,EAAE,UAAlC,mBAAyC,wBAAwB,WAAW;EAClF;AAEE,4BAA0B,GAAG;AAE7B,SAAO,KAAK,aAAa,KAAK,QAAQ;AAEtC,SAAO;AACT;ACtHA,SAAS,UAAU,OAAO,QAAQ,KAAK,gBAAgB,UAAW;AAChE,MAAI;AAEF,WAAO,MAAM,IAAI,OAAO,OAAO,aAAa;EAChD,SAAW,KAAK;AACZ,WAAO,EAAE,OAAO,yBAAyB,GAAG,IAAG;EACnD;AACA;AAGA,SAAS,gBAEP,QAEA,QAAQ,GAER,UAAU,MAAM,MAChB;AACA,QAAM,aAAa,UAAU,QAAQ,KAAK;AAE1C,MAAI,SAAS,UAAU,IAAI,SAAS;AAClC,WAAO,gBAAgB,QAAQ,QAAQ,GAAG,OAAO;EACrD;AAEE,SAAO;AACT;AAWA,SAAS,MACP,KACA,OACA,QAAQ,UACR,gBAAgB,UAChB,OAAO,YAAW,GAClB;AACA,QAAM,CAAC,SAAS,SAAS,IAAI;AAG7B,MACE,SAAS;EACT,CAAC,WAAW,QAAQ,EAAE,SAAS,OAAO,KAAK,KAC1C,OAAO,UAAU,YAAY,OAAO,SAAS,KAAK,GACnD;AACA,WAAO;EACX;AAEE,QAAM,cAAc,eAAe,KAAK,KAAK;AAI7C,MAAI,CAAC,YAAY,WAAW,UAAU,GAAG;AACvC,WAAO;EACX;AAOE,MAAK,MAAQ,+BAA+B,GAAG;AAC7C,WAAO;EACX;AAKE,QAAM,iBACJ,OAAQ,MAAQ,yCAAyC,MAAM,WACzD,MAAQ,yCAAyC,IACnD;AAGN,MAAI,mBAAmB,GAAG;AAExB,WAAO,YAAY,QAAQ,WAAW,EAAE;EAC5C;AAGE,MAAI,QAAQ,KAAK,GAAG;AAClB,WAAO;EACX;AAGE,QAAM,kBAAkB;AACxB,MAAI,mBAAmB,OAAO,gBAAgB,WAAW,YAAY;AACnE,QAAI;AACF,YAAM,YAAY,gBAAgB,OAAM;AAExC,aAAO,MAAM,IAAI,WAAW,iBAAiB,GAAG,eAAe,IAAI;IACzE,SAAa,KAAK;IAElB;EACA;AAKE,QAAM,aAAc,MAAM,QAAQ,KAAK,IAAI,CAAA,IAAK,CAAA;AAChD,MAAI,WAAW;AAIf,QAAM,YAAY,qBAAqB,KAAK;AAE5C,aAAW,YAAY,WAAW;AAEhC,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,WAAW,QAAQ,GAAG;AAC9D;IACN;AAEI,QAAI,YAAY,eAAe;AAC7B,iBAAW,QAAQ,IAAI;AACvB;IACN;AAGI,UAAM,aAAa,UAAU,QAAQ;AACrC,eAAW,QAAQ,IAAI,MAAM,UAAU,YAAY,iBAAiB,GAAG,eAAe,IAAI;AAE1F;EACJ;AAGE,YAAU,KAAK;AAGf,SAAO;AACT;AAYA,SAAS,eACP,KAGA,OACA;AACA,MAAI;AACF,QAAI,QAAQ,YAAY,SAAS,OAAO,UAAU,YAAa,MAAQ,SAAS;AAC9E,aAAO;IACb;AAEI,QAAI,QAAQ,iBAAiB;AAC3B,aAAO;IACb;AAKI,QAAI,OAAO,WAAW,eAAe,UAAU,QAAQ;AACrD,aAAO;IACb;AAGI,QAAI,OAAO,WAAW,eAAe,UAAU,QAAQ;AACrD,aAAO;IACb;AAGI,QAAI,OAAO,aAAa,eAAe,UAAU,UAAU;AACzD,aAAO;IACb;AAEI,QAAI,eAAe,KAAK,GAAG;AACzB,aAAO;IACb;AAGI,QAAI,iBAAiB,KAAK,GAAG;AAC3B,aAAO;IACb;AAEI,QAAI,OAAO,UAAU,YAAY,CAAC,OAAO,SAAS,KAAK,GAAG;AACxD,aAAO,IAAI,KAAK;IACtB;AAEI,QAAI,OAAO,UAAU,YAAY;AAC/B,aAAO,cAAc,gBAAgB,KAAK,CAAC;IACjD;AAEI,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,IAAI,OAAO,KAAK,CAAC;IAC9B;AAGI,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,YAAY,OAAO,KAAK,CAAC;IACtC;AAMI,UAAM,UAAU,mBAAmB,KAAK;AAGxC,QAAI,qBAAqB,KAAK,OAAO,GAAG;AACtC,aAAO,iBAAiB,OAAO;IACrC;AAEI,WAAO,WAAW,OAAO;EAC7B,SAAW,KAAK;AACZ,WAAO,yBAAyB,GAAG;EACvC;AACA;AAGA,SAAS,mBAAmB,OAAO;AACjC,QAAM,YAAY,OAAO,eAAe,KAAK;AAE7C,UAAO,uCAAW,eAAc,UAAU,YAAY,OAAO;AAC/D;AAGA,SAAS,WAAW,OAAO;AAEzB,SAAO,CAAC,CAAC,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE;AAC3C;AAIA,SAAS,SAAS,OAAO;AACvB,SAAO,WAAW,KAAK,UAAU,KAAK,CAAC;AACzC;AAmCA,SAAS,cAAc;AACrB,QAAM,QAAQ,oBAAI,QAAO;AACzB,WAAS,QAAQ,KAAK;AACpB,QAAI,MAAM,IAAI,GAAG,GAAG;AAClB,aAAO;IACb;AACI,UAAM,IAAI,GAAG;AACb,WAAO;EACX;AAEE,WAAS,UAAU,KAAK;AACtB,UAAM,OAAO,GAAG;EACpB;AACE,SAAO,CAAC,SAAS,SAAS;AAC5B;ACnTA,IAAI;CAAS,SAAUC,SAAQ;AAE7B,QAAM,UAAU;AAAG,EAAAA,QAAOA,QAAO,SAAS,IAAI,OAAO,IAAI;AAEzD,QAAM,WAAW;AAAG,EAAAA,QAAOA,QAAO,UAAU,IAAI,QAAQ,IAAI;AAE5D,QAAM,WAAW;AAAG,EAAAA,QAAOA,QAAO,UAAU,IAAI,QAAQ,IAAI;AAC9D,GAAG,WAAW,SAAS,CAAA,EAAG;AAU1B,SAAS,oBAAoB,OAAO;AAClC,SAAO,IAAI,YAAY,aAAW;AAChC,YAAQ,KAAK;EACjB,CAAG;AACH;AAQA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,IAAI,YAAY,CAAC,GAAG,WAAW;AACpC,WAAO,MAAM;EACjB,CAAG;AACH;AAMA,IAAM,cAAN,MAAM,aAAY;EAEf,YAAY,UAAU;AACrB,SAAK,SAAS,OAAO;AACrB,SAAK,YAAY,CAAA;AAEjB,SAAK,aAAa,QAAQ;EAC9B;;EAGG,KACC,aACA,YACA;AACA,WAAO,IAAI,aAAY,CAAC,SAAS,WAAW;AAC1C,WAAK,UAAU,KAAK;QAClB;QACA,YAAU;AACR,cAAI,CAAC,aAAa;AAGhB,oBAAQ,MAAM;UAC1B,OAAiB;AACL,gBAAI;AACF,sBAAQ,YAAY,MAAM,CAAC;YACzC,SAAqB,GAAG;AACV,qBAAO,CAAC;YACtB;UACA;QACA;QACQ,YAAU;AACR,cAAI,CAAC,YAAY;AACf,mBAAO,MAAM;UACzB,OAAiB;AACL,gBAAI;AACF,sBAAQ,WAAW,MAAM,CAAC;YACxC,SAAqB,GAAG;AACV,qBAAO,CAAC;YACtB;UACA;QACA;MACA,CAAO;AACD,WAAK,iBAAgB;IAC3B,CAAK;EACL;;EAGG,MACC,YACA;AACA,WAAO,KAAK,KAAK,SAAO,KAAK,UAAU;EAC3C;;EAGG,QAAQ,WAAW;AAClB,WAAO,IAAI,aAAY,CAAC,SAAS,WAAW;AAC1C,UAAI;AACJ,UAAI;AAEJ,aAAO,KAAK;QACV,WAAS;AACP,uBAAa;AACb,gBAAM;AACN,cAAI,WAAW;AACb,sBAAS;UACrB;QACA;QACQ,YAAU;AACR,uBAAa;AACb,gBAAM;AACN,cAAI,WAAW;AACb,sBAAS;UACrB;QACA;MACA,EAAQ,KAAK,MAAM;AACX,YAAI,YAAY;AACd,iBAAO,GAAG;AACV;QACV;AAEQ,gBAAQ,GAAG;MACnB,CAAO;IACP,CAAK;EACL;;EAGG,mBAAmB;AAClB,QAAI,KAAK,WAAW,OAAO,SAAS;AAClC;IACN;AAEI,UAAM,iBAAiB,KAAK,UAAU,MAAK;AAC3C,SAAK,YAAY,CAAA;AAEjB,mBAAe,QAAQ,aAAW;AAChC,UAAI,QAAQ,CAAC,GAAG;AACd;MACR;AAEM,UAAI,KAAK,WAAW,OAAO,UAAU;AACnC,gBAAQ,CAAC,EAAE,KAAK,MAAM;MAC9B;AAEM,UAAI,KAAK,WAAW,OAAO,UAAU;AACnC,gBAAQ,CAAC,EAAE,KAAK,MAAM;MAC9B;AAEM,cAAQ,CAAC,IAAI;IACnB,CAAK;EACL;;EAGG,aAAa,UAAU;AACtB,UAAM,YAAY,CAAC,OAAO,UAAU;AAClC,UAAI,KAAK,WAAW,OAAO,SAAS;AAClC;MACR;AAEM,UAAI,WAAW,KAAK,GAAG;AACrB,aAAM,MAAQ,KAAK,SAAS,MAAM;AAClC;MACR;AAEM,WAAK,SAAS;AACd,WAAK,SAAS;AAEd,WAAK,iBAAgB;IAC3B;AAEI,UAAM,UAAU,CAAC,UAAU;AACzB,gBAAU,OAAO,UAAU,KAAK;IACtC;AAEI,UAAM,SAAS,CAAC,WAAW;AACzB,gBAAU,OAAO,UAAU,MAAM;IACvC;AAEI,QAAI;AACF,eAAS,SAAS,MAAM;IAC9B,SAAa,GAAG;AACV,aAAO,CAAC;IACd;EACA;AACA;ACnLA,SAAS,sBACP,YACA,OACA,MACA,QAAQ,GACR;AACA,SAAO,IAAI,YAAY,CAAC,SAAS,WAAW;AAC1C,UAAM,YAAY,WAAW,KAAK;AAClC,QAAI,UAAU,QAAQ,OAAO,cAAc,YAAY;AACrD,cAAQ,KAAK;IACnB,OAAW;AACL,YAAM,SAAS,UAAU,EAAE,GAAG,MAAK,GAAI,IAAI;AAE3C,qBAAe,UAAU,MAAM,WAAW,QAAQ,OAAO,IAAI,oBAAoB,UAAU,EAAE,iBAAiB;AAE9G,UAAI,WAAW,MAAM,GAAG;AACtB,aAAK,OACF,KAAK,WAAS,sBAAsB,YAAY,OAAO,MAAM,QAAQ,CAAC,EAAE,KAAK,OAAO,CAAC,EACrF,KAAK,MAAM,MAAM;MAC5B,OAAa;AACL,aAAK,sBAAsB,YAAY,QAAQ,MAAM,QAAQ,CAAC,EAC3D,KAAK,OAAO,EACZ,KAAK,MAAM,MAAM;MAC5B;IACA;EACA,CAAG;AACH;AChCA,IAAI;AACJ,IAAI;AACJ,IAAI;AAKJ,SAAS,wBAAwB,aAAa;AAC5C,QAAM,aAAa,WAAW;AAC9B,MAAI,CAAC,YAAY;AACf,WAAO,CAAA;EACX;AAEE,QAAM,cAAc,OAAO,KAAK,UAAU;AAI1C,MAAI,0BAA0B,YAAY,WAAW,eAAe;AAClE,WAAO;EACX;AAEE,kBAAgB,YAAY;AAG5B,2BAAyB,YAAY,OAAO,CAAC,KAAK,aAAa;AAC7D,QAAI,CAAC,oBAAoB;AACvB,2BAAqB,CAAA;IAC3B;AAEI,UAAM,SAAS,mBAAmB,QAAQ;AAE1C,QAAI,QAAQ;AACV,UAAI,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC;IAC/B,OAAW;AACL,YAAM,cAAc,YAAY,QAAQ;AAExC,eAAS,IAAI,YAAY,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,cAAM,aAAa,YAAY,CAAC;AAChC,cAAM,WAAW,yCAAY;AAC7B,cAAM,UAAU,WAAW,QAAQ;AAEnC,YAAI,YAAY,SAAS;AACvB,cAAI,QAAQ,IAAI;AAChB,6BAAmB,QAAQ,IAAI,CAAC,UAAU,OAAO;AACjD;QACV;MACA;IACA;AAEI,WAAO;EACX,GAAK,CAAA,CAAE;AAEL,SAAO;AACT;AChDA,SAAS,sBAAsB,OAAO,MAAM;AAC1C,QAAM,EAAE,aAAa,MAAM,aAAa,sBAAqB,IAAK;AAGlE,mBAAiB,OAAO,IAAI;AAK5B,MAAI,MAAM;AACR,qBAAiB,OAAO,IAAI;EAChC;AAEE,0BAAwB,OAAO,WAAW;AAC1C,0BAAwB,OAAO,WAAW;AAC1C,0BAAwB,OAAO,qBAAqB;AACtD;AAGA,SAAS,eAAe,MAAM,WAAW;AACvC,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACJ,IAAM;AAEJ,6BAA2B,MAAM,SAAS,KAAK;AAC/C,6BAA2B,MAAM,QAAQ,IAAI;AAC7C,6BAA2B,MAAM,QAAQ,IAAI;AAC7C,6BAA2B,MAAM,YAAY,QAAQ;AAErD,OAAK,wBAAwB,MAAM,KAAK,uBAAuB,uBAAuB,CAAC;AAEvF,MAAI,OAAO;AACT,SAAK,QAAQ;EACjB;AAEE,MAAI,iBAAiB;AACnB,SAAK,kBAAkB;EAC3B;AAEE,MAAI,MAAM;AACR,SAAK,OAAO;EAChB;AAEE,MAAI,YAAY,QAAQ;AACtB,SAAK,cAAc,CAAC,GAAG,KAAK,aAAa,GAAG,WAAW;EAC3D;AAEE,MAAI,YAAY,QAAQ;AACtB,SAAK,cAAc,CAAC,GAAG,KAAK,aAAa,GAAG,WAAW;EAC3D;AAEE,MAAI,gBAAgB,QAAQ;AAC1B,SAAK,kBAAkB,CAAC,GAAG,KAAK,iBAAiB,GAAG,eAAe;EACvE;AAEE,MAAI,YAAY,QAAQ;AACtB,SAAK,cAAc,CAAC,GAAG,KAAK,aAAa,GAAG,WAAW;EAC3D;AAEE,OAAK,qBAAqB,EAAE,GAAG,KAAK,oBAAoB,GAAG,mBAAkB;AAC/E;AAMA,SAAS,2BAER,MAAM,MAAM,UAAU;AACrB,OAAK,IAAI,IAAI,MAAM,KAAK,IAAI,GAAG,UAAU,CAAC;AAC5C;AAEA,SAAS,iBAAiB,OAAO,MAAM;AACrC,QAAM,EAAE,OAAO,MAAM,MAAM,UAAU,OAAO,gBAAe,IAAK;AAEhE,MAAI,OAAO,KAAK,KAAK,EAAE,QAAQ;AAC7B,UAAM,QAAQ,EAAE,GAAG,OAAO,GAAG,MAAM,MAAK;EAC5C;AAEE,MAAI,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC5B,UAAM,OAAO,EAAE,GAAG,MAAM,GAAG,MAAM,KAAI;EACzC;AAEE,MAAI,OAAO,KAAK,IAAI,EAAE,QAAQ;AAC5B,UAAM,OAAO,EAAE,GAAG,MAAM,GAAG,MAAM,KAAI;EACzC;AAEE,MAAI,OAAO,KAAK,QAAQ,EAAE,QAAQ;AAChC,UAAM,WAAW,EAAE,GAAG,UAAU,GAAG,MAAM,SAAQ;EACrD;AAEE,MAAI,OAAO;AACT,UAAM,QAAQ;EAClB;AAGE,MAAI,mBAAmB,MAAM,SAAS,eAAe;AACnD,UAAM,cAAc;EACxB;AACA;AAEA,SAAS,wBAAwB,OAAO,aAAa;AACnD,QAAM,oBAAoB,CAAC,GAAI,MAAM,eAAe,CAAA,GAAK,GAAG,WAAW;AACvE,QAAM,cAAc,kBAAkB,SAAS,oBAAoB;AACrE;AAEA,SAAS,wBAAwB,OAAO,uBAAuB;AAC7D,QAAM,wBAAwB;IAC5B,GAAG,MAAM;IACT,GAAG;EACP;AACA;AAEA,SAAS,iBAAiB,OAAO,MAAM;AACrC,QAAM,WAAW;IACf,OAAO,mBAAmB,IAAI;IAC9B,GAAG,MAAM;EACb;AAEE,QAAM,wBAAwB;IAC5B,wBAAwB,kCAAkC,IAAI;IAC9D,GAAG,MAAM;EACb;AAEE,QAAM,WAAW,YAAY,IAAI;AACjC,QAAM,kBAAkB,WAAW,QAAQ,EAAE;AAC7C,MAAI,mBAAmB,CAAC,MAAM,eAAe,MAAM,SAAS,eAAe;AACzE,UAAM,cAAc;EACxB;AACA;AAMA,SAAS,wBAAwB,OAAO,aAAa;AAEnD,QAAM,cAAc,MAAM,cACtB,MAAM,QAAQ,MAAM,WAAW,IAC7B,MAAM,cACN,CAAC,MAAM,WAAW,IACpB,CAAA;AAGJ,MAAI,aAAa;AACf,UAAM,cAAc,MAAM,YAAY,OAAO,WAAW;EAC5D;AAGE,MAAI,CAAC,MAAM,YAAY,QAAQ;AAC7B,WAAO,MAAM;EACjB;AACA;AC3IA,SAAS,aACP,SACA,OACA,MACA,OACA,QACA,gBACA;AACA,QAAM,EAAE,iBAAiB,GAAG,sBAAsB,IAAI,IAAK;AAC3D,QAAM,WAAW;IACf,GAAG;IACH,UAAU,MAAM,YAAY,KAAK,YAAY,MAAK;IAClD,WAAW,MAAM,aAAa,uBAAsB;EACxD;AACE,QAAM,eAAe,KAAK,gBAAgB,QAAQ,aAAa,IAAI,OAAK,EAAE,IAAI;AAE9E,qBAAmB,UAAU,OAAO;AACpC,4BAA0B,UAAU,YAAY;AAEhD,MAAI,QAAQ;AACV,WAAO,KAAK,sBAAsB,KAAK;EAC3C;AAGE,MAAI,MAAM,SAAS,QAAW;AAC5B,kBAAc,UAAU,QAAQ,WAAW;EAC/C;AAIE,QAAM,aAAa,cAAc,OAAO,KAAK,cAAc;AAE3D,MAAI,KAAK,WAAW;AAClB,0BAAsB,UAAU,KAAK,SAAS;EAClD;AAEE,QAAM,wBAAwB,SAAS,OAAO,mBAAkB,IAAK,CAAA;AAKrE,QAAM,OAAO,eAAc,EAAG,aAAY;AAE1C,MAAI,gBAAgB;AAClB,UAAM,gBAAgB,eAAe,aAAY;AACjD,mBAAe,MAAM,aAAa;EACtC;AAEE,MAAI,YAAY;AACd,UAAM,iBAAiB,WAAW,aAAY;AAC9C,mBAAe,MAAM,cAAc;EACvC;AAEE,QAAM,cAAc,CAAC,GAAI,KAAK,eAAe,CAAA,GAAK,GAAG,KAAK,WAAW;AACrE,MAAI,YAAY,QAAQ;AACtB,SAAK,cAAc;EACvB;AAEE,wBAAsB,UAAU,IAAI;AAEpC,QAAM,kBAAkB;IACtB,GAAG;;IAEH,GAAG,KAAK;EACZ;AAEE,QAAM,SAAS,sBAAsB,iBAAiB,UAAU,IAAI;AAEpE,SAAO,OAAO,KAAK,SAAO;AACxB,QAAI,KAAK;AAKP,qBAAe,GAAG;IACxB;AAEI,QAAI,OAAO,mBAAmB,YAAY,iBAAiB,GAAG;AAC5D,aAAO,eAAe,KAAK,gBAAgB,mBAAmB;IACpE;AACI,WAAO;EACX,CAAG;AACH;AAWA,SAAS,mBAAmB,OAAO,SAAS;AAC1C,QAAM,EAAE,aAAa,SAAS,MAAM,iBAAiB,IAAG,IAAK;AAI7D,QAAM,cAAc,MAAM,eAAe,eAAe;AAExD,MAAI,CAAC,MAAM,WAAW,SAAS;AAC7B,UAAM,UAAU;EACpB;AAEE,MAAI,CAAC,MAAM,QAAQ,MAAM;AACvB,UAAM,OAAO;EACjB;AAEE,QAAM,UAAU,MAAM;AACtB,MAAI,mCAAS,KAAK;AAChB,YAAQ,MAAM,SAAS,QAAQ,KAAK,cAAc;EACtD;AACA;AAKA,SAAS,cAAc,OAAO,aAAa;AnCrJ3C;AmCuJE,QAAM,qBAAqB,wBAAwB,WAAW;AAE9D,oBAAM,cAAN,mBAAiB,WAAjB,mBAAyB,QAAQ,eAAa;AnCzJhD,QAAAC,KAAAC;AmC0JI,KAAAA,OAAAD,MAAA,UAAU,eAAV,gBAAAA,IAAsB,WAAtB,gBAAAC,IAA8B,QAAQ,WAAS;AAC7C,UAAI,MAAM,UAAU;AAClB,cAAM,WAAW,mBAAmB,MAAM,QAAQ;MAC1D;IACA;EACA;AACA;AAKA,SAAS,eAAe,OAAO;AnCrK/B;AmCuKE,QAAM,qBAAqB,CAAA;AAC3B,oBAAM,cAAN,mBAAiB,WAAjB,mBAAyB,QAAQ,eAAa;AnCxKhD,QAAAD,KAAAC;AmCyKI,KAAAA,OAAAD,MAAA,UAAU,eAAV,gBAAAA,IAAsB,WAAtB,gBAAAC,IAA8B,QAAQ,WAAS;AAC7C,UAAI,MAAM,UAAU;AAClB,YAAI,MAAM,UAAU;AAClB,6BAAmB,MAAM,QAAQ,IAAI,MAAM;QACrD,WAAmB,MAAM,UAAU;AACzB,6BAAmB,MAAM,QAAQ,IAAI,MAAM;QACrD;AACQ,eAAO,MAAM;MACrB;IACA;EACA;AAEE,MAAI,OAAO,KAAK,kBAAkB,EAAE,WAAW,GAAG;AAChD;EACJ;AAGE,QAAM,aAAa,MAAM,cAAc,CAAA;AACvC,QAAM,WAAW,SAAS,MAAM,WAAW,UAAU,CAAA;AACrD,QAAM,SAAS,MAAM,WAAW;AAChC,SAAO,QAAQ,kBAAkB,EAAE,QAAQ,CAAC,CAAC,UAAU,QAAQ,MAAM;AACnE,WAAO,KAAK;MACV,MAAM;MACN,WAAW;MACX;IACN,CAAK;EACL,CAAG;AACH;AAMA,SAAS,0BAA0B,OAAO,kBAAkB;AAC1D,MAAI,iBAAiB,SAAS,GAAG;AAC/B,UAAM,MAAM,MAAM,OAAO,CAAA;AACzB,UAAM,IAAI,eAAe,CAAC,GAAI,MAAM,IAAI,gBAAgB,CAAA,GAAK,GAAG,gBAAgB;EACpF;AACA;AAYA,SAAS,eAAe,OAAO,OAAO,YAAY;AnC3NlD;AmC4NE,MAAI,CAAC,OAAO;AACV,WAAO;EACX;AAEE,QAAM,aAAa;IACjB,GAAG;IACH,GAAI,MAAM,eAAe;MACvB,aAAa,MAAM,YAAY,IAAI,QAAM;QACvC,GAAG;QACH,GAAI,EAAE,QAAQ;UACZ,MAAM,UAAU,EAAE,MAAM,OAAO,UAAU;QACnD;MACA,EAAQ;IACR;IACI,GAAI,MAAM,QAAQ;MAChB,MAAM,UAAU,MAAM,MAAM,OAAO,UAAU;IACnD;IACI,GAAI,MAAM,YAAY;MACpB,UAAU,UAAU,MAAM,UAAU,OAAO,UAAU;IAC3D;IACI,GAAI,MAAM,SAAS;MACjB,OAAO,UAAU,MAAM,OAAO,OAAO,UAAU;IACrD;EACA;AASE,QAAI,WAAM,aAAN,mBAAgB,UAAS,WAAW,UAAU;AAChD,eAAW,SAAS,QAAQ,MAAM,SAAS;AAG3C,QAAI,MAAM,SAAS,MAAM,MAAM;AAC7B,iBAAW,SAAS,MAAM,OAAO,UAAU,MAAM,SAAS,MAAM,MAAM,OAAO,UAAU;IAC7F;EACA;AAGE,MAAI,MAAM,OAAO;AACf,eAAW,QAAQ,MAAM,MAAM,IAAI,UAAQ;AACzC,aAAO;QACL,GAAG;QACH,GAAI,KAAK,QAAQ;UACf,MAAM,UAAU,KAAK,MAAM,OAAO,UAAU;QACtD;MACA;IACA,CAAK;EACL;AAME,QAAI,WAAM,aAAN,mBAAgB,UAAS,WAAW,UAAU;AAChD,eAAW,SAAS,QAAQ,UAAU,MAAM,SAAS,OAAO,GAAG,UAAU;EAC7E;AAEE,SAAO;AACT;AAEA,SAAS,cAAc,OAAO,gBAAgB;AAC5C,MAAI,CAAC,gBAAgB;AACnB,WAAO;EACX;AAEE,QAAM,aAAa,QAAQ,MAAM,MAAK,IAAK,IAAI,MAAK;AACpD,aAAW,OAAO,cAAc;AAChC,SAAO;AACT;AAMA,SAAS,+BACP,MACA;AACW;AACT,WAAO;EACX;AAcA;AC5SA,SAAS,iBAAiB,WAAW,MAAM;AACzC,SAAO,gBAAe,EAAG,iBAAiB,WAAW,+BAAmC,CAAC;AAC3F;AASA,SAAS,eAAe,SAAS,gBAAgB;AAG/C,QAAM,QAA8D;AACpE,QAAM,UAA+C,EAAE,eAAc;AACrE,SAAO,gBAAe,EAAG,eAAe,SAAS,OAAO,OAAO;AACjE;AASA,SAAS,aAAa,OAAO,MAAM;AACjC,SAAO,gBAAe,EAAG,aAAa,OAAO,IAAI;AACnD;AAOA,SAAS,WAAW,MAAM,SAAS;AACjC,oBAAiB,EAAG,WAAW,MAAM,OAAO;AAC9C;AA4CA,SAAS,QAAQ,MAAM;AACrB,oBAAiB,EAAG,QAAQ,IAAI;AAClC;AAmJA,SAAS,aAAa,SAAS;AAC7B,QAAM,iBAAiB,kBAAiB;AACxC,QAAM,eAAe,gBAAe;AAGpC,QAAM,EAAE,UAAS,IAAK,WAAW,aAAa,CAAA;AAE9C,QAAM,UAAU,YAAY;IAC1B,MAAM,aAAa,QAAO,KAAM,eAAe,QAAO;IACtD,GAAI,aAAa,EAAE,UAAS;IAC5B,GAAG;EACP,CAAG;AAGD,QAAM,iBAAiB,eAAe,WAAU;AAChD,OAAI,iDAAgB,YAAW,MAAM;AACnC,kBAAc,gBAAgB,EAAE,QAAQ,SAAQ,CAAE;EACtD;AAEE,aAAU;AAGV,iBAAe,WAAW,OAAO;AAEjC,SAAO;AACT;AAKA,SAAS,aAAa;AACpB,QAAM,iBAAiB,kBAAiB;AACxC,QAAM,eAAe,gBAAe;AAEpC,QAAM,UAAU,aAAa,WAAU,KAAM,eAAe,WAAU;AACtE,MAAI,SAAS;AACX,iBAAa,OAAO;EACxB;AACE,qBAAkB;AAGlB,iBAAe,WAAU;AAC3B;AAKA,SAAS,qBAAqB;AAC5B,QAAM,iBAAiB,kBAAiB;AACxC,QAAM,SAAS,UAAS;AACxB,QAAM,UAAU,eAAe,WAAU;AACzC,MAAI,WAAW,QAAQ;AACrB,WAAO,eAAe,OAAO;EACjC;AACA;AAQA,SAAS,eAAe,MAAM,OAAO;AAEnC,MAAI,KAAK;AACP,eAAU;AACV;EACJ;AAGE,qBAAkB;AACpB;", "names": ["console", "logger", "withScope", "dsc", "States", "_a", "_b"]}