{"version": 3, "sources": ["../../.pnpm/@vue+server-renderer@3.5.13_vue@3.5.13_typescript@5.8.3_/node_modules/@vue/server-renderer/dist/server-renderer.esm-bundler.js"], "sourcesContent": ["/**\n* @vue/server-renderer v3.5.13\n* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors\n* @license MIT\n**/\nimport { createVNode, ssrUtils, ssrContextKey, warn as warn$2, Fragment, Static, Comment, Text, mergeProps, createApp, initDirectivesForSSR } from 'vue';\nimport { isOn, isRenderableAttrValue, isSVGTag, propsToAttrMap, isBooleanAttr, includeBooleanAttr, isSSRSafeAttrName, escapeHtml, normalizeClass, isString, normalizeStyle, stringifyStyle, makeMap, isArray, toDisplayString, extend, isFunction, EMPTY_OBJ, getGlobalThis, NOOP, isObject, looseEqual, looseIndexOf, isPromise, escapeHtmlComment, isVoidTag } from '@vue/shared';\nexport { includeBooleanAttr as ssrIncludeBooleanAttr } from '@vue/shared';\n\nconst shouldIgnoreProp = /* @__PURE__ */ makeMap(\n  `,key,ref,innerHTML,textContent,ref_key,ref_for`\n);\nfunction ssrRenderAttrs(props, tag) {\n  let ret = \"\";\n  for (const key in props) {\n    if (shouldIgnoreProp(key) || isOn(key) || tag === \"textarea\" && key === \"value\") {\n      continue;\n    }\n    const value = props[key];\n    if (key === \"class\") {\n      ret += ` class=\"${ssrRenderClass(value)}\"`;\n    } else if (key === \"style\") {\n      ret += ` style=\"${ssrRenderStyle(value)}\"`;\n    } else if (key === \"className\") {\n      ret += ` class=\"${String(value)}\"`;\n    } else {\n      ret += ssrRenderDynamicAttr(key, value, tag);\n    }\n  }\n  return ret;\n}\nfunction ssrRenderDynamicAttr(key, value, tag) {\n  if (!isRenderableAttrValue(value)) {\n    return ``;\n  }\n  const attrKey = tag && (tag.indexOf(\"-\") > 0 || isSVGTag(tag)) ? key : propsToAttrMap[key] || key.toLowerCase();\n  if (isBooleanAttr(attrKey)) {\n    return includeBooleanAttr(value) ? ` ${attrKey}` : ``;\n  } else if (isSSRSafeAttrName(attrKey)) {\n    return value === \"\" ? ` ${attrKey}` : ` ${attrKey}=\"${escapeHtml(value)}\"`;\n  } else {\n    console.warn(\n      `[@vue/server-renderer] Skipped rendering unsafe attribute name: ${attrKey}`\n    );\n    return ``;\n  }\n}\nfunction ssrRenderAttr(key, value) {\n  if (!isRenderableAttrValue(value)) {\n    return ``;\n  }\n  return ` ${key}=\"${escapeHtml(value)}\"`;\n}\nfunction ssrRenderClass(raw) {\n  return escapeHtml(normalizeClass(raw));\n}\nfunction ssrRenderStyle(raw) {\n  if (!raw) {\n    return \"\";\n  }\n  if (isString(raw)) {\n    return escapeHtml(raw);\n  }\n  const styles = normalizeStyle(raw);\n  return escapeHtml(stringifyStyle(styles));\n}\n\nfunction ssrRenderComponent(comp, props = null, children = null, parentComponent = null, slotScopeId) {\n  return renderComponentVNode(\n    createVNode(comp, props, children),\n    parentComponent,\n    slotScopeId\n  );\n}\n\nconst { ensureValidVNode } = ssrUtils;\nfunction ssrRenderSlot(slots, slotName, slotProps, fallbackRenderFn, push, parentComponent, slotScopeId) {\n  push(`<!--[-->`);\n  ssrRenderSlotInner(\n    slots,\n    slotName,\n    slotProps,\n    fallbackRenderFn,\n    push,\n    parentComponent,\n    slotScopeId\n  );\n  push(`<!--]-->`);\n}\nfunction ssrRenderSlotInner(slots, slotName, slotProps, fallbackRenderFn, push, parentComponent, slotScopeId, transition) {\n  const slotFn = slots[slotName];\n  if (slotFn) {\n    const slotBuffer = [];\n    const bufferedPush = (item) => {\n      slotBuffer.push(item);\n    };\n    const ret = slotFn(\n      slotProps,\n      bufferedPush,\n      parentComponent,\n      slotScopeId ? \" \" + slotScopeId : \"\"\n    );\n    if (isArray(ret)) {\n      const validSlotContent = ensureValidVNode(ret);\n      if (validSlotContent) {\n        renderVNodeChildren(\n          push,\n          validSlotContent,\n          parentComponent,\n          slotScopeId\n        );\n      } else if (fallbackRenderFn) {\n        fallbackRenderFn();\n      }\n    } else {\n      let isEmptySlot = true;\n      if (transition) {\n        isEmptySlot = false;\n      } else {\n        for (let i = 0; i < slotBuffer.length; i++) {\n          if (!isComment(slotBuffer[i])) {\n            isEmptySlot = false;\n            break;\n          }\n        }\n      }\n      if (isEmptySlot) {\n        if (fallbackRenderFn) {\n          fallbackRenderFn();\n        }\n      } else {\n        let start = 0;\n        let end = slotBuffer.length;\n        if (transition && slotBuffer[0] === \"<!--[-->\" && slotBuffer[end - 1] === \"<!--]-->\") {\n          start++;\n          end--;\n        }\n        for (let i = start; i < end; i++) {\n          push(slotBuffer[i]);\n        }\n      }\n    }\n  } else if (fallbackRenderFn) {\n    fallbackRenderFn();\n  }\n}\nconst commentTestRE = /^<!--[\\s\\S]*-->$/;\nconst commentRE = /<!--[^]*?-->/gm;\nfunction isComment(item) {\n  if (typeof item !== \"string\" || !commentTestRE.test(item)) return false;\n  if (item.length <= 8) return true;\n  return !item.replace(commentRE, \"\").trim();\n}\n\nfunction ssrRenderTeleport(parentPush, contentRenderFn, target, disabled, parentComponent) {\n  parentPush(\"<!--teleport start-->\");\n  const context = parentComponent.appContext.provides[ssrContextKey];\n  const teleportBuffers = context.__teleportBuffers || (context.__teleportBuffers = {});\n  const targetBuffer = teleportBuffers[target] || (teleportBuffers[target] = []);\n  const bufferIndex = targetBuffer.length;\n  let teleportContent;\n  if (disabled) {\n    contentRenderFn(parentPush);\n    teleportContent = `<!--teleport start anchor--><!--teleport anchor-->`;\n  } else {\n    const { getBuffer, push } = createBuffer();\n    push(`<!--teleport start anchor-->`);\n    contentRenderFn(push);\n    push(`<!--teleport anchor-->`);\n    teleportContent = getBuffer();\n  }\n  targetBuffer.splice(bufferIndex, 0, teleportContent);\n  parentPush(\"<!--teleport end-->\");\n}\n\nfunction ssrInterpolate(value) {\n  return escapeHtml(toDisplayString(value));\n}\n\nlet activeSub;\nlet batchDepth = 0;\nlet batchedSub;\nfunction startBatch() {\n  batchDepth++;\n}\nfunction endBatch() {\n  if (--batchDepth > 0) {\n    return;\n  }\n  let error;\n  while (batchedSub) {\n    let e = batchedSub;\n    batchedSub = void 0;\n    while (e) {\n      const next = e.next;\n      e.next = void 0;\n      e.flags &= ~8;\n      if (e.flags & 1) {\n        try {\n          ;\n          e.trigger();\n        } catch (err) {\n          if (!error) error = err;\n        }\n      }\n      e = next;\n    }\n  }\n  if (error) throw error;\n}\nlet shouldTrack = true;\nconst trackStack = [];\nfunction pauseTracking() {\n  trackStack.push(shouldTrack);\n  shouldTrack = false;\n}\nfunction resetTracking() {\n  const last = trackStack.pop();\n  shouldTrack = last === void 0 ? true : last;\n}\n\nclass Dep {\n  constructor(computed) {\n    this.computed = computed;\n    this.version = 0;\n    /**\n     * Link between this dep and the current active effect\n     */\n    this.activeLink = void 0;\n    /**\n     * Doubly linked list representing the subscribing effects (tail)\n     */\n    this.subs = void 0;\n    /**\n     * For object property deps cleanup\n     */\n    this.map = void 0;\n    this.key = void 0;\n    /**\n     * Subscriber counter\n     */\n    this.sc = 0;\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      this.subsHead = void 0;\n    }\n  }\n  track(debugInfo) {\n    {\n      return;\n    }\n  }\n  trigger(debugInfo) {\n    this.version++;\n    this.notify(debugInfo);\n  }\n  notify(debugInfo) {\n    startBatch();\n    try {\n      if (!!(process.env.NODE_ENV !== \"production\")) {\n        for (let head = this.subsHead; head; head = head.nextSub) {\n          if (head.sub.onTrigger && !(head.sub.flags & 8)) {\n            head.sub.onTrigger(\n              extend(\n                {\n                  effect: head.sub\n                },\n                debugInfo\n              )\n            );\n          }\n        }\n      }\n      for (let link = this.subs; link; link = link.prevSub) {\n        if (link.sub.notify()) {\n          ;\n          link.sub.dep.notify();\n        }\n      }\n    } finally {\n      endBatch();\n    }\n  }\n}\nconst targetMap = /* @__PURE__ */ new WeakMap();\nSymbol(\n  !!(process.env.NODE_ENV !== \"production\") ? \"Object iterate\" : \"\"\n);\nSymbol(\n  !!(process.env.NODE_ENV !== \"production\") ? \"Map keys iterate\" : \"\"\n);\nSymbol(\n  !!(process.env.NODE_ENV !== \"production\") ? \"Array iterate\" : \"\"\n);\nfunction track(target, type, key) {\n  if (shouldTrack && activeSub) {\n    let depsMap = targetMap.get(target);\n    if (!depsMap) {\n      targetMap.set(target, depsMap = /* @__PURE__ */ new Map());\n    }\n    let dep = depsMap.get(key);\n    if (!dep) {\n      depsMap.set(key, dep = new Dep());\n      dep.map = depsMap;\n      dep.key = key;\n    }\n    if (!!(process.env.NODE_ENV !== \"production\")) {\n      dep.track({\n        target,\n        type,\n        key\n      });\n    } else {\n      dep.track();\n    }\n  }\n}\n\nfunction isProxy(value) {\n  return value ? !!value[\"__v_raw\"] : false;\n}\nfunction toRaw(observed) {\n  const raw = observed && observed[\"__v_raw\"];\n  return raw ? toRaw(raw) : observed;\n}\n\nfunction isRef(r) {\n  return r ? r[\"__v_isRef\"] === true : false;\n}\n\nconst stack = [];\nfunction pushWarningContext$1(vnode) {\n  stack.push(vnode);\n}\nfunction popWarningContext$1() {\n  stack.pop();\n}\nlet isWarning = false;\nfunction warn$1(msg, ...args) {\n  if (isWarning) return;\n  isWarning = true;\n  pauseTracking();\n  const instance = stack.length ? stack[stack.length - 1].component : null;\n  const appWarnHandler = instance && instance.appContext.config.warnHandler;\n  const trace = getComponentTrace();\n  if (appWarnHandler) {\n    callWithErrorHandling(\n      appWarnHandler,\n      instance,\n      11,\n      [\n        // eslint-disable-next-line no-restricted-syntax\n        msg + args.map((a) => {\n          var _a, _b;\n          return (_b = (_a = a.toString) == null ? void 0 : _a.call(a)) != null ? _b : JSON.stringify(a);\n        }).join(\"\"),\n        instance && instance.proxy,\n        trace.map(\n          ({ vnode }) => `at <${formatComponentName(instance, vnode.type)}>`\n        ).join(\"\\n\"),\n        trace\n      ]\n    );\n  } else {\n    const warnArgs = [`[Vue warn]: ${msg}`, ...args];\n    if (trace.length && // avoid spamming console during tests\n    true) {\n      warnArgs.push(`\n`, ...formatTrace(trace));\n    }\n    console.warn(...warnArgs);\n  }\n  resetTracking();\n  isWarning = false;\n}\nfunction getComponentTrace() {\n  let currentVNode = stack[stack.length - 1];\n  if (!currentVNode) {\n    return [];\n  }\n  const normalizedStack = [];\n  while (currentVNode) {\n    const last = normalizedStack[0];\n    if (last && last.vnode === currentVNode) {\n      last.recurseCount++;\n    } else {\n      normalizedStack.push({\n        vnode: currentVNode,\n        recurseCount: 0\n      });\n    }\n    const parentInstance = currentVNode.component && currentVNode.component.parent;\n    currentVNode = parentInstance && parentInstance.vnode;\n  }\n  return normalizedStack;\n}\nfunction formatTrace(trace) {\n  const logs = [];\n  trace.forEach((entry, i) => {\n    logs.push(...i === 0 ? [] : [`\n`], ...formatTraceEntry(entry));\n  });\n  return logs;\n}\nfunction formatTraceEntry({ vnode, recurseCount }) {\n  const postfix = recurseCount > 0 ? `... (${recurseCount} recursive calls)` : ``;\n  const isRoot = vnode.component ? vnode.component.parent == null : false;\n  const open = ` at <${formatComponentName(\n    vnode.component,\n    vnode.type,\n    isRoot\n  )}`;\n  const close = `>` + postfix;\n  return vnode.props ? [open, ...formatProps(vnode.props), close] : [open + close];\n}\nfunction formatProps(props) {\n  const res = [];\n  const keys = Object.keys(props);\n  keys.slice(0, 3).forEach((key) => {\n    res.push(...formatProp(key, props[key]));\n  });\n  if (keys.length > 3) {\n    res.push(` ...`);\n  }\n  return res;\n}\nfunction formatProp(key, value, raw) {\n  if (isString(value)) {\n    value = JSON.stringify(value);\n    return raw ? value : [`${key}=${value}`];\n  } else if (typeof value === \"number\" || typeof value === \"boolean\" || value == null) {\n    return raw ? value : [`${key}=${value}`];\n  } else if (isRef(value)) {\n    value = formatProp(key, toRaw(value.value), true);\n    return raw ? value : [`${key}=Ref<`, value, `>`];\n  } else if (isFunction(value)) {\n    return [`${key}=fn${value.name ? `<${value.name}>` : ``}`];\n  } else {\n    value = toRaw(value);\n    return raw ? value : [`${key}=`, value];\n  }\n}\n\nconst ErrorTypeStrings = {\n  [\"sp\"]: \"serverPrefetch hook\",\n  [\"bc\"]: \"beforeCreate hook\",\n  [\"c\"]: \"created hook\",\n  [\"bm\"]: \"beforeMount hook\",\n  [\"m\"]: \"mounted hook\",\n  [\"bu\"]: \"beforeUpdate hook\",\n  [\"u\"]: \"updated\",\n  [\"bum\"]: \"beforeUnmount hook\",\n  [\"um\"]: \"unmounted hook\",\n  [\"a\"]: \"activated hook\",\n  [\"da\"]: \"deactivated hook\",\n  [\"ec\"]: \"errorCaptured hook\",\n  [\"rtc\"]: \"renderTracked hook\",\n  [\"rtg\"]: \"renderTriggered hook\",\n  [0]: \"setup function\",\n  [1]: \"render function\",\n  [2]: \"watcher getter\",\n  [3]: \"watcher callback\",\n  [4]: \"watcher cleanup function\",\n  [5]: \"native event handler\",\n  [6]: \"component event handler\",\n  [7]: \"vnode hook\",\n  [8]: \"directive hook\",\n  [9]: \"transition hook\",\n  [10]: \"app errorHandler\",\n  [11]: \"app warnHandler\",\n  [12]: \"ref function\",\n  [13]: \"async component loader\",\n  [14]: \"scheduler flush\",\n  [15]: \"component update\",\n  [16]: \"app unmount cleanup function\"\n};\nfunction callWithErrorHandling(fn, instance, type, args) {\n  try {\n    return args ? fn(...args) : fn();\n  } catch (err) {\n    handleError(err, instance, type);\n  }\n}\nfunction handleError(err, instance, type, throwInDev = true) {\n  const contextVNode = instance ? instance.vnode : null;\n  const { errorHandler, throwUnhandledErrorInProduction } = instance && instance.appContext.config || EMPTY_OBJ;\n  if (instance) {\n    let cur = instance.parent;\n    const exposedInstance = instance.proxy;\n    const errorInfo = !!(process.env.NODE_ENV !== \"production\") ? ErrorTypeStrings[type] : `https://vuejs.org/error-reference/#runtime-${type}`;\n    while (cur) {\n      const errorCapturedHooks = cur.ec;\n      if (errorCapturedHooks) {\n        for (let i = 0; i < errorCapturedHooks.length; i++) {\n          if (errorCapturedHooks[i](err, exposedInstance, errorInfo) === false) {\n            return;\n          }\n        }\n      }\n      cur = cur.parent;\n    }\n    if (errorHandler) {\n      pauseTracking();\n      callWithErrorHandling(errorHandler, null, 10, [\n        err,\n        exposedInstance,\n        errorInfo\n      ]);\n      resetTracking();\n      return;\n    }\n  }\n  logError(err, type, contextVNode, throwInDev, throwUnhandledErrorInProduction);\n}\nfunction logError(err, type, contextVNode, throwInDev = true, throwInProd = false) {\n  if (!!(process.env.NODE_ENV !== \"production\")) {\n    const info = ErrorTypeStrings[type];\n    if (contextVNode) {\n      pushWarningContext$1(contextVNode);\n    }\n    warn$1(`Unhandled error${info ? ` during execution of ${info}` : ``}`);\n    if (contextVNode) {\n      popWarningContext$1();\n    }\n    if (throwInDev) {\n      throw err;\n    } else {\n      console.error(err);\n    }\n  } else if (throwInProd) {\n    throw err;\n  } else {\n    console.error(err);\n  }\n}\n\nlet devtools;\nlet buffer = [];\nfunction setDevtoolsHook(hook, target) {\n  var _a, _b;\n  devtools = hook;\n  if (devtools) {\n    devtools.enabled = true;\n    buffer.forEach(({ event, args }) => devtools.emit(event, ...args));\n    buffer = [];\n  } else if (\n    // handle late devtools injection - only do this if we are in an actual\n    // browser environment to avoid the timer handle stalling test runner exit\n    // (#4815)\n    typeof window !== \"undefined\" && // some envs mock window but not fully\n    window.HTMLElement && // also exclude jsdom\n    // eslint-disable-next-line no-restricted-syntax\n    !((_b = (_a = window.navigator) == null ? void 0 : _a.userAgent) == null ? void 0 : _b.includes(\"jsdom\"))\n  ) {\n    const replay = target.__VUE_DEVTOOLS_HOOK_REPLAY__ = target.__VUE_DEVTOOLS_HOOK_REPLAY__ || [];\n    replay.push((newHook) => {\n      setDevtoolsHook(newHook, target);\n    });\n    setTimeout(() => {\n      if (!devtools) {\n        target.__VUE_DEVTOOLS_HOOK_REPLAY__ = null;\n        buffer = [];\n      }\n    }, 3e3);\n  } else {\n    buffer = [];\n  }\n}\n\n{\n  const g = getGlobalThis();\n  const registerGlobalSetter = (key, setter) => {\n    let setters;\n    if (!(setters = g[key])) setters = g[key] = [];\n    setters.push(setter);\n    return (v) => {\n      if (setters.length > 1) setters.forEach((set) => set(v));\n      else setters[0](v);\n    };\n  };\n  registerGlobalSetter(\n    `__VUE_INSTANCE_SETTERS__`,\n    (v) => v\n  );\n  registerGlobalSetter(\n    `__VUE_SSR_SETTERS__`,\n    (v) => v\n  );\n}\n!!(process.env.NODE_ENV !== \"production\") ? {\n  get(target, key) {\n    track(target, \"get\", \"\");\n    return target[key];\n  },\n  set() {\n    warn$1(`setupContext.attrs is readonly.`);\n    return false;\n  },\n  deleteProperty() {\n    warn$1(`setupContext.attrs is readonly.`);\n    return false;\n  }\n} : {\n  get(target, key) {\n    track(target, \"get\", \"\");\n    return target[key];\n  }\n};\nconst classifyRE = /(?:^|[-_])(\\w)/g;\nconst classify = (str) => str.replace(classifyRE, (c) => c.toUpperCase()).replace(/[-_]/g, \"\");\nfunction getComponentName(Component, includeInferred = true) {\n  return isFunction(Component) ? Component.displayName || Component.name : Component.name || includeInferred && Component.__name;\n}\nfunction formatComponentName(instance, Component, isRoot = false) {\n  let name = getComponentName(Component);\n  if (!name && Component.__file) {\n    const match = Component.__file.match(/([^/\\\\]+)\\.\\w+$/);\n    if (match) {\n      name = match[1];\n    }\n  }\n  if (!name && instance && instance.parent) {\n    const inferFromRegistry = (registry) => {\n      for (const key in registry) {\n        if (registry[key] === Component) {\n          return key;\n        }\n      }\n    };\n    name = inferFromRegistry(\n      instance.components || instance.parent.type.components\n    ) || inferFromRegistry(instance.appContext.components);\n  }\n  return name ? classify(name) : isRoot ? `App` : `Anonymous`;\n}\nfunction isClassComponent(value) {\n  return isFunction(value) && \"__vccOpts\" in value;\n}\n\nconst warn = !!(process.env.NODE_ENV !== \"production\") ? warn$1 : NOOP;\n!!(process.env.NODE_ENV !== \"production\") || true ? devtools : void 0;\n!!(process.env.NODE_ENV !== \"production\") || true ? setDevtoolsHook : NOOP;\n\nfunction ssrRenderList(source, renderItem) {\n  if (isArray(source) || isString(source)) {\n    for (let i = 0, l = source.length; i < l; i++) {\n      renderItem(source[i], i);\n    }\n  } else if (typeof source === \"number\") {\n    if (!!(process.env.NODE_ENV !== \"production\") && !Number.isInteger(source)) {\n      warn(`The v-for range expect an integer value but got ${source}.`);\n      return;\n    }\n    for (let i = 0; i < source; i++) {\n      renderItem(i + 1, i);\n    }\n  } else if (isObject(source)) {\n    if (source[Symbol.iterator]) {\n      const arr = Array.from(source);\n      for (let i = 0, l = arr.length; i < l; i++) {\n        renderItem(arr[i], i);\n      }\n    } else {\n      const keys = Object.keys(source);\n      for (let i = 0, l = keys.length; i < l; i++) {\n        const key = keys[i];\n        renderItem(source[key], key, i);\n      }\n    }\n  }\n}\n\nasync function ssrRenderSuspense(push, { default: renderContent }) {\n  if (renderContent) {\n    renderContent();\n  } else {\n    push(`<!---->`);\n  }\n}\n\nfunction ssrGetDirectiveProps(instance, dir, value, arg, modifiers = {}) {\n  if (typeof dir !== \"function\" && dir.getSSRProps) {\n    return dir.getSSRProps(\n      {\n        dir,\n        instance: ssrUtils.getComponentPublicInstance(instance.$),\n        value,\n        oldValue: void 0,\n        arg,\n        modifiers\n      },\n      null\n    ) || {};\n  }\n  return {};\n}\n\nconst ssrLooseEqual = looseEqual;\nfunction ssrLooseContain(arr, value) {\n  return looseIndexOf(arr, value) > -1;\n}\nfunction ssrRenderDynamicModel(type, model, value) {\n  switch (type) {\n    case \"radio\":\n      return looseEqual(model, value) ? \" checked\" : \"\";\n    case \"checkbox\":\n      return (isArray(model) ? ssrLooseContain(model, value) : model) ? \" checked\" : \"\";\n    default:\n      return ssrRenderAttr(\"value\", model);\n  }\n}\nfunction ssrGetDynamicModelProps(existingProps = {}, model) {\n  const { type, value } = existingProps;\n  switch (type) {\n    case \"radio\":\n      return looseEqual(model, value) ? { checked: true } : null;\n    case \"checkbox\":\n      return (isArray(model) ? ssrLooseContain(model, value) : model) ? { checked: true } : null;\n    default:\n      return { value: model };\n  }\n}\n\nfunction ssrCompile(template, instance) {\n  {\n    throw new Error(\n      `On-the-fly template compilation is not supported in the ESM build of @vue/server-renderer. All templates must be pre-compiled into render functions.`\n    );\n  }\n}\n\nconst {\n  createComponentInstance,\n  setCurrentRenderingInstance,\n  setupComponent,\n  renderComponentRoot,\n  normalizeVNode,\n  pushWarningContext,\n  popWarningContext\n} = ssrUtils;\nfunction createBuffer() {\n  let appendable = false;\n  const buffer = [];\n  return {\n    getBuffer() {\n      return buffer;\n    },\n    push(item) {\n      const isStringItem = isString(item);\n      if (appendable && isStringItem) {\n        buffer[buffer.length - 1] += item;\n        return;\n      }\n      buffer.push(item);\n      appendable = isStringItem;\n      if (isPromise(item) || isArray(item) && item.hasAsync) {\n        buffer.hasAsync = true;\n      }\n    }\n  };\n}\nfunction renderComponentVNode(vnode, parentComponent = null, slotScopeId) {\n  const instance = vnode.component = createComponentInstance(\n    vnode,\n    parentComponent,\n    null\n  );\n  if (!!(process.env.NODE_ENV !== \"production\")) pushWarningContext(vnode);\n  const res = setupComponent(\n    instance,\n    true\n    /* isSSR */\n  );\n  if (!!(process.env.NODE_ENV !== \"production\")) popWarningContext();\n  const hasAsyncSetup = isPromise(res);\n  let prefetches = instance.sp;\n  if (hasAsyncSetup || prefetches) {\n    const p = Promise.resolve(res).then(() => {\n      if (hasAsyncSetup) prefetches = instance.sp;\n      if (prefetches) {\n        return Promise.all(\n          prefetches.map((prefetch) => prefetch.call(instance.proxy))\n        );\n      }\n    }).catch(NOOP);\n    return p.then(() => renderComponentSubTree(instance, slotScopeId));\n  } else {\n    return renderComponentSubTree(instance, slotScopeId);\n  }\n}\nfunction renderComponentSubTree(instance, slotScopeId) {\n  if (!!(process.env.NODE_ENV !== \"production\")) pushWarningContext(instance.vnode);\n  const comp = instance.type;\n  const { getBuffer, push } = createBuffer();\n  if (isFunction(comp)) {\n    let root = renderComponentRoot(instance);\n    if (!comp.props) {\n      for (const key in instance.attrs) {\n        if (key.startsWith(`data-v-`)) {\n          (root.props || (root.props = {}))[key] = ``;\n        }\n      }\n    }\n    renderVNode(push, instance.subTree = root, instance, slotScopeId);\n  } else {\n    if ((!instance.render || instance.render === NOOP) && !instance.ssrRender && !comp.ssrRender && isString(comp.template)) {\n      comp.ssrRender = ssrCompile(comp.template);\n    }\n    const ssrRender = instance.ssrRender || comp.ssrRender;\n    if (ssrRender) {\n      let attrs = instance.inheritAttrs !== false ? instance.attrs : void 0;\n      let hasCloned = false;\n      let cur = instance;\n      while (true) {\n        const scopeId = cur.vnode.scopeId;\n        if (scopeId) {\n          if (!hasCloned) {\n            attrs = { ...attrs };\n            hasCloned = true;\n          }\n          attrs[scopeId] = \"\";\n        }\n        const parent = cur.parent;\n        if (parent && parent.subTree && parent.subTree === cur.vnode) {\n          cur = parent;\n        } else {\n          break;\n        }\n      }\n      if (slotScopeId) {\n        if (!hasCloned) attrs = { ...attrs };\n        const slotScopeIdList = slotScopeId.trim().split(\" \");\n        for (let i = 0; i < slotScopeIdList.length; i++) {\n          attrs[slotScopeIdList[i]] = \"\";\n        }\n      }\n      const prev = setCurrentRenderingInstance(instance);\n      try {\n        ssrRender(\n          instance.proxy,\n          push,\n          instance,\n          attrs,\n          // compiler-optimized bindings\n          instance.props,\n          instance.setupState,\n          instance.data,\n          instance.ctx\n        );\n      } finally {\n        setCurrentRenderingInstance(prev);\n      }\n    } else if (instance.render && instance.render !== NOOP) {\n      renderVNode(\n        push,\n        instance.subTree = renderComponentRoot(instance),\n        instance,\n        slotScopeId\n      );\n    } else {\n      const componentName = comp.name || comp.__file || `<Anonymous>`;\n      warn$2(`Component ${componentName} is missing template or render function.`);\n      push(`<!---->`);\n    }\n  }\n  if (!!(process.env.NODE_ENV !== \"production\")) popWarningContext();\n  return getBuffer();\n}\nfunction renderVNode(push, vnode, parentComponent, slotScopeId) {\n  const { type, shapeFlag, children, dirs, props } = vnode;\n  if (dirs) {\n    vnode.props = applySSRDirectives(vnode, props, dirs);\n  }\n  switch (type) {\n    case Text:\n      push(escapeHtml(children));\n      break;\n    case Comment:\n      push(\n        children ? `<!--${escapeHtmlComment(children)}-->` : `<!---->`\n      );\n      break;\n    case Static:\n      push(children);\n      break;\n    case Fragment:\n      if (vnode.slotScopeIds) {\n        slotScopeId = (slotScopeId ? slotScopeId + \" \" : \"\") + vnode.slotScopeIds.join(\" \");\n      }\n      push(`<!--[-->`);\n      renderVNodeChildren(\n        push,\n        children,\n        parentComponent,\n        slotScopeId\n      );\n      push(`<!--]-->`);\n      break;\n    default:\n      if (shapeFlag & 1) {\n        renderElementVNode(push, vnode, parentComponent, slotScopeId);\n      } else if (shapeFlag & 6) {\n        push(renderComponentVNode(vnode, parentComponent, slotScopeId));\n      } else if (shapeFlag & 64) {\n        renderTeleportVNode(push, vnode, parentComponent, slotScopeId);\n      } else if (shapeFlag & 128) {\n        renderVNode(push, vnode.ssContent, parentComponent, slotScopeId);\n      } else {\n        warn$2(\n          \"[@vue/server-renderer] Invalid VNode type:\",\n          type,\n          `(${typeof type})`\n        );\n      }\n  }\n}\nfunction renderVNodeChildren(push, children, parentComponent, slotScopeId) {\n  for (let i = 0; i < children.length; i++) {\n    renderVNode(push, normalizeVNode(children[i]), parentComponent, slotScopeId);\n  }\n}\nfunction renderElementVNode(push, vnode, parentComponent, slotScopeId) {\n  const tag = vnode.type;\n  let { props, children, shapeFlag, scopeId } = vnode;\n  let openTag = `<${tag}`;\n  if (props) {\n    openTag += ssrRenderAttrs(props, tag);\n  }\n  if (scopeId) {\n    openTag += ` ${scopeId}`;\n  }\n  let curParent = parentComponent;\n  let curVnode = vnode;\n  while (curParent && curVnode === curParent.subTree) {\n    curVnode = curParent.vnode;\n    if (curVnode.scopeId) {\n      openTag += ` ${curVnode.scopeId}`;\n    }\n    curParent = curParent.parent;\n  }\n  if (slotScopeId) {\n    openTag += ` ${slotScopeId}`;\n  }\n  push(openTag + `>`);\n  if (!isVoidTag(tag)) {\n    let hasChildrenOverride = false;\n    if (props) {\n      if (props.innerHTML) {\n        hasChildrenOverride = true;\n        push(props.innerHTML);\n      } else if (props.textContent) {\n        hasChildrenOverride = true;\n        push(escapeHtml(props.textContent));\n      } else if (tag === \"textarea\" && props.value) {\n        hasChildrenOverride = true;\n        push(escapeHtml(props.value));\n      }\n    }\n    if (!hasChildrenOverride) {\n      if (shapeFlag & 8) {\n        push(escapeHtml(children));\n      } else if (shapeFlag & 16) {\n        renderVNodeChildren(\n          push,\n          children,\n          parentComponent,\n          slotScopeId\n        );\n      }\n    }\n    push(`</${tag}>`);\n  }\n}\nfunction applySSRDirectives(vnode, rawProps, dirs) {\n  const toMerge = [];\n  for (let i = 0; i < dirs.length; i++) {\n    const binding = dirs[i];\n    const {\n      dir: { getSSRProps }\n    } = binding;\n    if (getSSRProps) {\n      const props = getSSRProps(binding, vnode);\n      if (props) toMerge.push(props);\n    }\n  }\n  return mergeProps(rawProps || {}, ...toMerge);\n}\nfunction renderTeleportVNode(push, vnode, parentComponent, slotScopeId) {\n  const target = vnode.props && vnode.props.to;\n  const disabled = vnode.props && vnode.props.disabled;\n  if (!target) {\n    if (!disabled) {\n      warn$2(`[@vue/server-renderer] Teleport is missing target prop.`);\n    }\n    return [];\n  }\n  if (!isString(target)) {\n    warn$2(\n      `[@vue/server-renderer] Teleport target must be a query selector string.`\n    );\n    return [];\n  }\n  ssrRenderTeleport(\n    push,\n    (push2) => {\n      renderVNodeChildren(\n        push2,\n        vnode.children,\n        parentComponent,\n        slotScopeId\n      );\n    },\n    target,\n    disabled || disabled === \"\",\n    parentComponent\n  );\n}\n\nconst { isVNode: isVNode$1 } = ssrUtils;\nfunction nestedUnrollBuffer(buffer, parentRet, startIndex) {\n  if (!buffer.hasAsync) {\n    return parentRet + unrollBufferSync$1(buffer);\n  }\n  let ret = parentRet;\n  for (let i = startIndex; i < buffer.length; i += 1) {\n    const item = buffer[i];\n    if (isString(item)) {\n      ret += item;\n      continue;\n    }\n    if (isPromise(item)) {\n      return item.then((nestedItem) => {\n        buffer[i] = nestedItem;\n        return nestedUnrollBuffer(buffer, ret, i);\n      });\n    }\n    const result = nestedUnrollBuffer(item, ret, 0);\n    if (isPromise(result)) {\n      return result.then((nestedItem) => {\n        buffer[i] = nestedItem;\n        return nestedUnrollBuffer(buffer, \"\", i);\n      });\n    }\n    ret = result;\n  }\n  return ret;\n}\nfunction unrollBuffer$1(buffer) {\n  return nestedUnrollBuffer(buffer, \"\", 0);\n}\nfunction unrollBufferSync$1(buffer) {\n  let ret = \"\";\n  for (let i = 0; i < buffer.length; i++) {\n    let item = buffer[i];\n    if (isString(item)) {\n      ret += item;\n    } else {\n      ret += unrollBufferSync$1(item);\n    }\n  }\n  return ret;\n}\nasync function renderToString(input, context = {}) {\n  if (isVNode$1(input)) {\n    return renderToString(createApp({ render: () => input }), context);\n  }\n  const vnode = createVNode(input._component, input._props);\n  vnode.appContext = input._context;\n  input.provide(ssrContextKey, context);\n  const buffer = await renderComponentVNode(vnode);\n  const result = await unrollBuffer$1(buffer);\n  await resolveTeleports(context);\n  if (context.__watcherHandles) {\n    for (const unwatch of context.__watcherHandles) {\n      unwatch();\n    }\n  }\n  return result;\n}\nasync function resolveTeleports(context) {\n  if (context.__teleportBuffers) {\n    context.teleports = context.teleports || {};\n    for (const key in context.__teleportBuffers) {\n      context.teleports[key] = await unrollBuffer$1(\n        await Promise.all([context.__teleportBuffers[key]])\n      );\n    }\n  }\n}\n\nconst { isVNode } = ssrUtils;\nasync function unrollBuffer(buffer, stream) {\n  if (buffer.hasAsync) {\n    for (let i = 0; i < buffer.length; i++) {\n      let item = buffer[i];\n      if (isPromise(item)) {\n        item = await item;\n      }\n      if (isString(item)) {\n        stream.push(item);\n      } else {\n        await unrollBuffer(item, stream);\n      }\n    }\n  } else {\n    unrollBufferSync(buffer, stream);\n  }\n}\nfunction unrollBufferSync(buffer, stream) {\n  for (let i = 0; i < buffer.length; i++) {\n    let item = buffer[i];\n    if (isString(item)) {\n      stream.push(item);\n    } else {\n      unrollBufferSync(item, stream);\n    }\n  }\n}\nfunction renderToSimpleStream(input, context, stream) {\n  if (isVNode(input)) {\n    return renderToSimpleStream(\n      createApp({ render: () => input }),\n      context,\n      stream\n    );\n  }\n  const vnode = createVNode(input._component, input._props);\n  vnode.appContext = input._context;\n  input.provide(ssrContextKey, context);\n  Promise.resolve(renderComponentVNode(vnode)).then((buffer) => unrollBuffer(buffer, stream)).then(() => resolveTeleports(context)).then(() => {\n    if (context.__watcherHandles) {\n      for (const unwatch of context.__watcherHandles) {\n        unwatch();\n      }\n    }\n  }).then(() => stream.push(null)).catch((error) => {\n    stream.destroy(error);\n  });\n  return stream;\n}\nfunction renderToStream(input, context = {}) {\n  console.warn(\n    `[@vue/server-renderer] renderToStream is deprecated - use renderToNodeStream instead.`\n  );\n  return renderToNodeStream(input, context);\n}\nfunction renderToNodeStream(input, context = {}) {\n  {\n    throw new Error(\n      `ESM build of renderToStream() does not support renderToNodeStream(). Use pipeToNodeWritable() with an existing Node.js Writable stream instance instead.`\n    );\n  }\n}\nfunction pipeToNodeWritable(input, context = {}, writable) {\n  renderToSimpleStream(input, context, {\n    push(content) {\n      if (content != null) {\n        writable.write(content);\n      } else {\n        writable.end();\n      }\n    },\n    destroy(err) {\n      writable.destroy(err);\n    }\n  });\n}\nfunction renderToWebStream(input, context = {}) {\n  if (typeof ReadableStream !== \"function\") {\n    throw new Error(\n      `ReadableStream constructor is not available in the global scope. If the target environment does support web streams, consider using pipeToWebWritable() with an existing WritableStream instance instead.`\n    );\n  }\n  const encoder = new TextEncoder();\n  let cancelled = false;\n  return new ReadableStream({\n    start(controller) {\n      renderToSimpleStream(input, context, {\n        push(content) {\n          if (cancelled) return;\n          if (content != null) {\n            controller.enqueue(encoder.encode(content));\n          } else {\n            controller.close();\n          }\n        },\n        destroy(err) {\n          controller.error(err);\n        }\n      });\n    },\n    cancel() {\n      cancelled = true;\n    }\n  });\n}\nfunction pipeToWebWritable(input, context = {}, writable) {\n  const writer = writable.getWriter();\n  const encoder = new TextEncoder();\n  let hasReady = false;\n  try {\n    hasReady = isPromise(writer.ready);\n  } catch (e) {\n  }\n  renderToSimpleStream(input, context, {\n    async push(content) {\n      if (hasReady) {\n        await writer.ready;\n      }\n      if (content != null) {\n        return writer.write(encoder.encode(content));\n      } else {\n        return writer.close();\n      }\n    },\n    destroy(err) {\n      console.log(err);\n      writer.close();\n    }\n  });\n}\n\ninitDirectivesForSSR();\n\nexport { pipeToNodeWritable, pipeToWebWritable, renderToNodeStream, renderToSimpleStream, renderToStream, renderToString, renderToWebStream, ssrGetDirectiveProps, ssrGetDynamicModelProps, ssrInterpolate, ssrLooseContain, ssrLooseEqual, ssrRenderAttr, ssrRenderAttrs, ssrRenderClass, ssrRenderComponent, ssrRenderDynamicAttr, ssrRenderDynamicModel, ssrRenderList, ssrRenderSlot, ssrRenderSlotInner, ssrRenderStyle, ssrRenderSuspense, ssrRenderTeleport, renderVNode as ssrRenderVNode };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,mBAAmC;AAAA,EACvC;AACF;AACA,SAAS,eAAe,OAAO,KAAK;AAClC,MAAI,MAAM;AACV,aAAW,OAAO,OAAO;AACvB,QAAI,iBAAiB,GAAG,KAAK,KAAK,GAAG,KAAK,QAAQ,cAAc,QAAQ,SAAS;AAC/E;AAAA,IACF;AACA,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,QAAQ,SAAS;AACnB,aAAO,WAAW,eAAe,KAAK,CAAC;AAAA,IACzC,WAAW,QAAQ,SAAS;AAC1B,aAAO,WAAW,eAAe,KAAK,CAAC;AAAA,IACzC,WAAW,QAAQ,aAAa;AAC9B,aAAO,WAAW,OAAO,KAAK,CAAC;AAAA,IACjC,OAAO;AACL,aAAO,qBAAqB,KAAK,OAAO,GAAG;AAAA,IAC7C;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,KAAK,OAAO,KAAK;AAC7C,MAAI,CAAC,sBAAsB,KAAK,GAAG;AACjC,WAAO;AAAA,EACT;AACA,QAAM,UAAU,QAAQ,IAAI,QAAQ,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK,MAAM,eAAe,GAAG,KAAK,IAAI,YAAY;AAC9G,MAAI,cAAc,OAAO,GAAG;AAC1B,WAAO,mBAAmB,KAAK,IAAI,IAAI,OAAO,KAAK;AAAA,EACrD,WAAW,kBAAkB,OAAO,GAAG;AACrC,WAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,WAAW,KAAK,CAAC;AAAA,EACzE,OAAO;AACL,YAAQ;AAAA,MACN,mEAAmE,OAAO;AAAA,IAC5E;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,cAAc,KAAK,OAAO;AACjC,MAAI,CAAC,sBAAsB,KAAK,GAAG;AACjC,WAAO;AAAA,EACT;AACA,SAAO,IAAI,GAAG,KAAK,WAAW,KAAK,CAAC;AACtC;AACA,SAAS,eAAe,KAAK;AAC3B,SAAO,WAAW,eAAe,GAAG,CAAC;AACvC;AACA,SAAS,eAAe,KAAK;AAC3B,MAAI,CAAC,KAAK;AACR,WAAO;AAAA,EACT;AACA,MAAI,SAAS,GAAG,GAAG;AACjB,WAAO,WAAW,GAAG;AAAA,EACvB;AACA,QAAM,SAAS,eAAe,GAAG;AACjC,SAAO,WAAW,eAAe,MAAM,CAAC;AAC1C;AAEA,SAAS,mBAAmB,MAAM,QAAQ,MAAM,WAAW,MAAM,kBAAkB,MAAM,aAAa;AACpG,SAAO;AAAA,IACL,YAAY,MAAM,OAAO,QAAQ;AAAA,IACjC;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,EAAE,iBAAiB,IAAI;AAC7B,SAAS,cAAc,OAAO,UAAU,WAAW,kBAAkB,MAAM,iBAAiB,aAAa;AACvG,OAAK,UAAU;AACf;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,OAAK,UAAU;AACjB;AACA,SAAS,mBAAmB,OAAO,UAAU,WAAW,kBAAkB,MAAM,iBAAiB,aAAa,YAAY;AACxH,QAAM,SAAS,MAAM,QAAQ;AAC7B,MAAI,QAAQ;AACV,UAAM,aAAa,CAAC;AACpB,UAAM,eAAe,CAAC,SAAS;AAC7B,iBAAW,KAAK,IAAI;AAAA,IACtB;AACA,UAAM,MAAM;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,MAAM,cAAc;AAAA,IACpC;AACA,QAAI,QAAQ,GAAG,GAAG;AAChB,YAAM,mBAAmB,iBAAiB,GAAG;AAC7C,UAAI,kBAAkB;AACpB;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF,WAAW,kBAAkB;AAC3B,yBAAiB;AAAA,MACnB;AAAA,IACF,OAAO;AACL,UAAI,cAAc;AAClB,UAAI,YAAY;AACd,sBAAc;AAAA,MAChB,OAAO;AACL,iBAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,cAAI,CAAC,UAAU,WAAW,CAAC,CAAC,GAAG;AAC7B,0BAAc;AACd;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,aAAa;AACf,YAAI,kBAAkB;AACpB,2BAAiB;AAAA,QACnB;AAAA,MACF,OAAO;AACL,YAAI,QAAQ;AACZ,YAAI,MAAM,WAAW;AACrB,YAAI,cAAc,WAAW,CAAC,MAAM,cAAc,WAAW,MAAM,CAAC,MAAM,YAAY;AACpF;AACA;AAAA,QACF;AACA,iBAAS,IAAI,OAAO,IAAI,KAAK,KAAK;AAChC,eAAK,WAAW,CAAC,CAAC;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAW,kBAAkB;AAC3B,qBAAiB;AAAA,EACnB;AACF;AACA,IAAM,gBAAgB;AACtB,IAAM,YAAY;AAClB,SAAS,UAAU,MAAM;AACvB,MAAI,OAAO,SAAS,YAAY,CAAC,cAAc,KAAK,IAAI;AAAG,WAAO;AAClE,MAAI,KAAK,UAAU;AAAG,WAAO;AAC7B,SAAO,CAAC,KAAK,QAAQ,WAAW,EAAE,EAAE,KAAK;AAC3C;AAEA,SAAS,kBAAkB,YAAY,iBAAiB,QAAQ,UAAU,iBAAiB;AACzF,aAAW,uBAAuB;AAClC,QAAM,UAAU,gBAAgB,WAAW,SAAS,aAAa;AACjE,QAAM,kBAAkB,QAAQ,sBAAsB,QAAQ,oBAAoB,CAAC;AACnF,QAAM,eAAe,gBAAgB,MAAM,MAAM,gBAAgB,MAAM,IAAI,CAAC;AAC5E,QAAM,cAAc,aAAa;AACjC,MAAI;AACJ,MAAI,UAAU;AACZ,oBAAgB,UAAU;AAC1B,sBAAkB;AAAA,EACpB,OAAO;AACL,UAAM,EAAE,WAAW,KAAK,IAAI,aAAa;AACzC,SAAK,8BAA8B;AACnC,oBAAgB,IAAI;AACpB,SAAK,wBAAwB;AAC7B,sBAAkB,UAAU;AAAA,EAC9B;AACA,eAAa,OAAO,aAAa,GAAG,eAAe;AACnD,aAAW,qBAAqB;AAClC;AAEA,SAAS,eAAe,OAAO;AAC7B,SAAO,WAAW,gBAAgB,KAAK,CAAC;AAC1C;AAiCA,IAAI,cAAc;AAClB,IAAM,aAAa,CAAC;AACpB,SAAS,gBAAgB;AACvB,aAAW,KAAK,WAAW;AAC3B,gBAAc;AAChB;AACA,SAAS,gBAAgB;AACvB,QAAM,OAAO,WAAW,IAAI;AAC5B,gBAAc,SAAS,SAAS,OAAO;AACzC;AAiEA;AAAA,EACE,OAA4C,mBAAmB;AACjE;AACA;AAAA,EACE,OAA4C,qBAAqB;AACnE;AACA;AAAA,EACE,OAA4C,kBAAkB;AAChE;AA4BA,SAAS,MAAM,UAAU;AACvB,QAAM,MAAM,YAAY,SAAS,SAAS;AAC1C,SAAO,MAAM,MAAM,GAAG,IAAI;AAC5B;AAEA,SAAS,MAAM,GAAG;AAChB,SAAO,IAAI,EAAE,WAAW,MAAM,OAAO;AACvC;AAEA,IAAM,QAAQ,CAAC;AACf,SAAS,qBAAqB,OAAO;AACnC,QAAM,KAAK,KAAK;AAClB;AACA,SAAS,sBAAsB;AAC7B,QAAM,IAAI;AACZ;AACA,IAAI,YAAY;AAChB,SAAS,OAAO,QAAQ,MAAM;AAC5B,MAAI;AAAW;AACf,cAAY;AACZ,gBAAc;AACd,QAAM,WAAW,MAAM,SAAS,MAAM,MAAM,SAAS,CAAC,EAAE,YAAY;AACpE,QAAM,iBAAiB,YAAY,SAAS,WAAW,OAAO;AAC9D,QAAM,QAAQ,kBAAkB;AAChC,MAAI,gBAAgB;AAClB;AAAA,MACE;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,QAEE,MAAM,KAAK,IAAI,CAAC,MAAM;AACpB,cAAI,IAAI;AACR,kBAAQ,MAAM,KAAK,EAAE,aAAa,OAAO,SAAS,GAAG,KAAK,CAAC,MAAM,OAAO,KAAK,KAAK,UAAU,CAAC;AAAA,QAC/F,CAAC,EAAE,KAAK,EAAE;AAAA,QACV,YAAY,SAAS;AAAA,QACrB,MAAM;AAAA,UACJ,CAAC,EAAE,MAAM,MAAM,OAAO,oBAAoB,UAAU,MAAM,IAAI,CAAC;AAAA,QACjE,EAAE,KAAK,IAAI;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,WAAW,CAAC,eAAe,GAAG,IAAI,GAAG,IAAI;AAC/C,QAAI,MAAM;AAAA,IACV,MAAM;AACJ,eAAS,KAAK;AAAA,GACjB,GAAG,YAAY,KAAK,CAAC;AAAA,IACpB;AACA,YAAQ,KAAK,GAAG,QAAQ;AAAA,EAC1B;AACA,gBAAc;AACd,cAAY;AACd;AACA,SAAS,oBAAoB;AAC3B,MAAI,eAAe,MAAM,MAAM,SAAS,CAAC;AACzC,MAAI,CAAC,cAAc;AACjB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,kBAAkB,CAAC;AACzB,SAAO,cAAc;AACnB,UAAM,OAAO,gBAAgB,CAAC;AAC9B,QAAI,QAAQ,KAAK,UAAU,cAAc;AACvC,WAAK;AAAA,IACP,OAAO;AACL,sBAAgB,KAAK;AAAA,QACnB,OAAO;AAAA,QACP,cAAc;AAAA,MAChB,CAAC;AAAA,IACH;AACA,UAAM,iBAAiB,aAAa,aAAa,aAAa,UAAU;AACxE,mBAAe,kBAAkB,eAAe;AAAA,EAClD;AACA,SAAO;AACT;AACA,SAAS,YAAY,OAAO;AAC1B,QAAM,OAAO,CAAC;AACd,QAAM,QAAQ,CAAC,OAAO,MAAM;AAC1B,SAAK,KAAK,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC;AAAA,CAChC,GAAG,GAAG,iBAAiB,KAAK,CAAC;AAAA,EAC5B,CAAC;AACD,SAAO;AACT;AACA,SAAS,iBAAiB,EAAE,OAAO,aAAa,GAAG;AACjD,QAAM,UAAU,eAAe,IAAI,QAAQ,YAAY,sBAAsB;AAC7E,QAAM,SAAS,MAAM,YAAY,MAAM,UAAU,UAAU,OAAO;AAClE,QAAM,OAAO,QAAQ;AAAA,IACnB,MAAM;AAAA,IACN,MAAM;AAAA,IACN;AAAA,EACF,CAAC;AACD,QAAM,QAAQ,MAAM;AACpB,SAAO,MAAM,QAAQ,CAAC,MAAM,GAAG,YAAY,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,OAAO,KAAK;AACjF;AACA,SAAS,YAAY,OAAO;AAC1B,QAAM,MAAM,CAAC;AACb,QAAM,OAAO,OAAO,KAAK,KAAK;AAC9B,OAAK,MAAM,GAAG,CAAC,EAAE,QAAQ,CAAC,QAAQ;AAChC,QAAI,KAAK,GAAG,WAAW,KAAK,MAAM,GAAG,CAAC,CAAC;AAAA,EACzC,CAAC;AACD,MAAI,KAAK,SAAS,GAAG;AACnB,QAAI,KAAK,MAAM;AAAA,EACjB;AACA,SAAO;AACT;AACA,SAAS,WAAW,KAAK,OAAO,KAAK;AACnC,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,KAAK,UAAU,KAAK;AAC5B,WAAO,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE;AAAA,EACzC,WAAW,OAAO,UAAU,YAAY,OAAO,UAAU,aAAa,SAAS,MAAM;AACnF,WAAO,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE;AAAA,EACzC,WAAW,MAAM,KAAK,GAAG;AACvB,YAAQ,WAAW,KAAK,MAAM,MAAM,KAAK,GAAG,IAAI;AAChD,WAAO,MAAM,QAAQ,CAAC,GAAG,GAAG,SAAS,OAAO,GAAG;AAAA,EACjD,WAAW,WAAW,KAAK,GAAG;AAC5B,WAAO,CAAC,GAAG,GAAG,MAAM,MAAM,OAAO,IAAI,MAAM,IAAI,MAAM,EAAE,EAAE;AAAA,EAC3D,OAAO;AACL,YAAQ,MAAM,KAAK;AACnB,WAAO,MAAM,QAAQ,CAAC,GAAG,GAAG,KAAK,KAAK;AAAA,EACxC;AACF;AAEA,IAAM,mBAAmB;AAAA,EACvB,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,GAAG,GAAG;AAAA,EACP,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,GAAG,GAAG;AAAA,EACP,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,GAAG,GAAG;AAAA,EACP,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,GAAG,GAAG;AAAA,EACP,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,IAAI,GAAG;AAAA,EACR,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,KAAK,GAAG;AAAA,EACT,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,CAAC,GAAG;AAAA,EACL,CAAC,EAAE,GAAG;AAAA,EACN,CAAC,EAAE,GAAG;AAAA,EACN,CAAC,EAAE,GAAG;AAAA,EACN,CAAC,EAAE,GAAG;AAAA,EACN,CAAC,EAAE,GAAG;AAAA,EACN,CAAC,EAAE,GAAG;AAAA,EACN,CAAC,EAAE,GAAG;AACR;AACA,SAAS,sBAAsB,IAAI,UAAU,MAAM,MAAM;AACvD,MAAI;AACF,WAAO,OAAO,GAAG,GAAG,IAAI,IAAI,GAAG;AAAA,EACjC,SAAS,KAAK;AACZ,gBAAY,KAAK,UAAU,IAAI;AAAA,EACjC;AACF;AACA,SAAS,YAAY,KAAK,UAAU,MAAM,aAAa,MAAM;AAC3D,QAAM,eAAe,WAAW,SAAS,QAAQ;AACjD,QAAM,EAAE,cAAc,gCAAgC,IAAI,YAAY,SAAS,WAAW,UAAU;AACpG,MAAI,UAAU;AACZ,QAAI,MAAM,SAAS;AACnB,UAAM,kBAAkB,SAAS;AACjC,UAAM,YAAY,OAA4C,iBAAiB,IAAI,IAAI,8CAA8C,IAAI;AACzI,WAAO,KAAK;AACV,YAAM,qBAAqB,IAAI;AAC/B,UAAI,oBAAoB;AACtB,iBAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,cAAI,mBAAmB,CAAC,EAAE,KAAK,iBAAiB,SAAS,MAAM,OAAO;AACpE;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,YAAM,IAAI;AAAA,IACZ;AACA,QAAI,cAAc;AAChB,oBAAc;AACd,4BAAsB,cAAc,MAAM,IAAI;AAAA,QAC5C;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,oBAAc;AACd;AAAA,IACF;AAAA,EACF;AACA,WAAS,KAAK,MAAM,cAAc,YAAY,+BAA+B;AAC/E;AACA,SAAS,SAAS,KAAK,MAAM,cAAc,aAAa,MAAM,cAAc,OAAO;AACjF,MAAI,MAA2C;AAC7C,UAAM,OAAO,iBAAiB,IAAI;AAClC,QAAI,cAAc;AAChB,2BAAqB,YAAY;AAAA,IACnC;AACA,WAAO,kBAAkB,OAAO,wBAAwB,IAAI,KAAK,EAAE,EAAE;AACrE,QAAI,cAAc;AAChB,0BAAoB;AAAA,IACtB;AACA,QAAI,YAAY;AACd,YAAM;AAAA,IACR,OAAO;AACL,cAAQ,MAAM,GAAG;AAAA,IACnB;AAAA,EACF,WAAW,aAAa;AACtB,UAAM;AAAA,EACR,OAAO;AACL,YAAQ,MAAM,GAAG;AAAA,EACnB;AACF;AAmCA;AACE,QAAM,IAAI,cAAc;AACxB,QAAM,uBAAuB,CAAC,KAAK,WAAW;AAC5C,QAAI;AACJ,QAAI,EAAE,UAAU,EAAE,GAAG;AAAI,gBAAU,EAAE,GAAG,IAAI,CAAC;AAC7C,YAAQ,KAAK,MAAM;AACnB,WAAO,CAAC,MAAM;AACZ,UAAI,QAAQ,SAAS;AAAG,gBAAQ,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC;AAAA;AAClD,gBAAQ,CAAC,EAAE,CAAC;AAAA,IACnB;AAAA,EACF;AACA;AAAA,IACE;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AACA;AAAA,IACE;AAAA,IACA,CAAC,MAAM;AAAA,EACT;AACF;AAoBA,IAAM,aAAa;AACnB,IAAM,WAAW,CAAC,QAAQ,IAAI,QAAQ,YAAY,CAAC,MAAM,EAAE,YAAY,CAAC,EAAE,QAAQ,SAAS,EAAE;AAC7F,SAAS,iBAAiB,WAAW,kBAAkB,MAAM;AAC3D,SAAO,WAAW,SAAS,IAAI,UAAU,eAAe,UAAU,OAAO,UAAU,QAAQ,mBAAmB,UAAU;AAC1H;AACA,SAAS,oBAAoB,UAAU,WAAW,SAAS,OAAO;AAChE,MAAI,OAAO,iBAAiB,SAAS;AACrC,MAAI,CAAC,QAAQ,UAAU,QAAQ;AAC7B,UAAM,QAAQ,UAAU,OAAO,MAAM,iBAAiB;AACtD,QAAI,OAAO;AACT,aAAO,MAAM,CAAC;AAAA,IAChB;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,YAAY,SAAS,QAAQ;AACxC,UAAM,oBAAoB,CAAC,aAAa;AACtC,iBAAW,OAAO,UAAU;AAC1B,YAAI,SAAS,GAAG,MAAM,WAAW;AAC/B,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,SAAS,cAAc,SAAS,OAAO,KAAK;AAAA,IAC9C,KAAK,kBAAkB,SAAS,WAAW,UAAU;AAAA,EACvD;AACA,SAAO,OAAO,SAAS,IAAI,IAAI,SAAS,QAAQ;AAClD;AAKA,IAAMA,QAAO,OAA4C,SAAS;AAIlE,SAAS,cAAc,QAAQ,YAAY;AACzC,MAAI,QAAQ,MAAM,KAAK,SAAS,MAAM,GAAG;AACvC,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,iBAAW,OAAO,CAAC,GAAG,CAAC;AAAA,IACzB;AAAA,EACF,WAAW,OAAO,WAAW,UAAU;AACrC,QAAiD,CAAC,OAAO,UAAU,MAAM,GAAG;AAC1E,MAAAC,MAAK,mDAAmD,MAAM,GAAG;AACjE;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,iBAAW,IAAI,GAAG,CAAC;AAAA,IACrB;AAAA,EACF,WAAW,SAAS,MAAM,GAAG;AAC3B,QAAI,OAAO,OAAO,QAAQ,GAAG;AAC3B,YAAM,MAAM,MAAM,KAAK,MAAM;AAC7B,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,KAAK;AAC1C,mBAAW,IAAI,CAAC,GAAG,CAAC;AAAA,MACtB;AAAA,IACF,OAAO;AACL,YAAM,OAAO,OAAO,KAAK,MAAM;AAC/B,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AAC3C,cAAM,MAAM,KAAK,CAAC;AAClB,mBAAW,OAAO,GAAG,GAAG,KAAK,CAAC;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACF;AAEA,eAAe,kBAAkB,MAAM,EAAE,SAAS,cAAc,GAAG;AACjE,MAAI,eAAe;AACjB,kBAAc;AAAA,EAChB,OAAO;AACL,SAAK,SAAS;AAAA,EAChB;AACF;AAEA,SAAS,qBAAqB,UAAU,KAAK,OAAO,KAAK,YAAY,CAAC,GAAG;AACvE,MAAI,OAAO,QAAQ,cAAc,IAAI,aAAa;AAChD,WAAO,IAAI;AAAA,MACT;AAAA,QACE;AAAA,QACA,UAAU,SAAS,2BAA2B,SAAS,CAAC;AAAA,QACxD;AAAA,QACA,UAAU;AAAA,QACV;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF,KAAK,CAAC;AAAA,EACR;AACA,SAAO,CAAC;AACV;AAEA,IAAM,gBAAgB;AACtB,SAAS,gBAAgB,KAAK,OAAO;AACnC,SAAO,aAAa,KAAK,KAAK,IAAI;AACpC;AACA,SAAS,sBAAsB,MAAM,OAAO,OAAO;AACjD,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,WAAW,OAAO,KAAK,IAAI,aAAa;AAAA,IACjD,KAAK;AACH,cAAQ,QAAQ,KAAK,IAAI,gBAAgB,OAAO,KAAK,IAAI,SAAS,aAAa;AAAA,IACjF;AACE,aAAO,cAAc,SAAS,KAAK;AAAA,EACvC;AACF;AACA,SAAS,wBAAwB,gBAAgB,CAAC,GAAG,OAAO;AAC1D,QAAM,EAAE,MAAM,MAAM,IAAI;AACxB,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,aAAO,WAAW,OAAO,KAAK,IAAI,EAAE,SAAS,KAAK,IAAI;AAAA,IACxD,KAAK;AACH,cAAQ,QAAQ,KAAK,IAAI,gBAAgB,OAAO,KAAK,IAAI,SAAS,EAAE,SAAS,KAAK,IAAI;AAAA,IACxF;AACE,aAAO,EAAE,OAAO,MAAM;AAAA,EAC1B;AACF;AAEA,SAAS,WAAW,UAAU,UAAU;AACtC;AACE,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM;AAAA,EACJ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,IAAI;AACJ,SAAS,eAAe;AACtB,MAAI,aAAa;AACjB,QAAM,SAAS,CAAC;AAChB,SAAO;AAAA,IACL,YAAY;AACV,aAAO;AAAA,IACT;AAAA,IACA,KAAK,MAAM;AACT,YAAM,eAAe,SAAS,IAAI;AAClC,UAAI,cAAc,cAAc;AAC9B,eAAO,OAAO,SAAS,CAAC,KAAK;AAC7B;AAAA,MACF;AACA,aAAO,KAAK,IAAI;AAChB,mBAAa;AACb,UAAI,UAAU,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,UAAU;AACrD,eAAO,WAAW;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,OAAO,kBAAkB,MAAM,aAAa;AACxE,QAAM,WAAW,MAAM,YAAY;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI;AAA2C,uBAAmB,KAAK;AACvE,QAAM,MAAM;AAAA,IACV;AAAA,IACA;AAAA;AAAA,EAEF;AACA,MAAI;AAA2C,sBAAkB;AACjE,QAAM,gBAAgB,UAAU,GAAG;AACnC,MAAI,aAAa,SAAS;AAC1B,MAAI,iBAAiB,YAAY;AAC/B,UAAM,IAAI,QAAQ,QAAQ,GAAG,EAAE,KAAK,MAAM;AACxC,UAAI;AAAe,qBAAa,SAAS;AACzC,UAAI,YAAY;AACd,eAAO,QAAQ;AAAA,UACb,WAAW,IAAI,CAAC,aAAa,SAAS,KAAK,SAAS,KAAK,CAAC;AAAA,QAC5D;AAAA,MACF;AAAA,IACF,CAAC,EAAE,MAAM,IAAI;AACb,WAAO,EAAE,KAAK,MAAM,uBAAuB,UAAU,WAAW,CAAC;AAAA,EACnE,OAAO;AACL,WAAO,uBAAuB,UAAU,WAAW;AAAA,EACrD;AACF;AACA,SAAS,uBAAuB,UAAU,aAAa;AACrD,MAAI;AAA2C,uBAAmB,SAAS,KAAK;AAChF,QAAM,OAAO,SAAS;AACtB,QAAM,EAAE,WAAW,KAAK,IAAI,aAAa;AACzC,MAAI,WAAW,IAAI,GAAG;AACpB,QAAI,OAAO,oBAAoB,QAAQ;AACvC,QAAI,CAAC,KAAK,OAAO;AACf,iBAAW,OAAO,SAAS,OAAO;AAChC,YAAI,IAAI,WAAW,SAAS,GAAG;AAC7B,WAAC,KAAK,UAAU,KAAK,QAAQ,CAAC,IAAI,GAAG,IAAI;AAAA,QAC3C;AAAA,MACF;AAAA,IACF;AACA,gBAAY,MAAM,SAAS,UAAU,MAAM,UAAU,WAAW;AAAA,EAClE,OAAO;AACL,SAAK,CAAC,SAAS,UAAU,SAAS,WAAW,SAAS,CAAC,SAAS,aAAa,CAAC,KAAK,aAAa,SAAS,KAAK,QAAQ,GAAG;AACvH,WAAK,YAAY,WAAW,KAAK,QAAQ;AAAA,IAC3C;AACA,UAAM,YAAY,SAAS,aAAa,KAAK;AAC7C,QAAI,WAAW;AACb,UAAI,QAAQ,SAAS,iBAAiB,QAAQ,SAAS,QAAQ;AAC/D,UAAI,YAAY;AAChB,UAAI,MAAM;AACV,aAAO,MAAM;AACX,cAAM,UAAU,IAAI,MAAM;AAC1B,YAAI,SAAS;AACX,cAAI,CAAC,WAAW;AACd,oBAAQ,EAAE,GAAG,MAAM;AACnB,wBAAY;AAAA,UACd;AACA,gBAAM,OAAO,IAAI;AAAA,QACnB;AACA,cAAM,SAAS,IAAI;AACnB,YAAI,UAAU,OAAO,WAAW,OAAO,YAAY,IAAI,OAAO;AAC5D,gBAAM;AAAA,QACR,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACA,UAAI,aAAa;AACf,YAAI,CAAC;AAAW,kBAAQ,EAAE,GAAG,MAAM;AACnC,cAAM,kBAAkB,YAAY,KAAK,EAAE,MAAM,GAAG;AACpD,iBAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,gBAAM,gBAAgB,CAAC,CAAC,IAAI;AAAA,QAC9B;AAAA,MACF;AACA,YAAM,OAAO,4BAA4B,QAAQ;AACjD,UAAI;AACF;AAAA,UACE,SAAS;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA;AAAA,UAEA,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,QACX;AAAA,MACF,UAAE;AACA,oCAA4B,IAAI;AAAA,MAClC;AAAA,IACF,WAAW,SAAS,UAAU,SAAS,WAAW,MAAM;AACtD;AAAA,QACE;AAAA,QACA,SAAS,UAAU,oBAAoB,QAAQ;AAAA,QAC/C;AAAA,QACA;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,gBAAgB,KAAK,QAAQ,KAAK,UAAU;AAClD,WAAO,aAAa,aAAa,0CAA0C;AAC3E,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AACA,MAAI;AAA2C,sBAAkB;AACjE,SAAO,UAAU;AACnB;AACA,SAAS,YAAY,MAAM,OAAO,iBAAiB,aAAa;AAC9D,QAAM,EAAE,MAAM,WAAW,UAAU,MAAM,MAAM,IAAI;AACnD,MAAI,MAAM;AACR,UAAM,QAAQ,mBAAmB,OAAO,OAAO,IAAI;AAAA,EACrD;AACA,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,WAAK,WAAW,QAAQ,CAAC;AACzB;AAAA,IACF,KAAK;AACH;AAAA,QACE,WAAW,OAAO,kBAAkB,QAAQ,CAAC,QAAQ;AAAA,MACvD;AACA;AAAA,IACF,KAAK;AACH,WAAK,QAAQ;AACb;AAAA,IACF,KAAK;AACH,UAAI,MAAM,cAAc;AACtB,uBAAe,cAAc,cAAc,MAAM,MAAM,MAAM,aAAa,KAAK,GAAG;AAAA,MACpF;AACA,WAAK,UAAU;AACf;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,WAAK,UAAU;AACf;AAAA,IACF;AACE,UAAI,YAAY,GAAG;AACjB,2BAAmB,MAAM,OAAO,iBAAiB,WAAW;AAAA,MAC9D,WAAW,YAAY,GAAG;AACxB,aAAK,qBAAqB,OAAO,iBAAiB,WAAW,CAAC;AAAA,MAChE,WAAW,YAAY,IAAI;AACzB,4BAAoB,MAAM,OAAO,iBAAiB,WAAW;AAAA,MAC/D,WAAW,YAAY,KAAK;AAC1B,oBAAY,MAAM,MAAM,WAAW,iBAAiB,WAAW;AAAA,MACjE,OAAO;AACL;AAAA,UACE;AAAA,UACA;AAAA,UACA,IAAI,OAAO,IAAI;AAAA,QACjB;AAAA,MACF;AAAA,EACJ;AACF;AACA,SAAS,oBAAoB,MAAM,UAAU,iBAAiB,aAAa;AACzE,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAY,MAAM,eAAe,SAAS,CAAC,CAAC,GAAG,iBAAiB,WAAW;AAAA,EAC7E;AACF;AACA,SAAS,mBAAmB,MAAM,OAAO,iBAAiB,aAAa;AACrE,QAAM,MAAM,MAAM;AAClB,MAAI,EAAE,OAAO,UAAU,WAAW,QAAQ,IAAI;AAC9C,MAAI,UAAU,IAAI,GAAG;AACrB,MAAI,OAAO;AACT,eAAW,eAAe,OAAO,GAAG;AAAA,EACtC;AACA,MAAI,SAAS;AACX,eAAW,IAAI,OAAO;AAAA,EACxB;AACA,MAAI,YAAY;AAChB,MAAI,WAAW;AACf,SAAO,aAAa,aAAa,UAAU,SAAS;AAClD,eAAW,UAAU;AACrB,QAAI,SAAS,SAAS;AACpB,iBAAW,IAAI,SAAS,OAAO;AAAA,IACjC;AACA,gBAAY,UAAU;AAAA,EACxB;AACA,MAAI,aAAa;AACf,eAAW,IAAI,WAAW;AAAA,EAC5B;AACA,OAAK,UAAU,GAAG;AAClB,MAAI,CAAC,UAAU,GAAG,GAAG;AACnB,QAAI,sBAAsB;AAC1B,QAAI,OAAO;AACT,UAAI,MAAM,WAAW;AACnB,8BAAsB;AACtB,aAAK,MAAM,SAAS;AAAA,MACtB,WAAW,MAAM,aAAa;AAC5B,8BAAsB;AACtB,aAAK,WAAW,MAAM,WAAW,CAAC;AAAA,MACpC,WAAW,QAAQ,cAAc,MAAM,OAAO;AAC5C,8BAAsB;AACtB,aAAK,WAAW,MAAM,KAAK,CAAC;AAAA,MAC9B;AAAA,IACF;AACA,QAAI,CAAC,qBAAqB;AACxB,UAAI,YAAY,GAAG;AACjB,aAAK,WAAW,QAAQ,CAAC;AAAA,MAC3B,WAAW,YAAY,IAAI;AACzB;AAAA,UACE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,KAAK,GAAG,GAAG;AAAA,EAClB;AACF;AACA,SAAS,mBAAmB,OAAO,UAAU,MAAM;AACjD,QAAM,UAAU,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,UAAU,KAAK,CAAC;AACtB,UAAM;AAAA,MACJ,KAAK,EAAE,YAAY;AAAA,IACrB,IAAI;AACJ,QAAI,aAAa;AACf,YAAM,QAAQ,YAAY,SAAS,KAAK;AACxC,UAAI;AAAO,gBAAQ,KAAK,KAAK;AAAA,IAC/B;AAAA,EACF;AACA,SAAO,WAAW,YAAY,CAAC,GAAG,GAAG,OAAO;AAC9C;AACA,SAAS,oBAAoB,MAAM,OAAO,iBAAiB,aAAa;AACtE,QAAM,SAAS,MAAM,SAAS,MAAM,MAAM;AAC1C,QAAM,WAAW,MAAM,SAAS,MAAM,MAAM;AAC5C,MAAI,CAAC,QAAQ;AACX,QAAI,CAAC,UAAU;AACb,WAAO,yDAAyD;AAAA,IAClE;AACA,WAAO,CAAC;AAAA,EACV;AACA,MAAI,CAAC,SAAS,MAAM,GAAG;AACrB;AAAA,MACE;AAAA,IACF;AACA,WAAO,CAAC;AAAA,EACV;AACA;AAAA,IACE;AAAA,IACA,CAAC,UAAU;AACT;AAAA,QACE;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,IACA;AAAA,IACA,YAAY,aAAa;AAAA,IACzB;AAAA,EACF;AACF;AAEA,IAAM,EAAE,SAAS,UAAU,IAAI;AAC/B,SAAS,mBAAmB,QAAQ,WAAW,YAAY;AACzD,MAAI,CAAC,OAAO,UAAU;AACpB,WAAO,YAAY,mBAAmB,MAAM;AAAA,EAC9C;AACA,MAAI,MAAM;AACV,WAAS,IAAI,YAAY,IAAI,OAAO,QAAQ,KAAK,GAAG;AAClD,UAAM,OAAO,OAAO,CAAC;AACrB,QAAI,SAAS,IAAI,GAAG;AAClB,aAAO;AACP;AAAA,IACF;AACA,QAAI,UAAU,IAAI,GAAG;AACnB,aAAO,KAAK,KAAK,CAAC,eAAe;AAC/B,eAAO,CAAC,IAAI;AACZ,eAAO,mBAAmB,QAAQ,KAAK,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH;AACA,UAAM,SAAS,mBAAmB,MAAM,KAAK,CAAC;AAC9C,QAAI,UAAU,MAAM,GAAG;AACrB,aAAO,OAAO,KAAK,CAAC,eAAe;AACjC,eAAO,CAAC,IAAI;AACZ,eAAO,mBAAmB,QAAQ,IAAI,CAAC;AAAA,MACzC,CAAC;AAAA,IACH;AACA,UAAM;AAAA,EACR;AACA,SAAO;AACT;AACA,SAAS,eAAe,QAAQ;AAC9B,SAAO,mBAAmB,QAAQ,IAAI,CAAC;AACzC;AACA,SAAS,mBAAmB,QAAQ;AAClC,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,OAAO,OAAO,CAAC;AACnB,QAAI,SAAS,IAAI,GAAG;AAClB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,mBAAmB,IAAI;AAAA,IAChC;AAAA,EACF;AACA,SAAO;AACT;AACA,eAAe,eAAe,OAAO,UAAU,CAAC,GAAG;AACjD,MAAI,UAAU,KAAK,GAAG;AACpB,WAAO,eAAe,UAAU,EAAE,QAAQ,MAAM,MAAM,CAAC,GAAG,OAAO;AAAA,EACnE;AACA,QAAM,QAAQ,YAAY,MAAM,YAAY,MAAM,MAAM;AACxD,QAAM,aAAa,MAAM;AACzB,QAAM,QAAQ,eAAe,OAAO;AACpC,QAAM,SAAS,MAAM,qBAAqB,KAAK;AAC/C,QAAM,SAAS,MAAM,eAAe,MAAM;AAC1C,QAAM,iBAAiB,OAAO;AAC9B,MAAI,QAAQ,kBAAkB;AAC5B,eAAW,WAAW,QAAQ,kBAAkB;AAC9C,cAAQ;AAAA,IACV;AAAA,EACF;AACA,SAAO;AACT;AACA,eAAe,iBAAiB,SAAS;AACvC,MAAI,QAAQ,mBAAmB;AAC7B,YAAQ,YAAY,QAAQ,aAAa,CAAC;AAC1C,eAAW,OAAO,QAAQ,mBAAmB;AAC3C,cAAQ,UAAU,GAAG,IAAI,MAAM;AAAA,QAC7B,MAAM,QAAQ,IAAI,CAAC,QAAQ,kBAAkB,GAAG,CAAC,CAAC;AAAA,MACpD;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,EAAE,QAAQ,IAAI;AACpB,eAAe,aAAa,QAAQ,QAAQ;AAC1C,MAAI,OAAO,UAAU;AACnB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAI,OAAO,OAAO,CAAC;AACnB,UAAI,UAAU,IAAI,GAAG;AACnB,eAAO,MAAM;AAAA,MACf;AACA,UAAI,SAAS,IAAI,GAAG;AAClB,eAAO,KAAK,IAAI;AAAA,MAClB,OAAO;AACL,cAAM,aAAa,MAAM,MAAM;AAAA,MACjC;AAAA,IACF;AAAA,EACF,OAAO;AACL,qBAAiB,QAAQ,MAAM;AAAA,EACjC;AACF;AACA,SAAS,iBAAiB,QAAQ,QAAQ;AACxC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,OAAO,OAAO,CAAC;AACnB,QAAI,SAAS,IAAI,GAAG;AAClB,aAAO,KAAK,IAAI;AAAA,IAClB,OAAO;AACL,uBAAiB,MAAM,MAAM;AAAA,IAC/B;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,OAAO,SAAS,QAAQ;AACpD,MAAI,QAAQ,KAAK,GAAG;AAClB,WAAO;AAAA,MACL,UAAU,EAAE,QAAQ,MAAM,MAAM,CAAC;AAAA,MACjC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,QAAQ,YAAY,MAAM,YAAY,MAAM,MAAM;AACxD,QAAM,aAAa,MAAM;AACzB,QAAM,QAAQ,eAAe,OAAO;AACpC,UAAQ,QAAQ,qBAAqB,KAAK,CAAC,EAAE,KAAK,CAAC,WAAW,aAAa,QAAQ,MAAM,CAAC,EAAE,KAAK,MAAM,iBAAiB,OAAO,CAAC,EAAE,KAAK,MAAM;AAC3I,QAAI,QAAQ,kBAAkB;AAC5B,iBAAW,WAAW,QAAQ,kBAAkB;AAC9C,gBAAQ;AAAA,MACV;AAAA,IACF;AAAA,EACF,CAAC,EAAE,KAAK,MAAM,OAAO,KAAK,IAAI,CAAC,EAAE,MAAM,CAAC,UAAU;AAChD,WAAO,QAAQ,KAAK;AAAA,EACtB,CAAC;AACD,SAAO;AACT;AACA,SAAS,eAAe,OAAO,UAAU,CAAC,GAAG;AAC3C,UAAQ;AAAA,IACN;AAAA,EACF;AACA,SAAO,mBAAmB,OAAO,OAAO;AAC1C;AACA,SAAS,mBAAmB,OAAO,UAAU,CAAC,GAAG;AAC/C;AACE,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,OAAO,UAAU,CAAC,GAAG,UAAU;AACzD,uBAAqB,OAAO,SAAS;AAAA,IACnC,KAAK,SAAS;AACZ,UAAI,WAAW,MAAM;AACnB,iBAAS,MAAM,OAAO;AAAA,MACxB,OAAO;AACL,iBAAS,IAAI;AAAA,MACf;AAAA,IACF;AAAA,IACA,QAAQ,KAAK;AACX,eAAS,QAAQ,GAAG;AAAA,IACtB;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,OAAO,UAAU,CAAC,GAAG;AAC9C,MAAI,OAAO,mBAAmB,YAAY;AACxC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU,IAAI,YAAY;AAChC,MAAI,YAAY;AAChB,SAAO,IAAI,eAAe;AAAA,IACxB,MAAM,YAAY;AAChB,2BAAqB,OAAO,SAAS;AAAA,QACnC,KAAK,SAAS;AACZ,cAAI;AAAW;AACf,cAAI,WAAW,MAAM;AACnB,uBAAW,QAAQ,QAAQ,OAAO,OAAO,CAAC;AAAA,UAC5C,OAAO;AACL,uBAAW,MAAM;AAAA,UACnB;AAAA,QACF;AAAA,QACA,QAAQ,KAAK;AACX,qBAAW,MAAM,GAAG;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,SAAS;AACP,kBAAY;AAAA,IACd;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,OAAO,UAAU,CAAC,GAAG,UAAU;AACxD,QAAM,SAAS,SAAS,UAAU;AAClC,QAAM,UAAU,IAAI,YAAY;AAChC,MAAI,WAAW;AACf,MAAI;AACF,eAAW,UAAU,OAAO,KAAK;AAAA,EACnC,SAAS,GAAG;AAAA,EACZ;AACA,uBAAqB,OAAO,SAAS;AAAA,IACnC,MAAM,KAAK,SAAS;AAClB,UAAI,UAAU;AACZ,cAAM,OAAO;AAAA,MACf;AACA,UAAI,WAAW,MAAM;AACnB,eAAO,OAAO,MAAM,QAAQ,OAAO,OAAO,CAAC;AAAA,MAC7C,OAAO;AACL,eAAO,OAAO,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,IACA,QAAQ,KAAK;AACX,cAAQ,IAAI,GAAG;AACf,aAAO,MAAM;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAEA,qBAAqB;", "names": ["warn", "warn"]}