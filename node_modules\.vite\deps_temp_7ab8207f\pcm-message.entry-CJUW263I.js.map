{"version": 3, "sources": ["../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/src/components/pcm-message/pcm-message.css?tag=pcm-message&encapsulation=shadow", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/src/components/pcm-message/pcm-message.tsx"], "sourcesContent": [".pcm-message {\r\n  position: fixed;\r\n  top: 20px;\r\n  left: 50%;\r\n  transform: translateX(-50%) translateY(-100%);\r\n  background-color: #fff;\r\n  padding: 10px 16px;\r\n  border-radius: 4px;\r\n  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);\r\n  pointer-events: all;\r\n  z-index: 1010;\r\n  opacity: 0;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.pcm-message-visible {\r\n  transform: translateX(-50%) translateY(0);\r\n  opacity: 1;\r\n}\r\n\r\n.pcm-message-content {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.pcm-message-icon {\r\n  margin-right: 8px;\r\n  font-size: 16px;\r\n  line-height: 1;\r\n}\r\n\r\n.pcm-message-success .pcm-message-icon {\r\n  color: #52c41a;\r\n}\r\n\r\n.pcm-message-error .pcm-message-icon {\r\n  color: #ff4d4f;\r\n}\r\n\r\n.pcm-message-warning .pcm-message-icon {\r\n  color: #faad14;\r\n}\r\n\r\n.pcm-message-info .pcm-message-icon {\r\n  color: #1890ff;\r\n}\r\n\r\n.pcm-message-text {\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.pcm-message-container {\r\n  position: fixed;\r\n  top: 20px;\r\n  left: 0;\r\n  width: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  pointer-events: none;\r\n  z-index: 1010;\r\n} \r\n\r\n", "import { Component, h, Prop, State, Element, Method } from '@stencil/core';\r\n\r\n@Component({\r\n  tag: 'pcm-message',\r\n  styleUrl: 'pcm-message.css',\r\n  shadow: true,\r\n})\r\nexport class PcmMessage {\r\n  @Element() el: HTMLElement;\r\n  @Prop() content: string = '';\r\n  @Prop() type: 'success' | 'error' | 'info' | 'warning' = 'info';\r\n  @Prop() duration: number = 30000; // 默认显示3秒\r\n  @State() visible: boolean = false;\r\n\r\n  private timer: number;\r\n\r\n  componentDidLoad() {\r\n    if (this.content) {\r\n      this.show();\r\n    }\r\n  }\r\n\r\n  disconnectedCallback() {\r\n    if (this.timer) {\r\n      clearTimeout(this.timer);\r\n    }\r\n  }\r\n\r\n  @Method()\r\n  async show() {\r\n    this.visible = true;\r\n    \r\n    if (this.duration > 0) {\r\n      this.timer = window.setTimeout(() => {\r\n        this.close();\r\n      }, this.duration);\r\n    }\r\n  }\r\n\r\n  @Method()\r\n  async close() {\r\n    this.visible = false;\r\n    \r\n    // 动画结束后移除元素\r\n    setTimeout(() => {\r\n      if (this.el && this.el.parentNode) {\r\n        this.el.parentNode.removeChild(this.el);\r\n      }\r\n    }, 300); // 假设动画持续300ms\r\n  }\r\n\r\n  render() {\r\n    return (\r\n      <div class={{\r\n        'pcm-message': true,\r\n        'pcm-message-visible': this.visible,\r\n        [`pcm-message-${this.type}`]: true\r\n      }}>\r\n        <div class=\"pcm-message-content\">\r\n          <span class=\"pcm-message-icon\">\r\n            {this.renderIcon()}\r\n          </span>\r\n          <span class=\"pcm-message-text\">{this.content}</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  private renderIcon() {\r\n    switch (this.type) {\r\n      case 'success':\r\n        return <svg viewBox=\"64 64 896 896\" width=\"16px\" height=\"16px\" fill=\"currentColor\"><path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z\"></path></svg>;\r\n      case 'error':\r\n        return <svg viewBox=\"64 64 896 896\" width=\"16px\" height=\"16px\" fill=\"currentColor\"><path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 01-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z\"></path></svg>;\r\n      case 'warning':\r\n        return <svg viewBox=\"64 64 896 896\" width=\"16px\" height=\"16px\" fill=\"currentColor\"><path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z\"></path></svg>;\r\n      default: // info\r\n        return <svg viewBox=\"64 64 896 896\" width=\"16px\" height=\"16px\" fill=\"currentColor\"><path d=\"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 664c-30.9 0-56-25.1-56-56 0-30.9 25.1-56 56-56s56 25.1 56 56c0 30.9-25.1 56-56 56zm32-296c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V248c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v184z\"></path></svg>;\r\n    }\r\n  }\r\n} "], "mappings": ";;;;;;;;;;AAAA,IAAM,gBAAgB;ICOT,aAAU,MAAA;;AAEb,mCAAkB;AAClB,gCAAiD;AACjD,oCAAmB;AAClB;mCAAmB;AAEpB;;;;;;EAER,mBAAgB;AACd,QAAI,KAAK,SAAS;AAChB,WAAK,KAAI;;;EAIb,uBAAoB;AAClB,QAAI,KAAK,OAAO;AACd,mBAAa,KAAK,KAAK;;;EAK3B,MAAM,OAAI;AACR,SAAK,UAAU;AAEf,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,QAAQ,OAAO,WAAW,MAAK;AAClC,aAAK,MAAK;MACZ,GAAG,KAAK,QAAQ;;;EAKpB,MAAM,QAAK;AACT,SAAK,UAAU;AAGf,eAAW,MAAK;AACd,UAAI,KAAK,MAAM,KAAK,GAAG,YAAY;AACjC,aAAK,GAAG,WAAW,YAAY,KAAK,EAAE;;IAE1C,GAAG,GAAG;;EAGR,SAAM;AACJ,WACE,EAAK,OAAA,EAAA,KAAA,4CAAA,OAAO;MACV,eAAe;MACf,uBAAuB,KAAK;MAC5B,CAAC,eAAe,KAAK,IAAI,EAAE,GAAG;MAC/B,GACC,EAAK,OAAA,EAAA,KAAA,4CAAA,OAAM,sBAAqB,GAC9B,EAAM,QAAA,EAAA,KAAA,4CAAA,OAAM,mBAAkB,GAC3B,KAAK,WAAU,CAAE,GAEpB,EAAM,QAAA,EAAA,KAAA,4CAAA,OAAM,mBAAkB,GAAE,KAAK,OAAO,CAAQ,CAChD;;EAKJ,aAAU;AAChB,YAAQ,KAAK,MAAI;MACf,KAAK;AACH,eAAO,EAAK,OAAA,EAAA,SAAQ,iBAAgB,OAAM,QAAO,QAAO,QAAO,MAAK,eAAc,GAAC,EAAA,QAAA,EAAM,GAAE,oRAAmR,CAAA,CAAQ;MACxX,KAAK;AACH,eAAO,EAAK,OAAA,EAAA,SAAQ,iBAAgB,OAAM,QAAO,QAAO,QAAO,MAAK,eAAc,GAAC,EAAA,QAAA,EAAM,GAAE,0WAAyW,CAAA,CAAQ;MAC9c,KAAK;AACH,eAAO,EAAK,OAAA,EAAA,SAAQ,iBAAgB,OAAM,QAAO,QAAO,QAAO,MAAK,eAAc,GAAC,EAAA,QAAA,EAAM,GAAE,mOAAkO,CAAA,CAAQ;MACvU;AACE,eAAO,EAAK,OAAA,EAAA,SAAQ,iBAAgB,OAAM,QAAO,QAAO,QAAO,MAAK,eAAc,GAAC,EAAA,QAAA,EAAM,GAAE,qQAAoQ,CAAA,CAAQ;;;;;", "names": []}