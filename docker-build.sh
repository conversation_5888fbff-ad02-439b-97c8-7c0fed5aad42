#!/bin/bash

# Docker构建脚本
# 用法: ./docker-build.sh [版本号] [镜像名称]

# 默认参数
VERSION=${1:-"v1.0.0"}
IMAGE_NAME=${2:-"ai-recruitment-demo"}
REGISTRY="registry.cn-hangzhou.aliyuncs.com/weisoft"

# 完整镜像名称
FULL_IMAGE_NAME="${REGISTRY}/${IMAGE_NAME}:${VERSION}"

echo "🚀 开始构建Docker镜像..."
echo "📦 镜像名称: ${FULL_IMAGE_NAME}"
echo "🌍 平台: linux/amd64"
echo "🔧 代理: taobao"

# 构建跨平台镜像
docker buildx build \
  --platform linux/amd64 \
  -t "${FULL_IMAGE_NAME}" \
  --build-arg name=app \
  --build-arg proxy=taobao \
  --push \
  .

if [ $? -eq 0 ]; then
    echo "✅ 镜像构建成功!"
    echo "📋 镜像信息:"
    echo "   名称: ${FULL_IMAGE_NAME}"
    echo "   平台: linux/amd64"
    echo ""
    echo "🚀 运行命令:"
    echo "   docker run -d -p 8080:80 ${FULL_IMAGE_NAME}"
    echo ""
    echo "🌐 访问地址:"
    echo "   http://localhost:8080"
else
    echo "❌ 镜像构建失败!"
    exit 1
fi
