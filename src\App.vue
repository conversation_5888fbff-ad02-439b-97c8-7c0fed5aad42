<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  PcmZyghModal,
  PcmMnmsModal,
  PcmJlppModal,
  PcmJdModal,
  PcmMsbgModal,
  PcmHtwsModal,
  PcmHyzjModal,
  PcmMnctModal,
  PcmQgqjlModal,
  PcmHrChatModal,
  PcmCard
} from 'pcm-agents-vue'

// SDK配置参数
const SDK_CONFIG = {
  secretId: 'ak-BMSZyMnACKX6MPNne9zPfdFA',
  secretKey: 'sk-qrFpGRrDiAcu0YSUirhOVgPBT83qusb5',
  userId: '76015687511834624'
}

// SDK鉴权密钥
const SHARED_TOKEN = ref('')
const tokenLoading = ref(false)
const tokenError = ref('')

// 定义模态框类型
type ModalType = 'zygh' | 'mnms' | 'jlpp' | 'jd' | 'msbg' | 'htws' | 'hyzj' | 'mnct' | 'qgqjl' | 'hrChat'

// 模态框状态管理
const modals = ref<Record<ModalType, boolean>>({
  zygh: false,      // 职业规划
  mnms: false,      // 模拟面试
  jlpp: false,      // 简历匹配
  jd: false,        // 职位生成
  msbg: false,      // 面试报告
  htws: false,      // 劳动合同卫士
  hyzj: false,      // 会议总结
  mnct: false,      // 模拟出题
  qgqjl: false,     // 千岗千简历
  hrChat: false     // HR聊天
})

// 加载状态
const loading = ref(false)

// 会话ID管理
const conversationIds = ref<Record<ModalType, string>>({
  zygh: '',
  mnms: '',
  jlpp: '',
  jd: '',
  msbg: '',
  htws: '',
  hyzj: '',
  mnct: '',
  qgqjl: '',
  hrChat: ''
})

// 功能卡片数据
const featureCards = ref<Array<{
  id: ModalType
  title: string
  description: string
  icon: string
  category: string
}>>([
  {
    id: 'zygh',
    title: '职业规划助手',
    description: '基于AI的智能职业规划建议，帮助求职者制定个人发展路径',
    icon: '🎯',
    category: '求职者服务'
  },
  {
    id: 'mnms',
    title: '模拟面试',
    description: 'AI模拟真实面试场景，提供专业的面试训练和反馈',
    icon: '🎭',
    category: '面试训练'
  },
  {
    id: 'jlpp',
    title: '简历匹配',
    description: '智能分析简历与职位的匹配度，提供优化建议',
    icon: '📄',
    category: 'HR工具'
  },
  {
    id: 'jd',
    title: '职位生成',
    description: 'AI智能生成职位描述，提高招聘效率',
    icon: '📝',
    category: 'HR工具'
  },
  {
    id: 'msbg',
    title: '面试报告',
    description: '生成详细的面试评估报告，辅助招聘决策',
    icon: '📊',
    category: 'HR工具'
  },
  {
    id: 'htws',
    title: '劳动合同卫士',
    description: '智能审查劳动合同条款，保障双方权益',
    icon: '🛡️',
    category: '法务工具'
  },
  {
    id: 'hyzj',
    title: '会议总结助手',
    description: '自动生成会议纪要和总结，提高工作效率',
    icon: '📋',
    category: '办公工具'
  },
  {
    id: 'mnct',
    title: '模拟出题大师',
    description: '智能生成面试题目，丰富面试题库',
    icon: '❓',
    category: 'HR工具'
  },
  {
    id: 'qgqjl',
    title: '千岗千简历',
    description: '批量处理简历和职位匹配，提高筛选效率',
    icon: '🔄',
    category: 'HR工具'
  },
  {
    id: 'hrChat',
    title: 'HR智能助手',
    description: '专业的HR咨询助手，解答招聘相关问题',
    icon: '💬',
    category: 'HR工具'
  }
])

// 打开模态框
const openModal = (modalType: ModalType) => {
  console.log(`正在打开 ${modalType} 模态框`)
  loading.value = true

  // 模拟加载延迟，提供更好的用户体验
  setTimeout(() => {
    modals.value[modalType] = true
    loading.value = false
  }, 300)
}

// 关闭模态框
const closeModal = (modalType: ModalType) => {
  modals.value[modalType] = false
}

// 通用事件处理
const handleModalClosed = (modalType: ModalType) => {
  console.log(`${modalType} 模态框已关闭`)
  closeModal(modalType)
}

const handleStreamComplete = (event: CustomEvent) => {
  console.log('流式响应完成:', event.detail)
}

const handleConversationStart = (event: CustomEvent, modalType: ModalType) => {
  console.log('会话开始:', event.detail)
  if (event.detail && event.detail.conversation_id) {
    conversationIds.value[modalType] = event.detail.conversation_id
  }
}

const handleInterviewComplete = (event: CustomEvent) => {
  console.log('面试完成:', event.detail)
}

const handleUploadSuccess = (event: CustomEvent) => {
  console.log('文件上传成功:', event.detail)
}

const handleTokenInvalid = () => {
  console.error('Token无效，请检查SDK密钥配置')
  alert('SDK密钥可能无效，请联系管理员。这是演示环境，某些功能可能受限。')
}

const handleError = (event: CustomEvent) => {
  console.error('发生错误:', event.detail)
}

// 根据官方文档实现AK/SK签名认证获取Token
const getSDKToken = async () => {
  tokenLoading.value = true
  tokenError.value = ''

  try {
    // 1. 构造规范请求参数
    const method = 'GET'
    const apiUri = '/auth/access-token/' // 注意：必须以/结尾
    const timestamp = Math.floor(Date.now() / 1000) // 时间戳（秒）
    const user = SDK_CONFIG.userId // 使用userId作为user参数

    // 2. 创建待签字符串
    const stringToSign = `${method}@${apiUri}@${timestamp}`
    console.log('待签字符串:', stringToSign)

    // 3. 计算签名 (HMAC-SHA1 + Base64)
    const signature = await generateSignature(SDK_CONFIG.secretKey, stringToSign)
    console.log('生成的签名:', signature)

    // 4. 构造请求URL和Headers (使用代理)
    const baseUrl = '/api/agents/v1/auth/access-token'
    const fullUrl = `${baseUrl}?user=${encodeURIComponent(user)}`

    const headers = {
      'x-secret-id': SDK_CONFIG.secretId,
      'x-timestamp': timestamp.toString(),
      'x-signature': signature
    }

    console.log('请求URL:', fullUrl)
    console.log('请求Headers:', headers)

    // 5. 发送请求
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: headers
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('API响应:', data)

    // 6. 处理响应
    if (data.code === 0 && data.data && data.data.token) {
      SHARED_TOKEN.value = data.data.token
      console.log('SDK Token获取成功')
      tokenLoading.value = false
      return
    } else {
      throw new Error(data.message || data.msg || '获取token失败')
    }

  } catch (error) {
    console.error('获取token失败:', error)

    const errorMsg = error instanceof Error ? error.message : String(error)

    if (errorMsg.includes('Failed to fetch')) {
      tokenError.value = 'CORS跨域问题：浏览器阻止了跨域请求，请配置代理服务器'
    } else if (errorMsg.includes('CORS')) {
      tokenError.value = 'CORS跨域问题：需要配置代理服务器解决跨域限制'
    } else if (errorMsg.includes('Network')) {
      tokenError.value = '网络连接失败：请检查网络连接'
    } else {
      tokenError.value = `获取token失败: ${errorMsg}`
    }

    tokenLoading.value = false
  }
}

// 生成HMAC-SHA1签名的辅助函数
const generateSignature = async (secretKey: string, stringToSign: string) => {
  try {
    // 将字符串转换为Uint8Array
    const encoder = new TextEncoder()
    const keyData = encoder.encode(secretKey)
    const messageData = encoder.encode(stringToSign)

    // 导入密钥
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-1' },
      false,
      ['sign']
    )

    // 计算HMAC
    const signature = await crypto.subtle.sign('HMAC', cryptoKey, messageData)

    // 转换为Base64
    const signatureArray = new Uint8Array(signature)
    let binaryString = ''
    for (let i = 0; i < signatureArray.length; i++) {
      binaryString += String.fromCharCode(signatureArray[i])
    }
    const signatureBase64 = btoa(binaryString)

    return signatureBase64
  } catch (error) {
    console.error('签名生成失败:', error)
    // 如果Web Crypto API不可用，使用简单的fallback
    return btoa(secretKey + stringToSign).substring(0, 28)
  }
}

// 隐藏备案信息的函数
const hideBeianInfo = () => {
  // 获取所有可能的模态框元素
  const modalSelectors = [
    'pcm-mnms-modal',
    'pcm-zygh-modal',
    'pcm-jlpp-modal',
    'pcm-jd-modal',
    'pcm-msbg-modal',
    'pcm-htws-modal',
    'pcm-hyzj-modal',
    'pcm-mnct-modal',
    'pcm-qgqjl-modal',
    'pcm-hr-chat-modal'
  ]

  // 查找所有可能的模态框元素
  modalSelectors.forEach(selector => {
    const modals = document.querySelectorAll(selector)
    modals.forEach((modal: any) => {
      if (modal && modal.shadowRoot) {
        // 尝试多种可能的备案信息选择器
        const beianSelectors = [
          '.beian-info',
          '.beian',
          '.record-info',
          '.filing-info',
          '[class*="beian"]',
          '[class*="record"]',
          '[class*="filing"]'
        ]

        beianSelectors.forEach(beianSelector => {
          const beianElements = modal.shadowRoot.querySelectorAll(beianSelector)
          beianElements.forEach((beianElement: HTMLElement) => {
            if (beianElement && beianElement.style.display !== 'none') {
              beianElement.style.display = 'none'
              console.log(`✅ 已隐藏备案信息: ${beianSelector}`)
            }
          })
        })

        // 额外检查：查找包含备案号文本的元素
        const allElements = modal.shadowRoot.querySelectorAll('*')
        allElements.forEach((element: HTMLElement) => {
          if (element.textContent &&
              (element.textContent.includes('备案号') ||
               element.textContent.includes('Hunan-PinCaiMao') ||
               element.textContent.includes('中央网信办'))) {
            element.style.display = 'none'
            console.log(`✅ 已隐藏包含备案信息的元素: ${element.textContent.substring(0, 50)}...`)
          }
        })
      }
    })
  })
}

// 监听所有模态框打开事件
const setupBeianHiding = () => {
  // 立即执行一次隐藏
  hideBeianInfo()

  // 定期检查并隐藏备案信息
  const regularCheck = setInterval(hideBeianInfo, 1000)

  // 监听点击事件
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement
    if (target && target.classList && target.classList.contains('feature-card')) {
      // 立即隐藏
      hideBeianInfo()
      // 延迟隐藏信息，等待组件完全渲染
      setTimeout(hideBeianInfo, 50)
      setTimeout(hideBeianInfo, 200)
      setTimeout(hideBeianInfo, 500)
      setTimeout(hideBeianInfo, 1000)
      setTimeout(hideBeianInfo, 2000)
    }
  })

  // 监听DOM变化
  const observer = new MutationObserver((mutations) => {
    let shouldCheck = false
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        shouldCheck = true
      }
      if (mutation.type === 'attributes' &&
          (mutation.attributeName === 'is-open' ||
           mutation.attributeName === 'class' ||
           mutation.attributeName === 'style')) {
        shouldCheck = true
      }
    })
    if (shouldCheck) {
      hideBeianInfo()
      setTimeout(hideBeianInfo, 100)
    }
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['is-open', 'class', 'style']
  })

  // 监听窗口焦点变化
  window.addEventListener('focus', hideBeianInfo)

  // 清理函数
  return () => {
    clearInterval(regularCheck)
    observer.disconnect()
    window.removeEventListener('focus', hideBeianInfo)
  }
}

// 组件挂载时获取token和设置备案信息隐藏
onMounted(() => {
  getSDKToken()
  setupBeianHiding()
})

</script>

<template>
  <div class="app">
    <!-- 头部导航 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo-section">
            <h1 class="logo-title">🤖 AI招聘助手演示平台</h1>
            <p class="logo-subtitle">智能化招聘解决方案</p>
          </div>
          <div class="company-info">
            <span class="company-name">上海未软人工智能公司</span>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main">
      <div class="container">
        <!-- 介绍区域 -->
        <section class="intro-section">
          <h2 class="section-title">智能招聘，未来已来</h2>
          <p class="section-description">
            体验最前沿的AI招聘技术，从职业规划到面试评估，从简历匹配到合同审查，
            全方位的智能化招聘解决方案，让招聘更高效、更精准、更智能。
          </p>

          <!-- Token状态显示 -->
          <div class="token-status">
            <div v-if="tokenLoading" class="status-item loading">
              <span class="status-icon">⏳</span>
              <span>正在获取系统授权...</span>
            </div>
            <div v-else-if="tokenError" class="status-item error">
              <span class="status-icon">❌</span>
              <span>{{ tokenError }}</span>
              <button @click="getSDKToken" class="retry-btn">重试</button>
            </div>
            <div v-else-if="SHARED_TOKEN" class="status-item success">
              <span class="status-icon">✅</span>
              <span>系统授权成功，所有功能已就绪</span>
            </div>
            <div v-else class="status-item waiting">
              <span class="status-icon">⚪</span>
              <span>等待获取系统授权</span>
              <button @click="getSDKToken" class="retry-btn">获取授权</button>
            </div>
          </div>
        </section>

        <!-- 功能卡片网格 -->
        <section class="features-section">
          <div class="features-grid">
            <div
              v-for="card in featureCards"
              :key="card.id"
              class="feature-card"
              @click="openModal(card.id)"
            >
              <div class="card-header">
                <span class="card-icon">{{ card.icon }}</span>
                <div class="card-category">{{ card.category }}</div>
              </div>
              <h3 class="card-title">{{ card.title }}</h3>
              <p class="card-description">{{ card.description }}</p>
              <div class="card-action">
                <button class="try-button" :disabled="loading">
                  <span v-if="loading">加载中...</span>
                  <span v-else>立即体验</span>
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-info">
            <p>&copy; 2024 上海未软人工智能公司 版权所有</p>
            <p>AI招聘解决方案提供商</p>
          </div>
          <div class="footer-links">
            <a href="#" class="footer-link">关于我们</a>
            <a href="#" class="footer-link">联系我们</a>
            <a href="#" class="footer-link">技术支持</a>
          </div>
        </div>
      </div>
    </footer>

    <!-- 所有模态框组件 -->
    <!-- 职业规划助手 -->
    <PcmZyghModal
      v-if="SHARED_TOKEN"
      :is-open="modals.zygh"
      :token="SHARED_TOKEN"
      modal-title="AI职业规划助手"
      :conversation-id="conversationIds.zygh"
      :custom-inputs="{ type: '长期规划' }"
      default-query="您好！我是AI职业规划助手，我可以帮助您制定个人职业发展规划。请告诉我您的职业背景和发展目标。"
      :fullscreen="false"
      :is-need-close="true"
      :is-show-header="true"
      file-preview-mode="drawer"
      @modal-closed="() => handleModalClosed('zygh')"
      @stream-complete="handleStreamComplete"
      @conversation-start="(e: any) => handleConversationStart(e, 'zygh')"
      @interview-complete="handleInterviewComplete"
      @token-invalid="handleTokenInvalid"
      @some-error-event="handleError"
    />

    <!-- 模拟面试 -->
    <PcmMnmsModal
      v-if="SHARED_TOKEN"
      :is-open="modals.mnms"
      :token="SHARED_TOKEN"
      modal-title="AI模拟面试系统"
      :conversation-id="conversationIds.mnms"
      :custom-inputs="{}"
      default-query="欢迎使用AI模拟面试系统！请先上传您的简历，然后输入目标职位信息，我将为您模拟真实的面试场景。"
      :fullscreen="false"
      :is-need-close="true"
      :is-show-header="true"
      interview-mode="text"
      file-preview-mode="drawer"
      :show-copy-button="true"
      :show-feedback-buttons="true"
      @modal-closed="() => handleModalClosed('mnms')"
      @stream-complete="handleStreamComplete"
      @conversation-start="(e: any) => handleConversationStart(e, 'mnms')"
      @interview-complete="handleInterviewComplete"
      @token-invalid="handleTokenInvalid"
      @some-error-event="handleError"
    />

    <!-- 简历匹配 -->
    <PcmJlppModal
      v-if="SHARED_TOKEN"
      :is-open="modals.jlpp"
      :token="SHARED_TOKEN"
      modal-title="简历匹配分析"
      :conversation-id="conversationIds.jlpp"
      :custom-inputs="{}"
      :fullscreen="false"
      :is-need-close="true"
      :is-show-header="true"
      file-preview-mode="drawer"
      @modal-closed="() => handleModalClosed('jlpp')"
      @stream-complete="handleStreamComplete"
      @conversation-start="(e: any) => handleConversationStart(e, 'jlpp')"
      @upload-success="handleUploadSuccess"
      @token-invalid="handleTokenInvalid"
    />

    <!-- 职位生成 -->
    <PcmJdModal
      v-if="SHARED_TOKEN"
      :is-open="modals.jd"
      :token="SHARED_TOKEN"
      modal-title="智能职位生成"
      :conversation-id="conversationIds.jd"
      :custom-inputs="{}"
      :fullscreen="false"
      :is-need-close="true"
      :is-show-header="true"
      file-preview-mode="drawer"
      @modal-closed="() => handleModalClosed('jd')"
      @stream-complete="handleStreamComplete"
      @conversation-start="(e: any) => handleConversationStart(e, 'jd')"
      @token-invalid="handleTokenInvalid"
    />

    <!-- 面试报告 -->
    <PcmMsbgModal
      v-if="SHARED_TOKEN"
      :is-open="modals.msbg"
      :token="SHARED_TOKEN"
      modal-title="面试评估报告"
      :conversation-id="conversationIds.msbg"
      :custom-inputs="{}"
      :fullscreen="false"
      :is-need-close="true"
      :is-show-header="true"
      file-preview-mode="drawer"
      @modal-closed="() => handleModalClosed('msbg')"
      @stream-complete="handleStreamComplete"
      @conversation-start="(e: any) => handleConversationStart(e, 'msbg')"
      @upload-success="handleUploadSuccess"
      @token-invalid="handleTokenInvalid"
    />

    <!-- 劳动合同卫士 -->
    <PcmHtwsModal
      v-if="SHARED_TOKEN"
      :is-open="modals.htws"
      :token="SHARED_TOKEN"
      modal-title="劳动合同卫士"
      :conversation-id="conversationIds.htws"
      :custom-inputs="{}"
      :fullscreen="false"
      :is-need-close="true"
      :is-show-header="true"
      file-preview-mode="drawer"
      @modal-closed="() => handleModalClosed('htws')"
      @stream-complete="handleStreamComplete"
      @conversation-start="(e: any) => handleConversationStart(e, 'htws')"
      @upload-success="handleUploadSuccess"
      @token-invalid="handleTokenInvalid"
    />

    <!-- 会议总结助手 -->
    <PcmHyzjModal
      v-if="SHARED_TOKEN"
      :is-open="modals.hyzj"
      :token="SHARED_TOKEN"
      modal-title="会议总结助手"
      :conversation-id="conversationIds.hyzj"
      :custom-inputs="{}"
      :fullscreen="false"
      :is-need-close="true"
      :is-show-header="true"
      file-preview-mode="drawer"
      @modal-closed="() => handleModalClosed('hyzj')"
      @stream-complete="handleStreamComplete"
      @conversation-start="(e: any) => handleConversationStart(e, 'hyzj')"
      @upload-success="handleUploadSuccess"
      @token-invalid="handleTokenInvalid"
    />

    <!-- 模拟出题大师 -->
    <PcmMnctModal
      v-if="SHARED_TOKEN"
      :is-open="modals.mnct"
      :token="SHARED_TOKEN"
      modal-title="模拟出题大师"
      :conversation-id="conversationIds.mnct"
      :custom-inputs="{}"
      :fullscreen="false"
      :is-need-close="true"
      :is-show-header="true"
      file-preview-mode="drawer"
      @modal-closed="() => handleModalClosed('mnct')"
      @stream-complete="handleStreamComplete"
      @conversation-start="(e: any) => handleConversationStart(e, 'mnct')"
      @upload-success="handleUploadSuccess"
      @token-invalid="handleTokenInvalid"
    />

    <!-- 千岗千简历 -->
    <PcmQgqjlModal
      v-if="SHARED_TOKEN"
      :is-open="modals.qgqjl"
      :token="SHARED_TOKEN"
      modal-title="千岗千简历"
      :conversation-id="conversationIds.qgqjl"
      :custom-inputs="{}"
      :fullscreen="false"
      :is-need-close="true"
      :is-show-header="true"
      file-preview-mode="drawer"
      @modal-closed="() => handleModalClosed('qgqjl')"
      @stream-complete="handleStreamComplete"
      @conversation-start="(e: any) => handleConversationStart(e, 'qgqjl')"
      @upload-success="handleUploadSuccess"
      @token-invalid="handleTokenInvalid"
    />

    <!-- HR智能助手 -->
    <PcmHrChatModal
      v-if="SHARED_TOKEN"
      :is-open="modals.hrChat"
      :token="SHARED_TOKEN"
      modal-title="HR智能助手"
      :conversation-id="conversationIds.hrChat"
      default-query="您好，我是HR智能助手，有什么可以帮助您的吗？"
      :fullscreen="false"
      :is-need-close="true"
      :is-show-header="true"
      :enable-audio="true"
      :enable-voice="false"
      :display-content-status="true"
      :require-resume="false"
      :total-questions="10"
      :max-recording-time="300"
      :countdown-warning-time="30"
      to-email=""
      callback-url=""
      @modal-closed="() => handleModalClosed('hrChat')"
      @stream-complete="handleStreamComplete"
      @conversation-start="(e: any) => handleConversationStart(e, 'hrChat')"
      @interview-complete="handleInterviewComplete"
      @token-invalid="handleTokenInvalid"
    />

    <!-- 全局加载遮罩 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p>正在加载AI助手...</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  min-width: 1200px; /* PC端最小宽度 */
  overflow-x: auto;
}

.container {
  max-width: 1400px; /* 增大PC端容器宽度 */
  margin: 0 auto;
  padding: 0 40px; /* 增大PC端内边距 */
}

/* 头部样式 */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
}

.logo-section {
  flex: 1;
}

.logo-title {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 5px;
}

.logo-subtitle {
  color: #666;
  font-size: 0.9rem;
}

.company-info {
  text-align: right;
}

.company-name {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

/* 主要内容区域 */
.main {
  flex: 1;
  padding: 40px 0;
}

/* 介绍区域 */
.intro-section {
  text-align: center;
  margin-bottom: 60px;
  color: white;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-description {
  font-size: 1.2rem;
  line-height: 1.8;
  max-width: 800px;
  margin: 0 auto;
  opacity: 0.95;
}

/* Token状态样式 */
.token-status {
  margin-top: 30px;
}

.status-item {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  border-radius: 25px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.status-item.loading {
  background: rgba(255, 193, 7, 0.2);
  color: #fff3cd;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-item.error {
  background: rgba(220, 53, 69, 0.2);
  color: #f8d7da;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.status-item.warning {
  background: rgba(255, 193, 7, 0.2);
  color: #fff3cd;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-item.fallback {
  background: rgba(23, 162, 184, 0.2);
  color: #bee5eb;
  border: 1px solid rgba(23, 162, 184, 0.3);
}

.status-item.success {
  background: rgba(40, 167, 69, 0.2);
  color: #d4edda;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-icon {
  font-size: 1.2rem;
}

.retry-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 功能卡片网格 */
.features-section {
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* PC端固定4列布局 */
  gap: 30px;
  margin-top: 40px;
}

/* 大屏幕适配 */
@media (min-width: 1600px) {
  .features-grid {
    grid-template-columns: repeat(5, 1fr); /* 超大屏幕5列 */
    gap: 40px;
  }
}

/* 中等屏幕适配 */
@media (max-width: 1400px) and (min-width: 1200px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr); /* 中等屏幕3列 */
  }
}

.feature-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px 30px; /* 增加PC端内边距 */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 280px; /* 确保PC端卡片高度一致 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-icon {
  font-size: 3rem;
  line-height: 1;
}

.card-category {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: #333;
}

.card-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 25px;
  font-size: 1rem;
}

.card-action {
  text-align: center;
}

.try-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.try-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.try-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 页脚样式 */
.footer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 0;
}

.footer-info p {
  margin-bottom: 5px;
  color: #666;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.footer-link {
  color: #666;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #667eea;
}

/* PC端专用设计 - 专注桌面端体验 */

/* 加载遮罩样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.loading-spinner {
  text-align: center;
  color: white;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  font-size: 1.1rem;
  margin: 0;
}
</style>