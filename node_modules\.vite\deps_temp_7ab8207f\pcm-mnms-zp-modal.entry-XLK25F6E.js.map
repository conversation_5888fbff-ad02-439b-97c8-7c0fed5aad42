{"version": 3, "sources": ["../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/src/components/pcm-mnms-zp-modal/pcm-mnms-zp-modal.css?tag=pcm-mnms-zp-modal&encapsulation=shadow", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/src/global/global.css?tag=pcm-mnms-zp-modal&encapsulation=shadow", "../../.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/src/components/pcm-mnms-zp-modal/pcm-mnms-zp-modal.tsx"], "sourcesContent": ["", ":host {\r\n    font-size: 16px;\r\n}\r\n\r\n/* 模态框基础样式 */\r\n.modal-overlay {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: rgba(0, 0, 0, 0.5);\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    z-index: 1000;\r\n    overflow-y: auto;\r\n    padding: 20px;\r\n}\r\n\r\n/* 全屏模式下取消 padding */\r\n.fullscreen-overlay {\r\n    padding: 0;\r\n    background-color: rgba(0, 0, 0, 0.7);\r\n}\r\n\r\n.modal-container {\r\n    background-color: #fff;\r\n    border-radius: 8px;\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    position: relative;\r\n    margin: auto;\r\n    transition: all 0.3s ease-out;\r\n    overflow: hidden;\r\n}\r\n\r\n/* 全屏模式样式 */\r\n.modal-container.fullscreen {\r\n    width: 100vw;\r\n    max-width: none;\r\n    height: 100%;\r\n    border-radius: 0;\r\n    margin: 0;\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: 100vh;\r\n    max-height: 100vh;\r\n}\r\n\r\n/* PC端布局 */\r\n.pc-layout {\r\n    width: 80%;\r\n    max-width: 600px;\r\n    min-width: 320px;\r\n}\r\n\r\n/* 响应式布局 */\r\n@media screen and (max-width: 768px) {\r\n    .pc-layout {\r\n        width: 95%;\r\n    }\r\n\r\n    .modal-overlay {\r\n        padding: 0;\r\n    }\r\n\r\n    .modal-container.fullscreen {\r\n        /* 支持 iOS Safari */\r\n        height: -webkit-fill-available;\r\n        max-height: -webkit-fill-available;\r\n        /* 确保内容不会被顶部状态栏和底部工具栏遮挡 */\r\n        padding: env(safe-area-inset-top) 0 env(safe-area-inset-bottom);\r\n        margin-top: 40px;\r\n        height: calc(100% - 40px);\r\n        max-height: calc(100% - 40px);\r\n        border-radius: 16px 16px 0 0;\r\n    }\r\n\r\n}\r\n\r\n/* 模态框头部样式 */\r\n.modal-header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 4px 16px;\r\n    height: 50px;\r\n    border-bottom: 1px solid #e8e8e8;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.header-left {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n}\r\n\r\n.header-icon {\r\n    width: 24px;\r\n    height: 24px;\r\n}\r\n\r\n.close-button {\r\n    background: transparent;\r\n    border: none;\r\n    cursor: pointer;\r\n    padding: 8px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 32px;\r\n    height: 32px;\r\n    border-radius: 4px;\r\n}\r\n\r\n.close-button:hover {\r\n    background-color: rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n.close-button span {\r\n    font-size: 24px;\r\n    line-height: 1;\r\n    color: #999;\r\n}\r\n\r\n.close-button:hover span {\r\n    color: #666;\r\n}\r\n\r\n\r\n/* 文件上传区域通用样式 */\r\n.upload-area {\r\n    cursor: pointer;\r\n    width: 100%;\r\n}\r\n\r\n\r\n.upload-placeholder {\r\n    transition: all 0.3s ease;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    background: rgba(0, 0, 0, 0.02);\r\n    border: 1px dashed #d9d9d9;\r\n    border-radius: 8px;\r\n}\r\n\r\n.upload-placeholder:hover {\r\n    border: 1px dashed #1890ff;\r\n}\r\n\r\n.upload-placeholder img {\r\n    margin-top: 8px;\r\n    width: 50px;\r\n    height: 50px;\r\n}\r\n\r\n.upload-placeholder .upload-text {\r\n    margin: 4px 0;\r\n    color: #332F39;\r\n    font-size: 14px;\r\n}\r\n\r\n.upload-placeholder .upload-hint {\r\n    font-size: 14px;\r\n    color: #949AA5;\r\n    margin-top: 8px;\r\n    padding: 0px 10px;\r\n}\r\n\r\n\r\n/* 文件项样式 */\r\n.file-item {\r\n    position: relative;\r\n    padding: 16px;\r\n    border: 1px solid #e2e8f0;\r\n    border-radius: 8px;\r\n    transition: border-color 0.3s;\r\n    cursor: pointer;\r\n    margin-bottom: 16px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n}\r\n\r\n.file-item:hover {\r\n    border-color: #0D75FB;\r\n}\r\n\r\n.file-item-content {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    flex: 1;\r\n    min-width: 0;\r\n    overflow: hidden;\r\n}\r\n\r\n.file-icon {\r\n    color: #0D75FB;\r\n    flex-shrink: 0;\r\n}\r\n\r\n.file-name {\r\n    font-weight: 500;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    max-width: calc(100% - 50px);\r\n}\r\n\r\n.remove-file {\r\n    background: transparent;\r\n    border: none;\r\n    color: #94a3b8;\r\n    cursor: pointer;\r\n    font-size: 18px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 4px;\r\n    margin-left: 8px;\r\n    border-radius: 4px;\r\n    transition: all 0.2s;\r\n}\r\n\r\n.remove-file:hover {\r\n    background-color: #f1f5f9;\r\n    color: #475569;\r\n}\r\n\r\n.file-input {\r\n    display: none;\r\n}\r\n\r\n\r\n\r\n/* 输入容器样式 */\r\n.input-container {\r\n    padding: 20px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    height: calc(100% - 50px);\r\n    background: linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;\r\n    /* 减去header高度 */\r\n    overflow-y: auto;\r\n}\r\n\r\n.input-container h3 {\r\n    margin-top: 0;\r\n    margin-bottom: 20px;\r\n    font-size: 18px;\r\n    color: #333;\r\n    text-align: center;\r\n}\r\n\r\n/* JD输入区域样式 */\r\n.jd-input-section {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.jd-input-section label {\r\n    display: block;\r\n    margin-bottom: 8px;\r\n    font-weight: 500;\r\n    color: #333;\r\n}\r\n\r\n.job-description-textarea {\r\n    width: calc(100% - 16px);\r\n    border: 1px solid #ddd;\r\n    border-radius: 4px;\r\n    resize: vertical;\r\n    font-family: inherit;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n    transition: border-color 0.3s;\r\n    padding: 8px;\r\n}\r\n\r\n.job-description-textarea:focus {\r\n    outline: none;\r\n    border-color: #1890ff;\r\n    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\n/* 简历上传区域样式 */\r\n.resume-upload-section {\r\n    margin-bottom: 20px;\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n}\r\n\r\n.resume-upload-section label {\r\n    display: block;\r\n    margin-bottom: 8px;\r\n    font-weight: 500;\r\n    color: #333;\r\n    align-self: flex-start;\r\n}\r\n\r\n\r\n/* 提交按钮通用样式 */\r\n.submit-button {\r\n    margin-top: 10px;\r\n    padding: 10px 30px;\r\n    background: #0D75FB;\r\n    color: white;\r\n    border: none;\r\n    border-radius: 4px;\r\n    font-size: 16px;\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n    width: 100%;\r\n    max-width: 400px;\r\n    align-self: center;\r\n}\r\n\r\n.submit-button:hover {\r\n    background-color: #40a9ff;\r\n}\r\n\r\n.submit-button:disabled {\r\n    background-color: rgba(0,0,0,0.04);\r\n    color: rgba(0,0,0,0.25);\r\n    cursor: not-allowed;\r\n}\r\n\r\n\r\n\r\n/* AI免责声明和备案信息样式 */\r\n.ai-disclaimer {\r\n    margin-top: 16px;\r\n    text-align: center;\r\n    font-size: 12px;\r\n    color: #999;\r\n    line-height: 1.5;\r\n}\r\n\r\n.ai-disclaimer p {\r\n    margin: 4px 0;\r\n}\r\n\r\n.beian-info {\r\n    display: flex;\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n    gap: 4px;\r\n}\r\n\r\n.ai-disclaimer a {\r\n    color: #666;\r\n    text-decoration: none;\r\n    transition: color 0.2s ease;\r\n}\r\n\r\n.ai-disclaimer a:hover {\r\n    color: #1890ff;\r\n    text-decoration: underline;\r\n}\r\n\r\n/* 添加加载状态的样式 */\r\n.loading-container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    padding: 24px;\r\n  }\r\n  \r\n  .loading-spinner {\r\n    width: 40px;\r\n    height: 40px;\r\n    border: 4px solid rgba(0, 0, 0, 0.1);\r\n    border-radius: 50%;\r\n    border-top-color: var(--pcm-primary-color, #1890ff);\r\n    animation: spin 1s linear infinite;\r\n    margin-bottom: 16px;\r\n  }\r\n  \r\n  .loading-text {\r\n    font-size: 16px;\r\n    color: var(--pcm-text-color, #333);\r\n  }\r\n  \r\n  @keyframes spin {\r\n    0% { transform: rotate(0deg); }\r\n    100% { transform: rotate(360deg); }\r\n  }\r\n  \r\n  ", "import { Component, Prop, h, State, Element, Event, EventEmitter, Watch } from '@stencil/core';\r\nimport { uploadFileToBackend, FileUploadResponse, verifyApiKey } from '../../utils/utils';\r\nimport {\r\n    StreamCompleteEventData,\r\n    ConversationStartEventData,\r\n    InterviewCompleteEventData,\r\n    RecordingErrorEventData,\r\n} from '../../interfaces/events';\r\nimport { ErrorEventBus, ErrorEventDetail } from '../../utils/error-event';\r\nimport { authStore } from '../../../store/auth.store';\r\nimport { configStore } from '../../../store/config.store';\r\nimport { SentryReporter } from '../../utils/sentry-reporter';\r\n\r\n/**\r\n * 模拟面试\r\n */\r\n\r\n@Component({\r\n    tag: 'pcm-mnms-zp-modal',\r\n    styleUrls: ['pcm-mnms-zp-modal.css', '../../global/global.css'],\r\n    shadow: true,\r\n})\r\nexport class MnmsZpModal {\r\n    /**\r\n     * 模态框标题\r\n     */\r\n    @Prop() modalTitle: string = '模拟面试';\r\n\r\n    /**\r\n     * SDK鉴权密钥\r\n     */\r\n    @Prop({ attribute: 'token' }) token!: string;\r\n\r\n    /**\r\n     * 是否显示聊天模态框\r\n     */\r\n    @Prop({ mutable: true }) isOpen: boolean = false;\r\n\r\n    /**\r\n     * 当点击模态框关闭时触发\r\n     */\r\n    @Event() modalClosed: EventEmitter<void>;\r\n\r\n    /**\r\n     * 应用图标URL\r\n     */\r\n    @Prop() icon?: string;\r\n\r\n    /**\r\n     * 聊天框的页面层级\r\n     */\r\n    @Prop() zIndex?: number = 1000;\r\n\r\n    /**\r\n     * 是否展示顶部标题栏\r\n     */\r\n    @Prop() isShowHeader: boolean = true;\r\n\r\n    /**\r\n     * 是否展示右上角的关闭按钮\r\n     */\r\n    @Prop() isNeedClose: boolean = true;\r\n\r\n    /**\r\n     * 会话ID，传入继续对话，否则创建新会话\r\n     */\r\n    @Prop({ mutable: true }) conversationId?: string;\r\n\r\n    /**\r\n     * 默认查询文本\r\n     */\r\n    @Prop() defaultQuery: string = '请开始模拟面试';\r\n\r\n    /**\r\n     * 是否以全屏模式打开，移动端建议设置为true\r\n     */\r\n    @Prop() fullscreen: boolean = false;\r\n\r\n    /**\r\n     * 自定义输入参数，传入customInputs.job_info时，会隐藏JD输入区域。<br>\r\n     * 传入customInputs.file_url时，会隐藏简历上传区域。<br>\r\n     * 传入customInputs.file_url和customInputs.job_info时，会直接开始聊天。<br>\r\n     */\r\n    @Prop() customInputs: Record<string, string> = {};\r\n\r\n\r\n    /**\r\n     * 上传成功事件\r\n     */\r\n    @Event() uploadSuccess: EventEmitter<FileUploadResponse>;\r\n\r\n    /**\r\n     * 流式输出完成事件\r\n     */\r\n    @Event() streamComplete: EventEmitter<StreamCompleteEventData>;\r\n\r\n    /**\r\n     * 新会话开始的回调，只会在一轮对话开始时触发一次\r\n     */\r\n    @Event() conversationStart: EventEmitter<ConversationStartEventData>;\r\n\r\n    /**\r\n     * 当聊天完成时触发\r\n     */\r\n    @Event() interviewComplete: EventEmitter<InterviewCompleteEventData>;\r\n\r\n    /**\r\n     * SDK密钥验证失败事件\r\n     */\r\n    @Event() tokenInvalid: EventEmitter<void>;\r\n\r\n    /**\r\n     * 错误事件\r\n     */\r\n    @Event() someErrorEvent: EventEmitter<ErrorEventDetail>;\r\n\r\n    /**\r\n     * 附件预览模式\r\n     * 'drawer': 在右侧抽屉中预览\r\n     * 'window': 在新窗口中打开\r\n     */\r\n    @Prop() filePreviewMode: 'drawer' | 'window' = 'window';\r\n\r\n    /**\r\n     * 面试模式：text - 文本模式，video - 视频模式\r\n     */\r\n    @Prop() interviewMode: 'text' | 'video' = 'text';\r\n\r\n    /**\r\n     * 录制错误事件\r\n     */\r\n    @Event() recordingError: EventEmitter<RecordingErrorEventData>;\r\n\r\n    /**\r\n     * 是否显示复制按钮\r\n     */\r\n    @Prop() showCopyButton: boolean = true;\r\n\r\n    /**\r\n     * 是否显示点赞点踩按钮\r\n     */\r\n    @Prop() showFeedbackButtons: boolean = true;\r\n\r\n\r\n    @State() selectedFile: File | null = null;\r\n    @State() isUploading: boolean = false;\r\n    @State() uploadedFileInfo: FileUploadResponse | null = null;\r\n    @State() showChatModal: boolean = false;\r\n\r\n    // 使用 @Element 装饰器获取组件的 host 元素\r\n    @Element() hostElement: HTMLElement;\r\n\r\n    @State() jobDescription: string = '';\r\n    @State() isSubmitting: boolean = false;\r\n\r\n    private tokenInvalidListener: () => void;\r\n    private removeErrorListener: () => void;\r\n\r\n    @Watch('token')\r\n    handleTokenChange(newToken: string) {\r\n        // 当传入的 token 变化时，更新 authStore 中的 token\r\n        if (newToken && newToken !== authStore.getToken()) {\r\n            authStore.setToken(newToken);\r\n        }\r\n    }\r\n\r\n\r\n    @Watch('isOpen')\r\n    async handleIsOpenChange(newValue: boolean) {\r\n        if (!newValue) {\r\n            // 重置状态\r\n            this.clearSelectedFile();\r\n            this.showChatModal = false;\r\n            this.jobDescription = '';\r\n\r\n        } else {\r\n            if (this.customInputs && this.customInputs.job_info) {\r\n                this.jobDescription = this.customInputs.job_info;\r\n            }\r\n\r\n            await verifyApiKey(this.token);\r\n\r\n            // 如果同时有 file_url 和 job_info，或者有会话ID，直接显示聊天模态框\r\n            if ((this.customInputs?.file_url && this.customInputs?.job_info) || this.conversationId) {\r\n                this.showChatModal = true;\r\n            }\r\n        }\r\n    }\r\n\r\n    \r\n\r\n    componentWillLoad() {\r\n      \r\n\r\n        // 将 zIndex 存入配置缓存\r\n        if (this.zIndex) {\r\n            configStore.setItem('modal-zIndex', this.zIndex);\r\n        }\r\n\r\n        if (this.token) {\r\n            authStore.setToken(this.token);\r\n        }\r\n\r\n        // 添加全局token无效事件监听器\r\n        this.tokenInvalidListener = () => {\r\n            this.tokenInvalid.emit();\r\n        };\r\n\r\n        // 添加全局错误监听\r\n        this.removeErrorListener = ErrorEventBus.addErrorListener((errorDetail) => {\r\n            this.someErrorEvent.emit(errorDetail);\r\n        });\r\n\r\n        document.addEventListener('pcm-token-invalid', this.tokenInvalidListener);\r\n    }\r\n\r\n    disconnectedCallback() {\r\n        // 组件销毁时移除事件监听器\r\n        document.removeEventListener('pcm-token-invalid', this.tokenInvalidListener);\r\n        // 移除错误监听器\r\n        if (this.removeErrorListener) {\r\n            this.removeErrorListener();\r\n        }\r\n    }\r\n\r\n    private handleClose = () => {\r\n        this.modalClosed.emit();\r\n    };\r\n\r\n    private handleFileChange = (event: Event) => {\r\n        const input = event.target as HTMLInputElement;\r\n        if (input.files && input.files.length > 0) {\r\n            this.selectedFile = input.files[0];\r\n        }\r\n    };\r\n\r\n    private handleUploadClick = () => {\r\n        const fileInput = this.hostElement.shadowRoot?.querySelector('.file-input') as HTMLInputElement;\r\n        fileInput?.click();\r\n    };\r\n\r\n    private clearSelectedFile = () => {\r\n        this.selectedFile = null;\r\n        this.uploadedFileInfo = null;\r\n        const fileInput = this.hostElement.shadowRoot?.querySelector('.file-input') as HTMLInputElement;\r\n        if (fileInput) {\r\n            fileInput.value = '';\r\n        }\r\n    };\r\n\r\n    private async uploadFile() {\r\n        if (!this.selectedFile) return;\r\n\r\n        this.isUploading = true;\r\n\r\n        try {\r\n            // 使用 uploadFileToBackend 工具函数上传文件\r\n            const result = await uploadFileToBackend(this.selectedFile, {\r\n            }, {\r\n                'tags': ['resume']\r\n            });\r\n\r\n            this.uploadedFileInfo = result;\r\n            this.uploadSuccess.emit(result);\r\n        } catch (error) {\r\n            console.error('文件上传错误:', error);\r\n            this.clearSelectedFile();\r\n            SentryReporter.captureError(error, {\r\n                action: 'uploadFile',\r\n                component: 'pcm-mnms-zp-modal',\r\n                title: '文件上传失败'\r\n            });\r\n            ErrorEventBus.emitError({\r\n                error: error,\r\n                message: '文件上传失败，请重试'\r\n            });\r\n        } finally {\r\n            this.isUploading = false;\r\n        }\r\n    }\r\n\r\n    private handleJobDescriptionChange = (event: Event) => {\r\n        const textarea = event.target as HTMLTextAreaElement;\r\n        this.jobDescription = textarea.value;\r\n    };\r\n\r\n    private handleStartInterview = async () => {\r\n        if (!this.selectedFile) {\r\n            alert('请上传简历');\r\n            return;\r\n        }\r\n\r\n        // 如果没有预设的job_info，则需要检查用户输入\r\n        if (!this.customInputs?.job_info && !this.jobDescription.trim()) {\r\n            alert('请输入职位描述');\r\n            return;\r\n        }\r\n\r\n        this.isSubmitting = true;\r\n\r\n        try {\r\n            // 如果还没上传，先上传文件\r\n            if (!this.uploadedFileInfo) {\r\n                await this.uploadFile();\r\n                if (!this.uploadedFileInfo) {\r\n                    this.isSubmitting = false;\r\n                    return; // 上传失败\r\n                }\r\n            }\r\n\r\n            // 直接显示聊天模态框\r\n            this.showChatModal = true;\r\n        } catch (error) {\r\n            console.error('开始面试时出错:', error);\r\n            SentryReporter.captureError(error, {\r\n                action: 'handleStartInterview',\r\n                component: 'pcm-mnms-zp-modal',\r\n                title: '开始面试时出错'\r\n            });\r\n            ErrorEventBus.emitError({\r\n                error: error,\r\n                message: '开始面试时出错，请重试'\r\n            });\r\n        } finally {\r\n            this.isSubmitting = false;\r\n        }\r\n    };\r\n\r\n\r\n    render() {\r\n        if (!this.isOpen) return null;\r\n\r\n        const modalStyle = {\r\n            zIndex: String(this.zIndex)\r\n        };\r\n\r\n\r\n        const containerClass = {\r\n            'modal-container': true,\r\n            'fullscreen': this.fullscreen,\r\n            'pc-layout': true,\r\n        };\r\n\r\n        const overlayClass = {\r\n            'modal-overlay': true,\r\n            'fullscreen-overlay': this.fullscreen\r\n        };\r\n\r\n        // 显示加载状态\r\n        const isLoading = this.conversationId && !this.showChatModal;\r\n\r\n        // 修正这里的逻辑，确保当 customInputs.job_info 存在时，hideJdInput 为 true\r\n        const hideJdInput = Boolean(this.customInputs && this.customInputs.job_info);\r\n        \r\n        // 判断是否隐藏简历上传区域\r\n        const hideResumeUpload = Boolean(this.customInputs && this.customInputs.file_url);\r\n        \r\n        // 判断是否同时提供了file_url和job_info\r\n        const hasFileAndJob = Boolean(this.customInputs?.file_url && this.customInputs?.job_info);\r\n\r\n        return (\r\n            <div class={overlayClass} style={modalStyle}>\r\n                <div class={containerClass}>\r\n                    {this.isShowHeader && (\r\n                        <div class=\"modal-header\">\r\n                            <div class=\"header-left\">\r\n                                {this.icon && <img src={this.icon} class=\"header-icon\" alt=\"应用图标\" />}\r\n                                <div>{this.modalTitle}</div>\r\n                            </div>\r\n                            {this.isNeedClose && (\r\n                                <button class=\"close-button\" onClick={this.handleClose}>\r\n                                    <span>×</span>\r\n                                </button>\r\n                            )}\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* 上传界面 - 仅在不显示聊天模态框且没有会话ID且没有同时提供file_url和job_info时显示 */}\r\n                    {!this.showChatModal && !this.conversationId && !hasFileAndJob && (\r\n                        <div class=\"input-container\">\r\n                            {/* JD输入区域 - 仅在没有customInputs.job_info时显示 */}\r\n                            {!hideJdInput && (\r\n                                <div class=\"jd-input-section\">\r\n                                    <label htmlFor=\"job-description\">请输入职位描述 (JD)</label>\r\n                                    <textarea\r\n                                        id=\"job-description\"\r\n                                        class=\"job-description-textarea\"\r\n                                        placeholder=\"请输入职位描述，包括职责、要求等信息...\"\r\n                                        rows={6}\r\n                                        value={this.jobDescription}\r\n                                        onInput={this.handleJobDescriptionChange}\r\n                                    ></textarea>\r\n                                </div>\r\n                            )}\r\n\r\n                            {/* 简历上传区域 - 仅在没有customInputs.file_url时显示 */}\r\n                            {!hideResumeUpload && (\r\n                                <div class=\"resume-upload-section\">\r\n                                    <label>上传简历</label>\r\n                                    <div class=\"upload-area\" onClick={this.handleUploadClick}>\r\n                                        {this.selectedFile ? (\r\n                                            <div class=\"file-item\">\r\n                                                <div class=\"file-item-content\">\r\n                                                    <span class=\"file-icon\">📝</span>\r\n                                                    <span class=\"file-name\">{this.selectedFile.name}</span>\r\n                                                </div>\r\n                                                <button class=\"remove-file\" onClick={(e) => {\r\n                                                    e.stopPropagation();\r\n                                                    this.clearSelectedFile();\r\n                                                }}>×</button>\r\n                                            </div>\r\n                                        ) : (\r\n                                            <div class=\"upload-placeholder\">\r\n                                                <img src='https://pub.pincaimao.com/static/web/images/home/<USER>'></img>\r\n                                                <p class='upload-text'>点击上传简历</p>\r\n                                                <p class=\"upload-hint\">支持 txt、markdown、pdf、docx、doc、md 格式</p>\r\n                                            </div>\r\n                                        )}\r\n                                    </div>\r\n                                </div>\r\n                            )}\r\n\r\n                            <button\r\n                                class=\"submit-button\"\r\n                                disabled={(!hideResumeUpload && !this.selectedFile) || (!hideJdInput && !this.jobDescription.trim()) || this.isUploading || this.isSubmitting}\r\n                                onClick={this.handleStartInterview}\r\n                            >\r\n                                {this.isUploading ? '上传中...' : this.isSubmitting ? '处理中...' : '开始分析'}\r\n                            </button>\r\n\r\n                            <div class=\"ai-disclaimer\">\r\n                                <p>所有内容均由AI生成仅供参考</p>\r\n                                <p class=\"beian-info\">\r\n                                    <span>中央网信办生成式人工智能服务备案号</span>：\r\n                                    <a href=\"https://www.pincaimao.com\" target=\"_blank\" rel=\"noopener noreferrer\">Hunan-PinCaiMao-202412310003</a>\r\n                                </p>\r\n                            </div>\r\n\r\n                            <input\r\n                                type=\"file\"\r\n                                class=\"file-input\"\r\n                                onChange={this.handleFileChange}\r\n                            />\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* 加载状态 - 在有会话ID但聊天模态框尚未显示时展示 */}\r\n                    {isLoading && (\r\n                        <div class=\"loading-container\">\r\n                            <div class=\"loading-spinner\"></div>\r\n                            <p class=\"loading-text\">正在加载对话...</p>\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* 聊天界面 - 在显示聊天模态框时显示 */}\r\n                    {this.showChatModal && (\r\n                        <div >\r\n                            <pcm-app-chat-modal\r\n                                isOpen={true}\r\n                                modalTitle={this.modalTitle}\r\n                                icon={this.icon}\r\n                                isShowHeader={this.isShowHeader}\r\n                                isNeedClose={this.isShowHeader}\r\n                                fullscreen={this.fullscreen}\r\n                                botId=\"3022316191018907\"\r\n                                conversationId={this.conversationId}\r\n                                defaultQuery={this.defaultQuery}\r\n                                enableVoice={false}\r\n                                filePreviewMode={this.filePreviewMode}\r\n                                showCopyButton={this.showCopyButton}\r\n                                showFeedbackButtons={this.showFeedbackButtons}\r\n                                customInputs={this.conversationId ? {} : {\r\n                                    ...this.customInputs,\r\n                                    file_url: this.customInputs?.file_url || this.uploadedFileInfo?.cos_key,\r\n                                    file_name: this.customInputs?.file_name || this.uploadedFileInfo?.file_name,\r\n                                    job_info: this.customInputs?.job_info || this.jobDescription\r\n                                }}\r\n                                interviewMode={this.interviewMode}\r\n                            ></pcm-app-chat-modal>\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n} "], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAM,oBAAoB;ACA1B,IAAM,YAAY;ICsBL,cAAW,MAAA;;AAIZ;;;sCAAqB;AAKC;;;;AAKL;;;kCAAkB;AAKlC;;;;AAKD;;;;AAKA;;;kCAAkB;AAKlB;;;wCAAwB;AAKxB;;;uCAAuB;AAKN;;;;AAKjB;;;wCAAuB;AAKvB;;;sCAAsB;AAOtB;;;;;wCAAuC,CAAA;AAMtC;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAKA;;;;AAOD;;;;;2CAAuC;AAKvC;;;yCAAkC;AAKjC;;;;AAKD;;;0CAA0B;AAK1B;;;+CAA+B;AAG9B,wCAA4B;AAC5B,uCAAuB;AACvB,4CAA8C;AAC9C,yCAAyB;AAKzB,0CAAyB;AACzB,wCAAwB;AAEzB;AACA;AAqEA,uCAAc,MAAK;AACvB,WAAK,YAAY,KAAI;IACzB;AAEQ,4CAAmB,CAAC,UAAgB;AACxC,YAAM,QAAQ,MAAM;AACpB,UAAI,MAAM,SAAS,MAAM,MAAM,SAAS,GAAG;AACvC,aAAK,eAAe,MAAM,MAAM,CAAC;;IAEzC;AAEQ,6CAAoB,MAAK;;AAC7B,YAAM,aAAY,UAAK,YAAY,eAAjB,mBAA6B,cAAc;AAC7D,6CAAW;IACf;AAEQ,6CAAoB,MAAK;;AAC7B,WAAK,eAAe;AACpB,WAAK,mBAAmB;AACxB,YAAM,aAAY,UAAK,YAAY,eAAjB,mBAA6B,cAAc;AAC7D,UAAI,WAAW;AACX,kBAAU,QAAQ;;IAE1B;AAiCQ,sDAA6B,CAAC,UAAgB;AAClD,YAAM,WAAW,MAAM;AACvB,WAAK,iBAAiB,SAAS;IACnC;AAEQ,gDAAuB,YAAW;;AACtC,UAAI,CAAC,KAAK,cAAc;AACpB,cAAM,OAAO;AACb;;AAIJ,UAAI,GAAC,UAAK,iBAAL,mBAAmB,aAAY,CAAC,KAAK,eAAe,KAAI,GAAI;AAC7D,cAAM,SAAS;AACf;;AAGJ,WAAK,eAAe;AAEpB,UAAI;AAEA,YAAI,CAAC,KAAK,kBAAkB;AACxB,gBAAM,KAAK,WAAU;AACrB,cAAI,CAAC,KAAK,kBAAkB;AACxB,iBAAK,eAAe;AACpB;;;AAKR,aAAK,gBAAgB;eAChB,OAAO;AACZ,gBAAQ,MAAM,YAAY,KAAK;AAC/B,uBAAe,aAAa,OAAO;UAC/B,QAAQ;UACR,WAAW;UACX,OAAO;QACV,CAAA;AACD,sBAAc,UAAU;UACpB;UACA,SAAS;QACZ,CAAA;;AAED,aAAK,eAAe;;IAE5B;;;;;;;;;;;;;;EAvKA,kBAAkB,UAAgB;AAE9B,QAAI,YAAY,aAAa,UAAU,SAAQ,GAAI;AAC/C,gBAAU,SAAS,QAAQ;;;EAMnC,MAAM,mBAAmB,UAAiB;;AACtC,QAAI,CAAC,UAAU;AAEX,WAAK,kBAAiB;AACtB,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;WAEnB;AACH,UAAI,KAAK,gBAAgB,KAAK,aAAa,UAAU;AACjD,aAAK,iBAAiB,KAAK,aAAa;;AAG5C,YAAM,aAAa,KAAK,KAAK;AAG7B,YAAK,UAAK,iBAAL,mBAAmB,eAAY,UAAK,iBAAL,mBAAmB,aAAa,KAAK,gBAAgB;AACrF,aAAK,gBAAgB;;;;EAOjC,oBAAiB;AAIb,QAAI,KAAK,QAAQ;AACb,kBAAY,QAAQ,gBAAgB,KAAK,MAAM;;AAGnD,QAAI,KAAK,OAAO;AACZ,gBAAU,SAAS,KAAK,KAAK;;AAIjC,SAAK,uBAAuB,MAAK;AAC7B,WAAK,aAAa,KAAI;IAC1B;AAGA,SAAK,sBAAsB,cAAc,iBAAiB,CAAC,gBAAe;AACtE,WAAK,eAAe,KAAK,WAAW;IACxC,CAAC;AAED,aAAS,iBAAiB,qBAAqB,KAAK,oBAAoB;;EAG5E,uBAAoB;AAEhB,aAAS,oBAAoB,qBAAqB,KAAK,oBAAoB;AAE3E,QAAI,KAAK,qBAAqB;AAC1B,WAAK,oBAAmB;;;EA6BxB,MAAM,aAAU;AACpB,QAAI,CAAC,KAAK;AAAc;AAExB,SAAK,cAAc;AAEnB,QAAI;AAEA,YAAM,SAAS,MAAM,oBAAoB,KAAK,cAAc,CAAA,GACzD;QACC,QAAQ,CAAC,QAAQ;MACpB,CAAA;AAED,WAAK,mBAAmB;AACxB,WAAK,cAAc,KAAK,MAAM;aACzB,OAAO;AACZ,cAAQ,MAAM,WAAW,KAAK;AAC9B,WAAK,kBAAiB;AACtB,qBAAe,aAAa,OAAO;QAC/B,QAAQ;QACR,WAAW;QACX,OAAO;MACV,CAAA;AACD,oBAAc,UAAU;QACpB;QACA,SAAS;MACZ,CAAA;;AAED,WAAK,cAAc;;;EAoD3B,SAAM;;AACF,QAAI,CAAC,KAAK;AAAQ,aAAO;AAEzB,UAAM,aAAa;MACf,QAAQ,OAAO,KAAK,MAAM;;AAI9B,UAAM,iBAAiB;MACnB,mBAAmB;MACnB,cAAc,KAAK;MACnB,aAAa;;AAGjB,UAAM,eAAe;MACjB,iBAAiB;MACjB,sBAAsB,KAAK;;AAI/B,UAAM,YAAY,KAAK,kBAAkB,CAAC,KAAK;AAG/C,UAAM,cAAc,QAAQ,KAAK,gBAAgB,KAAK,aAAa,QAAQ;AAG3E,UAAM,mBAAmB,QAAQ,KAAK,gBAAgB,KAAK,aAAa,QAAQ;AAGhF,UAAM,gBAAgB,UAAQ,UAAK,iBAAL,mBAAmB,eAAY,UAAK,iBAAL,mBAAmB,SAAQ;AAExF,WACI,EAAA,OAAA,EAAK,OAAO,cAAc,OAAO,WAAU,GACvC,EAAK,OAAA,EAAA,OAAO,eAAc,GACrB,KAAK,gBACF,EAAK,OAAA,EAAA,OAAM,eAAc,GACrB,EAAK,OAAA,EAAA,OAAM,cAAa,GACnB,KAAK,QAAQ,EAAK,OAAA,EAAA,KAAK,KAAK,MAAM,OAAM,eAAc,KAAI,OAAM,CAAG,GACpE,EAAA,OAAA,MAAM,KAAK,UAAU,CAAO,GAE/B,KAAK,eACF,EAAQ,UAAA,EAAA,OAAM,gBAAe,SAAS,KAAK,YAAW,GAClD,EAAc,QAAA,MAAA,GAAA,CAAA,CAErB,GAKR,CAAC,KAAK,iBAAiB,CAAC,KAAK,kBAAkB,CAAC,iBAC7C,EAAK,OAAA,EAAA,OAAM,kBAAiB,GAEvB,CAAC,eACE,EAAK,OAAA,EAAA,OAAM,mBAAkB,GACzB,EAAO,SAAA,EAAA,SAAQ,kBAAiB,GAAqB,cAAA,GACrD,EAAA,YAAA,EACI,IAAG,mBACH,OAAM,4BACN,aAAY,yBACZ,MAAM,GACN,OAAO,KAAK,gBACZ,SAAS,KAAK,2BAA0B,CAAA,CAChC,GAKnB,CAAC,oBACE,EAAK,OAAA,EAAA,OAAM,wBAAuB,GAC9B,EAAmB,SAAA,MAAA,MAAA,GACnB,EAAK,OAAA,EAAA,OAAM,eAAc,SAAS,KAAK,kBAAiB,GACnD,KAAK,eACF,EAAK,OAAA,EAAA,OAAM,YAAW,GAClB,EAAK,OAAA,EAAA,OAAM,oBAAmB,GAC1B,EAAM,QAAA,EAAA,OAAM,YAAW,GAAU,IAAA,GACjC,EAAM,QAAA,EAAA,OAAM,YAAW,GAAE,KAAK,aAAa,IAAI,CAAQ,GAE3D,EAAQ,UAAA,EAAA,OAAM,eAAc,SAAS,CAAC,MAAK;AACvC,QAAE,gBAAe;AACjB,WAAK,kBAAiB;IAC1B,EAAC,GAAA,GAAA,CAAY,IAGjB,EAAA,OAAA,EAAK,OAAM,qBAAoB,GAC3B,EAAK,OAAA,EAAA,KAAI,gEAA+D,CAAO,GAC/E,EAAG,KAAA,EAAA,OAAM,cAAa,GAAW,QAAA,GACjC,EAAG,KAAA,EAAA,OAAM,cAAa,GAAA,oCAAA,CAAuC,CAEpE,CACC,GAId,EAAA,UAAA,EACI,OAAM,iBACN,UAAW,CAAC,oBAAoB,CAAC,KAAK,gBAAkB,CAAC,eAAe,CAAC,KAAK,eAAe,KAAI,KAAO,KAAK,eAAe,KAAK,cACjI,SAAS,KAAK,qBAAoB,GAEjC,KAAK,cAAc,WAAW,KAAK,eAAe,WAAW,MAAM,GAGxE,EAAK,OAAA,EAAA,OAAM,gBAAe,GACtB,EAAqB,KAAA,MAAA,gBAAA,GACrB,EAAG,KAAA,EAAA,OAAM,aAAY,GACjB,EAA8B,QAAA,MAAA,mBAAA,GAAA,KAC9B,EAAA,KAAA,EAAG,MAAK,6BAA4B,QAAO,UAAS,KAAI,sBAAqB,GAAA,8BAAA,CAAiC,CAC9G,GAGR,EAAA,SAAA,EACI,MAAK,QACL,OAAM,cACN,UAAU,KAAK,iBAAgB,CAAA,CACjC,GAKT,aACG,EAAK,OAAA,EAAA,OAAM,oBAAmB,GAC1B,EAAK,OAAA,EAAA,OAAM,kBAAiB,CAAO,GACnC,EAAA,KAAA,EAAG,OAAM,eAAc,GAAA,WAAA,CAAc,GAK5C,KAAK,iBACF,EAAA,OAAA,MACI,EAAA,sBAAA,EACI,QAAQ,MACR,YAAY,KAAK,YACjB,MAAM,KAAK,MACX,cAAc,KAAK,cACnB,aAAa,KAAK,cAClB,YAAY,KAAK,YACjB,OAAM,oBACN,gBAAgB,KAAK,gBACrB,cAAc,KAAK,cACnB,aAAa,OACb,iBAAiB,KAAK,iBACtB,gBAAgB,KAAK,gBACrB,qBAAqB,KAAK,qBAC1B,cAAc,KAAK,iBAAiB,CAAA,IAAK;MACrC,GAAG,KAAK;MACR,YAAU,UAAK,iBAAL,mBAAmB,eAAY,UAAK,qBAAL,mBAAuB;MAChE,aAAW,UAAK,iBAAL,mBAAmB,gBAAa,UAAK,qBAAL,mBAAuB;MAClE,YAAU,UAAK,iBAAL,mBAAmB,aAAY,KAAK;IACjD,GACD,eAAe,KAAK,cAAa,CACf,CAAA,CAE7B,CACC;;;;;;;;;;", "names": []}