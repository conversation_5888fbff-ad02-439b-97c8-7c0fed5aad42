{"version": 3, "sources": ["../../.pnpm/@stencil+vue-output-target@_8c3ef0d9b442a8f65855276f8885f5c1/node_modules/@stencil/vue-output-target/dist/runtime.js", "../../.pnpm/pcm-agents-vue@0.2.9_@stenc_44888d2b0777e20b476c307c7b5c9b06/node_modules/pcm-agents-vue/lib/components.ts"], "sourcesContent": ["import { defineComponent, useSlots, createSSRApp, compile, ref, onMounted, getCurrentInstance, inject, h, withDirectives } from 'vue';\n\nconst LOG_PREFIX = '[vue-output-target]';\n/**\n * returns true if the value is a primitive, e.g. string, number, boolean\n * @param value - the value to check\n * @returns true if the value is a primitive, false otherwise\n */\nfunction isPrimitive(value) {\n    return typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean';\n}\nfunction defineStencilSSRComponent(options) {\n    return defineComponent({\n        async setup(props, context) {\n            /**\n             * resolve light dom into a string\n             */\n            const slots = useSlots();\n            let renderedLightDom = '';\n            if (typeof slots.default === 'function') {\n                const ssrLightDom = createSSRApp({ render: () => slots.default() });\n                const { renderToString: vueRenderToString } = await import('vue/server-renderer');\n                renderedLightDom = await vueRenderToString(ssrLightDom, { context });\n            }\n            /**\n             * compose element props into a string\n             */\n            let stringProps = '';\n            for (const [key, value] of Object.entries(props)) {\n                if (typeof value === 'undefined') {\n                    continue;\n                }\n                /**\n                 * Stencils metadata tells us which properties can be serialized\n                 */\n                const propName = options.props?.[key][1];\n                const propValue = isPrimitive(value)\n                    ? typeof value === 'boolean'\n                        ? /**\n                           * omit boolean properties that are false all together\n                           */\n                            value\n                                ? '\"true\"'\n                                : undefined\n                        : `\"${value}\"`\n                    : Array.isArray(value) && value.every(isPrimitive)\n                        ? JSON.stringify(value)\n                        : undefined;\n                if (!propName || !propValue) {\n                    console.warn(`${LOG_PREFIX} ignore component property \"${key}\" for ${options.tagName} ` +\n                        \"- property type is unknown or not a primitive and can't be serialized\");\n                    continue;\n                }\n                stringProps += ` ${propName}=${propValue}`;\n            }\n            /**\n             * transform component into Declarative Shadow DOM by lazy loading the hydrate module\n             */\n            const toSerialize = `<${options.tagName}${stringProps}>${renderedLightDom}</${options.tagName}>`;\n            const { renderToString } = await options.hydrateModule;\n            const { html } = await renderToString(toSerialize, {\n                fullDocument: false,\n                serializeShadowRoot: true,\n            });\n            if (!html) {\n                throw new Error(`'${options.tagName}' component did not render anything.`);\n            }\n            return compile(html\n                /**\n                 * by default Vue strips out the <style> tag, so this little trick\n                 * makes it work by wrapping it in a component tag\n                 */\n                .replace('<style>', `<component :is=\"'style'\">`)\n                .replace('</style>', '</component>'), {\n                comments: true,\n                isCustomElement: (tag) => tag === options.tagName,\n            });\n        },\n        props: Object.entries(options.props || {}).reduce((acc, [key, value]) => {\n            acc[key] = value[0];\n            return acc;\n        }, {}),\n        /**\n         * the template tags can be arbitrary as they will be replaced with above compiled template\n         */\n        template: '<div></div>',\n    });\n}\n\nconst UPDATE_VALUE_EVENT = 'update:modelValue';\nconst MODEL_VALUE = 'modelValue';\nconst ROUTER_LINK_VALUE = 'routerLink';\nconst NAV_MANAGER = 'navManager';\nconst ROUTER_PROP_PREFIX = 'router';\nconst ARIA_PROP_PREFIX = 'aria';\n/**\n * Starting in Vue 3.1.0, all properties are\n * added as keys to the props object, even if\n * they are not being used. In order to correctly\n * account for both value props and v-model props,\n * we need to check if the key exists for Vue <3.1.0\n * and then check if it is not undefined for Vue >= 3.1.0.\n * See https://github.com/vuejs/vue-next/issues/3889\n */\nconst EMPTY_PROP = Symbol();\nconst DEFAULT_EMPTY_PROP = { default: EMPTY_PROP };\nconst getComponentClasses = (classes) => {\n    return classes?.split(' ') || [];\n};\nconst getElementClasses = (ref, componentClasses, defaultClasses = []) => {\n    return [...Array.from(ref.value?.classList || []), ...defaultClasses].filter((c, i, self) => !componentClasses.has(c) && self.indexOf(c) === i);\n};\n/**\n * Create a callback to define a Vue component wrapper around a Web Component.\n *\n * @prop name - The component tag name (i.e. `ion-button`)\n * @prop componentProps - An array of properties on the\n * component. These usually match up with the @Prop definitions\n * in each component's TSX file.\n * @prop emitProps - An array of for event listener on the Component.\n * these usually match up with the @Event definitions\n * in each compont's TSX file.\n * @prop customElement - An option custom element instance to pass\n * to customElements.define. Only set if `includeImportCustomElements: true` in your config.\n * @prop modelProp - The prop that v-model binds to (i.e. value)\n * @prop modelUpdateEvent - The event that is fired from your Web Component when the value changes (i.e. ionChange)\n */\nconst defineContainer = (name, defineCustomElement, componentProps = [], emitProps = [], modelProp, modelUpdateEvent) => {\n    /**\n     * Create a Vue component wrapper around a Web Component.\n     * Note: The `props` here are not all properties on a component.\n     * They refer to whatever properties are set on an instance of a component.\n     */\n    if (defineCustomElement !== undefined) {\n        defineCustomElement();\n    }\n    const emits = emitProps;\n    const props = [ROUTER_LINK_VALUE, ...componentProps].reduce((acc, prop) => {\n        acc[prop] = DEFAULT_EMPTY_PROP;\n        return acc;\n    }, {});\n    if (modelProp) {\n        emits.push(UPDATE_VALUE_EVENT);\n        props[MODEL_VALUE] = DEFAULT_EMPTY_PROP;\n    }\n    return defineComponent((props, { attrs, slots, emit }) => {\n        let modelPropValue = modelProp ? props[modelProp] : undefined;\n        const containerRef = ref();\n        const classes = new Set(getComponentClasses(attrs.class));\n        onMounted(() => {\n            /**\n             * we register the event emmiter for @Event definitions\n             * so we can use @event\n             */\n            emitProps.forEach((eventName) => {\n                containerRef.value?.addEventListener(eventName, (e) => {\n                    emit(eventName, e);\n                });\n            });\n        });\n        /**\n         * This directive is responsible for updating any reactive\n         * reference associated with v-model on the component.\n         * This code must be run inside of the \"created\" callback.\n         * Since the following listener callbacks as well as any potential\n         * event callback defined in the developer's app are set on\n         * the same element, we need to make sure the following callbacks\n         * are set first so they fire first. If the developer's callback fires first\n         * then the reactive reference will not have been updated yet.\n         */\n        const vModelDirective = {\n            created: (el) => {\n                const eventsNames = (Array.isArray(modelUpdateEvent) ? modelUpdateEvent : [modelUpdateEvent]).map((ev) => ev.replace(/-([a-z])/g, (g) => g[1].toUpperCase()));\n                eventsNames.forEach((eventName) => {\n                    el.addEventListener(eventName, (e) => {\n                        /**\n                         * Only update the v-model binding if the event's target is the element we are\n                         * listening on. For example, Component A could emit ionChange, but it could also\n                         * have a descendant Component B that also emits ionChange. We only want to update\n                         * the v-model for Component A when ionChange originates from that element and not\n                         * when ionChange bubbles up from Component B.\n                         */\n                        if (e.target.tagName === el.tagName && modelProp) {\n                            modelPropValue = (e?.target)[modelProp];\n                            emit(UPDATE_VALUE_EVENT, modelPropValue);\n                        }\n                    });\n                });\n            },\n        };\n        const currentInstance = getCurrentInstance();\n        const hasRouter = currentInstance?.appContext?.provides[NAV_MANAGER];\n        const navManager = hasRouter ? inject(NAV_MANAGER) : undefined;\n        const handleRouterLink = (ev) => {\n            const { routerLink } = props;\n            if (routerLink === EMPTY_PROP)\n                return;\n            if (navManager !== undefined) {\n                /**\n                 * This prevents the browser from\n                 * performing a page reload when pressing\n                 * an Ionic component with routerLink.\n                 * The page reload interferes with routing\n                 * and causes ion-back-button to disappear\n                 * since the local history is wiped on reload.\n                 */\n                ev.preventDefault();\n                let navigationPayload = { event: ev };\n                for (const key in props) {\n                    const value = props[key];\n                    if (props.hasOwnProperty(key) && key.startsWith(ROUTER_PROP_PREFIX) && value !== EMPTY_PROP) {\n                        navigationPayload[key] = value;\n                    }\n                }\n                navManager.navigate(navigationPayload);\n            }\n            else {\n                console.warn('Tried to navigate, but no router was found. Make sure you have mounted Vue Router.');\n            }\n        };\n        return () => {\n            modelPropValue = props[modelProp];\n            getComponentClasses(attrs.class).forEach((value) => {\n                classes.add(value);\n            });\n            // @ts-expect-error\n            const oldClick = props.onClick;\n            const handleClick = (ev) => {\n                if (oldClick !== undefined) {\n                    oldClick(ev);\n                }\n                if (!ev.defaultPrevented) {\n                    handleRouterLink(ev);\n                }\n            };\n            const propsToAdd = {\n                ref: containerRef,\n                class: getElementClasses(containerRef, classes),\n                onClick: handleClick,\n            };\n            /**\n             * We can use Object.entries here\n             * to avoid the hasOwnProperty check,\n             * but that would require 2 iterations\n             * where as this only requires 1.\n             */\n            for (const key in props) {\n                const value = props[key];\n                if ((props.hasOwnProperty(key) && value !== EMPTY_PROP) || key.startsWith(ARIA_PROP_PREFIX)) {\n                    propsToAdd[key] = value;\n                }\n                /**\n                 * register event handlers on the component\n                 */\n                const eventHandlerKey = 'on' + key.slice(0, 1).toUpperCase() + key.slice(1);\n                const eventHandler = attrs[eventHandlerKey];\n                if (containerRef.value && attrs.hasOwnProperty(eventHandlerKey) && 'addEventListener' in containerRef.value) {\n                    containerRef.value.addEventListener(key, eventHandler);\n                }\n            }\n            if (modelProp) {\n                /**\n                 * If form value property was set using v-model\n                 * then we should use that value.\n                 * Otherwise, check to see if form value property\n                 * was set as a static value (i.e. no v-model).\n                 */\n                if (props[MODEL_VALUE] !== EMPTY_PROP) {\n                    propsToAdd[modelProp] = props[MODEL_VALUE];\n                }\n                else if (modelPropValue !== EMPTY_PROP) {\n                    propsToAdd[modelProp] = modelPropValue;\n                }\n            }\n            // If router link is defined, add href to props\n            // in order to properly render an anchor tag inside\n            // of components that should become activatable and\n            // focusable with router link.\n            if (ROUTER_LINK_VALUE in props && props[ROUTER_LINK_VALUE] !== EMPTY_PROP) {\n                propsToAdd.href = props[ROUTER_LINK_VALUE];\n            }\n            /**\n             * vModelDirective is only needed on components that support v-model.\n             * As a result, we conditionally call withDirectives with v-model components.\n             */\n            const node = h(name, propsToAdd, slots.default && slots.default());\n            return modelProp === undefined ? node : withDirectives(node, [[vModelDirective]]);\n        };\n    }, {\n        name,\n        props,\n        emits,\n    });\n};\n\nexport { defineContainer, defineStencilSSRComponent };\n", null], "mappings": ";;;;;;;;;;;;AAyFA,IAAM,qBAAqB;AAC3B,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,cAAc;AACpB,IAAM,qBAAqB;AAC3B,IAAM,mBAAmB;AAUzB,IAAM,aAAa,OAAO;AAC1B,IAAM,qBAAqB,EAAE,SAAS,WAAW;AACjD,IAAM,sBAAsB,CAAC,YAAY;AACrC,UAAO,mCAAS,MAAM,SAAQ,CAAC;AACnC;AACA,IAAM,oBAAoB,CAACA,MAAK,kBAAkB,iBAAiB,CAAC,MAAM;AA7G1E;AA8GI,SAAO,CAAC,GAAG,MAAM,OAAK,KAAAA,KAAI,UAAJ,mBAAW,cAAa,CAAC,CAAC,GAAG,GAAG,cAAc,EAAE,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,iBAAiB,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,MAAM,CAAC;AAClJ;AAgBA,IAAM,kBAAkB,CAAC,MAAM,qBAAqB,iBAAiB,CAAC,GAAG,YAAY,CAAC,GAAG,WAAW,qBAAqB;AAMrH,MAAI,wBAAwB,QAAW;AACnC,wBAAoB;AAAA,EACxB;AACA,QAAM,QAAQ;AACd,QAAM,QAAQ,CAAC,mBAAmB,GAAG,cAAc,EAAE,OAAO,CAAC,KAAK,SAAS;AACvE,QAAI,IAAI,IAAI;AACZ,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,MAAI,WAAW;AACX,UAAM,KAAK,kBAAkB;AAC7B,UAAM,WAAW,IAAI;AAAA,EACzB;AACA,SAAO,gBAAgB,CAACC,QAAO,EAAE,OAAO,OAAO,KAAK,MAAM;AAjJ9D;AAkJQ,QAAI,iBAAiB,YAAYA,OAAM,SAAS,IAAI;AACpD,UAAM,eAAe,IAAI;AACzB,UAAM,UAAU,IAAI,IAAI,oBAAoB,MAAM,KAAK,CAAC;AACxD,cAAU,MAAM;AAKZ,gBAAU,QAAQ,CAAC,cAAc;AA1J7C,YAAAC;AA2JgB,SAAAA,MAAA,aAAa,UAAb,gBAAAA,IAAoB,iBAAiB,WAAW,CAAC,MAAM;AACnD,eAAK,WAAW,CAAC;AAAA,QACrB;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAWD,UAAM,kBAAkB;AAAA,MACpB,SAAS,CAAC,OAAO;AACb,cAAM,eAAe,MAAM,QAAQ,gBAAgB,IAAI,mBAAmB,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,GAAG,QAAQ,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;AAC5J,oBAAY,QAAQ,CAAC,cAAc;AAC/B,aAAG,iBAAiB,WAAW,CAAC,MAAM;AAQlC,gBAAI,EAAE,OAAO,YAAY,GAAG,WAAW,WAAW;AAC9C,gCAAkB,uBAAG,QAAQ,SAAS;AACtC,mBAAK,oBAAoB,cAAc;AAAA,YAC3C;AAAA,UACJ,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,kBAAkB,mBAAmB;AAC3C,UAAM,aAAY,wDAAiB,eAAjB,mBAA6B,SAAS;AACxD,UAAM,aAAa,YAAY,OAAO,WAAW,IAAI;AACrD,UAAM,mBAAmB,CAAC,OAAO;AAC7B,YAAM,EAAE,WAAW,IAAID;AACvB,UAAI,eAAe;AACf;AACJ,UAAI,eAAe,QAAW;AAS1B,WAAG,eAAe;AAClB,YAAI,oBAAoB,EAAE,OAAO,GAAG;AACpC,mBAAW,OAAOA,QAAO;AACrB,gBAAM,QAAQA,OAAM,GAAG;AACvB,cAAIA,OAAM,eAAe,GAAG,KAAK,IAAI,WAAW,kBAAkB,KAAK,UAAU,YAAY;AACzF,8BAAkB,GAAG,IAAI;AAAA,UAC7B;AAAA,QACJ;AACA,mBAAW,SAAS,iBAAiB;AAAA,MACzC,OACK;AACD,gBAAQ,KAAK,oFAAoF;AAAA,MACrG;AAAA,IACJ;AACA,WAAO,MAAM;AACT,uBAAiBA,OAAM,SAAS;AAChC,0BAAoB,MAAM,KAAK,EAAE,QAAQ,CAAC,UAAU;AAChD,gBAAQ,IAAI,KAAK;AAAA,MACrB,CAAC;AAED,YAAM,WAAWA,OAAM;AACvB,YAAM,cAAc,CAAC,OAAO;AACxB,YAAI,aAAa,QAAW;AACxB,mBAAS,EAAE;AAAA,QACf;AACA,YAAI,CAAC,GAAG,kBAAkB;AACtB,2BAAiB,EAAE;AAAA,QACvB;AAAA,MACJ;AACA,YAAM,aAAa;AAAA,QACf,KAAK;AAAA,QACL,OAAO,kBAAkB,cAAc,OAAO;AAAA,QAC9C,SAAS;AAAA,MACb;AAOA,iBAAW,OAAOA,QAAO;AACrB,cAAM,QAAQA,OAAM,GAAG;AACvB,YAAKA,OAAM,eAAe,GAAG,KAAK,UAAU,cAAe,IAAI,WAAW,gBAAgB,GAAG;AACzF,qBAAW,GAAG,IAAI;AAAA,QACtB;AAIA,cAAM,kBAAkB,OAAO,IAAI,MAAM,GAAG,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC;AAC1E,cAAM,eAAe,MAAM,eAAe;AAC1C,YAAI,aAAa,SAAS,MAAM,eAAe,eAAe,KAAK,sBAAsB,aAAa,OAAO;AACzG,uBAAa,MAAM,iBAAiB,KAAK,YAAY;AAAA,QACzD;AAAA,MACJ;AACA,UAAI,WAAW;AAOX,YAAIA,OAAM,WAAW,MAAM,YAAY;AACnC,qBAAW,SAAS,IAAIA,OAAM,WAAW;AAAA,QAC7C,WACS,mBAAmB,YAAY;AACpC,qBAAW,SAAS,IAAI;AAAA,QAC5B;AAAA,MACJ;AAKA,UAAI,qBAAqBA,UAASA,OAAM,iBAAiB,MAAM,YAAY;AACvE,mBAAW,OAAOA,OAAM,iBAAiB;AAAA,MAC7C;AAKA,YAAM,OAAO,EAAE,MAAM,YAAY,MAAM,WAAW,MAAM,QAAQ,CAAC;AACjE,aAAO,cAAc,SAAY,OAAO,eAAe,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC;AAAA,IACpF;AAAA,EACJ,GAAG;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACL;;;AC3RO,IAAM,uBAAoF,gBAA0C,2BAA2B,QAAW;EAC/K;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,kBAA0E,gBAAqC,sBAAsB,QAAW;EAC3J;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,YAA8D,gBAA+B,cAAc,QAAW;EACjI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,UAA0D,gBAA6B,YAAY,QAAW;EACzH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;CACD;AAGM,IAAM,iBAAwE,gBAAoC,oBAAoB,QAAW;EACtJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;CACD;AAGM,IAAM,YAA8D,gBAA+B,cAAc,QAAW;EACjI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;CACD;AAGM,IAAM,iBAAwE,gBAAoC,qBAAqB,QAAW;EACvJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,eAAoE,gBAAkC,kBAAkB,QAAW;EAC9I;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,eAAoE,gBAAkC,kBAAkB,QAAW;EAC9I;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,aAAgE,gBAAgC,gBAAgB,QAAW;EACtI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,eAAoE,gBAAkC,kBAAkB,QAAW;EAC9I;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,aAAgE,gBAAgC,eAAe,QAAW;EACrI;EACA;EACA;CACD;AAGM,IAAM,eAAoE,gBAAkC,kBAAkB,QAAW;EAC9I;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,eAAoE,gBAAkC,kBAAkB,QAAW;EAC9I;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,oBAA8E,gBAAuC,wBAAwB,QAAW;EACnK;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,iBAAwE,gBAAoC,qBAAqB,QAAW;EACvJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,eAAoE,gBAAkC,kBAAkB,QAAW;EAC9I;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,gBAAsE,gBAAmC,mBAAmB,QAAW;EAClJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,kBAA0E,gBAAqC,sBAAsB,QAAW;EAC3J;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;CACD;AAGM,IAAM,eAAoE,gBAAkC,kBAAkB,QAAW;EAC9I;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;GACC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;CACD;", "names": ["ref", "props", "_a"]}