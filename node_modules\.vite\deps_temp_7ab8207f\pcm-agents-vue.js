import {
  defineComponent,
  getCurrentInstance,
  h,
  inject,
  onMounted,
  ref,
  withDirectives
} from "./chunk-I3OM2ODF.js";
import "./chunk-5H7I2G36.js";

// node_modules/.pnpm/@stencil+vue-output-target@_8c3ef0d9b442a8f65855276f8885f5c1/node_modules/@stencil/vue-output-target/dist/runtime.js
var UPDATE_VALUE_EVENT = "update:modelValue";
var MODEL_VALUE = "modelValue";
var ROUTER_LINK_VALUE = "routerLink";
var NAV_MANAGER = "navManager";
var ROUTER_PROP_PREFIX = "router";
var ARIA_PROP_PREFIX = "aria";
var EMPTY_PROP = Symbol();
var DEFAULT_EMPTY_PROP = { default: EMPTY_PROP };
var getComponentClasses = (classes) => {
  return (classes == null ? void 0 : classes.split(" ")) || [];
};
var getElementClasses = (ref2, componentClasses, defaultClasses = []) => {
  var _a;
  return [...Array.from(((_a = ref2.value) == null ? void 0 : _a.classList) || []), ...defaultClasses].filter((c, i, self) => !componentClasses.has(c) && self.indexOf(c) === i);
};
var defineContainer = (name, defineCustomElement, componentProps = [], emitProps = [], modelProp, modelUpdateEvent) => {
  if (defineCustomElement !== void 0) {
    defineCustomElement();
  }
  const emits = emitProps;
  const props = [ROUTER_LINK_VALUE, ...componentProps].reduce((acc, prop) => {
    acc[prop] = DEFAULT_EMPTY_PROP;
    return acc;
  }, {});
  if (modelProp) {
    emits.push(UPDATE_VALUE_EVENT);
    props[MODEL_VALUE] = DEFAULT_EMPTY_PROP;
  }
  return defineComponent((props2, { attrs, slots, emit }) => {
    var _a;
    let modelPropValue = modelProp ? props2[modelProp] : void 0;
    const containerRef = ref();
    const classes = new Set(getComponentClasses(attrs.class));
    onMounted(() => {
      emitProps.forEach((eventName) => {
        var _a2;
        (_a2 = containerRef.value) == null ? void 0 : _a2.addEventListener(eventName, (e) => {
          emit(eventName, e);
        });
      });
    });
    const vModelDirective = {
      created: (el) => {
        const eventsNames = (Array.isArray(modelUpdateEvent) ? modelUpdateEvent : [modelUpdateEvent]).map((ev) => ev.replace(/-([a-z])/g, (g) => g[1].toUpperCase()));
        eventsNames.forEach((eventName) => {
          el.addEventListener(eventName, (e) => {
            if (e.target.tagName === el.tagName && modelProp) {
              modelPropValue = (e == null ? void 0 : e.target)[modelProp];
              emit(UPDATE_VALUE_EVENT, modelPropValue);
            }
          });
        });
      }
    };
    const currentInstance = getCurrentInstance();
    const hasRouter = (_a = currentInstance == null ? void 0 : currentInstance.appContext) == null ? void 0 : _a.provides[NAV_MANAGER];
    const navManager = hasRouter ? inject(NAV_MANAGER) : void 0;
    const handleRouterLink = (ev) => {
      const { routerLink } = props2;
      if (routerLink === EMPTY_PROP)
        return;
      if (navManager !== void 0) {
        ev.preventDefault();
        let navigationPayload = { event: ev };
        for (const key in props2) {
          const value = props2[key];
          if (props2.hasOwnProperty(key) && key.startsWith(ROUTER_PROP_PREFIX) && value !== EMPTY_PROP) {
            navigationPayload[key] = value;
          }
        }
        navManager.navigate(navigationPayload);
      } else {
        console.warn("Tried to navigate, but no router was found. Make sure you have mounted Vue Router.");
      }
    };
    return () => {
      modelPropValue = props2[modelProp];
      getComponentClasses(attrs.class).forEach((value) => {
        classes.add(value);
      });
      const oldClick = props2.onClick;
      const handleClick = (ev) => {
        if (oldClick !== void 0) {
          oldClick(ev);
        }
        if (!ev.defaultPrevented) {
          handleRouterLink(ev);
        }
      };
      const propsToAdd = {
        ref: containerRef,
        class: getElementClasses(containerRef, classes),
        onClick: handleClick
      };
      for (const key in props2) {
        const value = props2[key];
        if (props2.hasOwnProperty(key) && value !== EMPTY_PROP || key.startsWith(ARIA_PROP_PREFIX)) {
          propsToAdd[key] = value;
        }
        const eventHandlerKey = "on" + key.slice(0, 1).toUpperCase() + key.slice(1);
        const eventHandler = attrs[eventHandlerKey];
        if (containerRef.value && attrs.hasOwnProperty(eventHandlerKey) && "addEventListener" in containerRef.value) {
          containerRef.value.addEventListener(key, eventHandler);
        }
      }
      if (modelProp) {
        if (props2[MODEL_VALUE] !== EMPTY_PROP) {
          propsToAdd[modelProp] = props2[MODEL_VALUE];
        } else if (modelPropValue !== EMPTY_PROP) {
          propsToAdd[modelProp] = modelPropValue;
        }
      }
      if (ROUTER_LINK_VALUE in props2 && props2[ROUTER_LINK_VALUE] !== EMPTY_PROP) {
        propsToAdd.href = props2[ROUTER_LINK_VALUE];
      }
      const node = h(name, propsToAdd, slots.default && slots.default());
      return modelProp === void 0 ? node : withDirectives(node, [[vModelDirective]]);
    };
  }, {
    name,
    props,
    emits
  });
};

// node_modules/.pnpm/pcm-agents-vue@0.2.9_@stenc_44888d2b0777e20b476c307c7b5c9b06/node_modules/pcm-agents-vue/dist/components.js
var Pcm1zhanshiMnmsModal = defineContainer("pcm-1zhanshi-mnms-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "interviewMode",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent",
  "recordingError"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent",
  "recordingError"
]);
var PcmAppChatModal = defineContainer("pcm-app-chat-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "maxRecordingTime",
  "countdownWarningTime",
  "fullscreen",
  "enableTTS",
  "enableVoice",
  "interviewMode",
  "customInputs",
  "botId",
  "maxAudioRecordingTime",
  "userAvatar",
  "assistantAvatar",
  "showCopyButton",
  "showFeedbackButtons",
  "filePreviewMode",
  "modalClosed",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "recordingError",
  "recordingStatusChange",
  "tokenInvalid"
], [
  "modalClosed",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "recordingError",
  "recordingStatusChange",
  "tokenInvalid"
]);
var PcmButton = defineContainer("pcm-button", void 0, [
  "type",
  "size",
  "loading",
  "disabled",
  "icon",
  "shape",
  "backgroundColor",
  "textColor",
  "borderColor",
  "borderRadius",
  "width",
  "block",
  "borderStyle"
]);
var PcmCard = defineContainer("pcm-card", void 0, [
  "token",
  "cardTitle",
  "description",
  "iconUrl",
  "author",
  "authorAvatarUrl",
  "showChatTag",
  "customChatTag",
  "useButtonText",
  "botId",
  "tokenInvalid"
], [
  "tokenInvalid"
]);
var PcmChatMessage = defineContainer("pcm-chat-message", void 0, [
  "message",
  "showFeedbackButtons",
  "botId",
  "userAvatar",
  "assistantAvatar",
  "showCopyButton",
  "filePreviewMode",
  "messageChange",
  "filePreviewRequest",
  "retryRequest"
], [
  "messageChange",
  "filePreviewRequest",
  "retryRequest"
]);
var PcmDrawer = defineContainer("pcm-drawer", void 0, [
  "isOpen",
  "drawerTitle",
  "width",
  "height",
  "closable",
  "maskClosable",
  "mask",
  "closed",
  "afterOpen",
  "afterClose"
], [
  "closed",
  "afterOpen",
  "afterClose"
]);
var PcmHrChatModal = defineContainer("pcm-hr-chat-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "totalQuestions",
  "maxRecordingTime",
  "countdownWarningTime",
  "toEmail",
  "callbackUrl",
  "fullscreen",
  "requireResume",
  "enableVoice",
  "enableAudio",
  "displayContentStatus",
  "modalClosed",
  "streamComplete",
  "conversationStart",
  "someErrorEvent",
  "interviewComplete",
  "recordingError",
  "recordingStatusChange",
  "tokenInvalid"
], [
  "modalClosed",
  "streamComplete",
  "conversationStart",
  "someErrorEvent",
  "interviewComplete",
  "recordingError",
  "recordingStatusChange",
  "tokenInvalid"
]);
var PcmHtwsModal = defineContainer("pcm-htws-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
]);
var PcmHyzjModal = defineContainer("pcm-hyzj-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
]);
var PcmJdModal = defineContainer("pcm-jd-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "modalClosed",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
], [
  "modalClosed",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
]);
var PcmJlppModal = defineContainer("pcm-jlpp-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
]);
var PcmMessage = defineContainer("pcm-message", void 0, [
  "content",
  "type",
  "duration"
]);
var PcmMnctModal = defineContainer("pcm-mnct-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
]);
var PcmMnmsModal = defineContainer("pcm-mnms-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "interviewMode",
  "showCopyButton",
  "showFeedbackButtons",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent",
  "recordingError"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent",
  "recordingError"
]);
var PcmMnmsVideoModal = defineContainer("pcm-mnms-video-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "interviewMode",
  "showCopyButton",
  "showFeedbackButtons",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent",
  "recordingError"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent",
  "recordingError"
]);
var PcmMnmsZpModal = defineContainer("pcm-mnms-zp-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "interviewMode",
  "showCopyButton",
  "showFeedbackButtons",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent",
  "recordingError"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent",
  "recordingError"
]);
var PcmMsbgModal = defineContainer("pcm-msbg-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
]);
var PcmQgqjlModal = defineContainer("pcm-qgqjl-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "interviewComplete",
  "tokenInvalid",
  "someErrorEvent"
]);
var PcmZskChatModal = defineContainer("pcm-zsk-chat-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "fullscreen",
  "customInputs",
  "employeeId",
  "maxAudioRecordingTime",
  "modalClosed",
  "streamComplete",
  "conversationStart",
  "tokenInvalid",
  "clearConversation"
], [
  "modalClosed",
  "streamComplete",
  "conversationStart",
  "tokenInvalid",
  "clearConversation"
]);
var PcmZyghModal = defineContainer("pcm-zygh-modal", void 0, [
  "modalTitle",
  "token",
  "isOpen",
  "icon",
  "zIndex",
  "isShowHeader",
  "isNeedClose",
  "conversationId",
  "defaultQuery",
  "fullscreen",
  "customInputs",
  "filePreviewMode",
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "planningComplete",
  "tokenInvalid",
  "someErrorEvent"
], [
  "modalClosed",
  "uploadSuccess",
  "streamComplete",
  "conversationStart",
  "planningComplete",
  "tokenInvalid",
  "someErrorEvent"
]);
export {
  Pcm1zhanshiMnmsModal,
  PcmAppChatModal,
  PcmButton,
  PcmCard,
  PcmChatMessage,
  PcmDrawer,
  PcmHrChatModal,
  PcmHtwsModal,
  PcmHyzjModal,
  PcmJdModal,
  PcmJlppModal,
  PcmMessage,
  PcmMnctModal,
  PcmMnmsModal,
  PcmMnmsVideoModal,
  PcmMnmsZpModal,
  PcmMsbgModal,
  PcmQgqjlModal,
  PcmZskChatModal,
  PcmZyghModal
};
//# sourceMappingURL=pcm-agents-vue.js.map
