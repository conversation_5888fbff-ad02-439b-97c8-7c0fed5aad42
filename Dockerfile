# 多阶段构建 - 支持跨平台
FROM --platform=$BUILDPLATFORM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 接收构建参数
ARG proxy=taobao
ARG name=app

# 根据代理参数设置npm镜像源
RUN if [ "$proxy" = "taobao" ]; then \
        npm config set registry https://registry.npmmirror.com && \
        npm config set disturl https://npmmirror.com/dist && \
        npm config set electron_mirror https://npmmirror.com/mirrors/electron/ && \
        npm config set sass_binary_site https://npmmirror.com/mirrors/node-sass/ && \
        npm config set phantomjs_cdnurl https://npmmirror.com/mirrors/phantomjs/; \
    fi

# 安装pnpm
RUN npm install -g pnpm

# 如果使用taobao代理，也设置pnpm镜像源
RUN if [ "$proxy" = "taobao" ]; then \
        pnpm config set registry https://registry.npmmirror.com; \
    fi

# 复制package文件
COPY package*.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm build

# 生产阶段 - 使用nginx提供静态文件服务
FROM --platform=$TARGETPLATFORM nginx:alpine

# 复制构建产物到nginx目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置文件
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
