import {
  Error<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Reporter,
  authStore,
  configStore,
  uploadFileToBackend,
  verifyA<PERSON><PERSON><PERSON>
} from "./chunk-6HSK73GQ.js";
import "./chunk-CTIEEM75.js";
import {
  createEvent,
  getElement,
  h,
  registerInstance
} from "./chunk-SPDCWX4H.js";
import {
  __publicField
} from "./chunk-5H7I2G36.js";

// node_modules/.pnpm/pcm-agents@0.6.5_@stencil+c_55275e5c408c000271cd8728a0e5196a/node_modules/pcm-agents/dist/esm/pcm-mnms-zp-modal.entry.js
var pcmMnmsZpModalCss = "";
var globalCss = ":host{font-size:16px}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0, 0, 0, 0.5);display:flex;justify-content:center;align-items:center;z-index:1000;overflow-y:auto;padding:20px}.fullscreen-overlay{padding:0;background-color:rgba(0, 0, 0, 0.7)}.modal-container{background-color:#fff;border-radius:8px;width:100%;display:flex;flex-direction:column;position:relative;margin:auto;transition:all 0.3s ease-out;overflow:hidden}.modal-container.fullscreen{width:100vw;max-width:none;height:100%;border-radius:0;margin:0;display:flex;flex-direction:column;height:100vh;max-height:100vh}.pc-layout{width:80%;max-width:600px;min-width:320px}@media screen and (max-width: 768px){.pc-layout{width:95%}.modal-overlay{padding:0}.modal-container.fullscreen{height:-webkit-fill-available;max-height:-webkit-fill-available;padding:env(safe-area-inset-top) 0 env(safe-area-inset-bottom);margin-top:40px;height:calc(100% - 40px);max-height:calc(100% - 40px);border-radius:16px 16px 0 0}}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:4px 16px;height:50px;border-bottom:1px solid #e8e8e8;flex-shrink:0}.header-left{display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:#333}.header-icon{width:24px;height:24px}.close-button{background:transparent;border:none;cursor:pointer;padding:8px;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:4px}.close-button:hover{background-color:rgba(0, 0, 0, 0.04)}.close-button span{font-size:24px;line-height:1;color:#999}.close-button:hover span{color:#666}.upload-area{cursor:pointer;width:100%}.upload-placeholder{transition:all 0.3s ease;display:flex;flex-direction:column;align-items:center;background:rgba(0, 0, 0, 0.02);border:1px dashed #d9d9d9;border-radius:8px}.upload-placeholder:hover{border:1px dashed #1890ff}.upload-placeholder img{margin-top:8px;width:50px;height:50px}.upload-placeholder .upload-text{margin:4px 0;color:#332F39;font-size:14px}.upload-placeholder .upload-hint{font-size:14px;color:#949AA5;margin-top:8px;padding:0px 10px}.file-item{position:relative;padding:16px;border:1px solid #e2e8f0;border-radius:8px;transition:border-color 0.3s;cursor:pointer;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center}.file-item:hover{border-color:#0D75FB}.file-item-content{display:flex;align-items:center;gap:8px;flex:1;min-width:0;overflow:hidden}.file-icon{color:#0D75FB;flex-shrink:0}.file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:calc(100% - 50px)}.remove-file{background:transparent;border:none;color:#94a3b8;cursor:pointer;font-size:18px;display:flex;align-items:center;justify-content:center;padding:4px;margin-left:8px;border-radius:4px;transition:all 0.2s}.remove-file:hover{background-color:#f1f5f9;color:#475569}.file-input{display:none}.input-container{padding:20px;display:flex;flex-direction:column;height:calc(100% - 50px);background:linear-gradient(150deg, #2a6ee933, #0000 50%) 0 0 / 400px 200px no-repeat, #fff;overflow-y:auto}.input-container h3{margin-top:0;margin-bottom:20px;font-size:18px;color:#333;text-align:center}.jd-input-section{margin-bottom:20px}.jd-input-section label{display:block;margin-bottom:8px;font-weight:500;color:#333}.job-description-textarea{width:calc(100% - 16px);border:1px solid #ddd;border-radius:4px;resize:vertical;font-family:inherit;font-size:14px;line-height:1.5;transition:border-color 0.3s;padding:8px}.job-description-textarea:focus{outline:none;border-color:#1890ff;box-shadow:0 0 0 2px rgba(24, 144, 255, 0.2)}.resume-upload-section{margin-bottom:20px;width:100%;display:flex;flex-direction:column;align-items:center}.resume-upload-section label{display:block;margin-bottom:8px;font-weight:500;color:#333;align-self:flex-start}.submit-button{margin-top:10px;padding:10px 30px;background:#0D75FB;color:white;border:none;border-radius:4px;font-size:16px;cursor:pointer;transition:all 0.3s ease;width:100%;max-width:400px;align-self:center}.submit-button:hover{background-color:#40a9ff}.submit-button:disabled{background-color:rgba(0,0,0,0.04);color:rgba(0,0,0,0.25);cursor:not-allowed}.ai-disclaimer{margin-top:16px;text-align:center;font-size:12px;color:#999;line-height:1.5}.ai-disclaimer p{margin:4px 0}.beian-info{display:flex;justify-content:center;flex-wrap:wrap;gap:4px}.ai-disclaimer a{color:#666;text-decoration:none;transition:color 0.2s ease}.ai-disclaimer a:hover{color:#1890ff;text-decoration:underline}.loading-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:24px}.loading-spinner{width:40px;height:40px;border:4px solid rgba(0, 0, 0, 0.1);border-radius:50%;border-top-color:var(--pcm-primary-color, #1890ff);animation:spin 1s linear infinite;margin-bottom:16px}.loading-text{font-size:16px;color:var(--pcm-text-color, #333)}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}";
var MnmsZpModal = class {
  constructor(hostRef) {
    /**
     * 模态框标题
     */
    __publicField(this, "modalTitle", "模拟面试");
    /**
     * SDK鉴权密钥
     */
    __publicField(this, "token");
    /**
     * 是否显示聊天模态框
     */
    __publicField(this, "isOpen", false);
    /**
     * 当点击模态框关闭时触发
     */
    __publicField(this, "modalClosed");
    /**
     * 应用图标URL
     */
    __publicField(this, "icon");
    /**
     * 聊天框的页面层级
     */
    __publicField(this, "zIndex", 1e3);
    /**
     * 是否展示顶部标题栏
     */
    __publicField(this, "isShowHeader", true);
    /**
     * 是否展示右上角的关闭按钮
     */
    __publicField(this, "isNeedClose", true);
    /**
     * 会话ID，传入继续对话，否则创建新会话
     */
    __publicField(this, "conversationId");
    /**
     * 默认查询文本
     */
    __publicField(this, "defaultQuery", "请开始模拟面试");
    /**
     * 是否以全屏模式打开，移动端建议设置为true
     */
    __publicField(this, "fullscreen", false);
    /**
     * 自定义输入参数，传入customInputs.job_info时，会隐藏JD输入区域。<br>
     * 传入customInputs.file_url时，会隐藏简历上传区域。<br>
     * 传入customInputs.file_url和customInputs.job_info时，会直接开始聊天。<br>
     */
    __publicField(this, "customInputs", {});
    /**
     * 上传成功事件
     */
    __publicField(this, "uploadSuccess");
    /**
     * 流式输出完成事件
     */
    __publicField(this, "streamComplete");
    /**
     * 新会话开始的回调，只会在一轮对话开始时触发一次
     */
    __publicField(this, "conversationStart");
    /**
     * 当聊天完成时触发
     */
    __publicField(this, "interviewComplete");
    /**
     * SDK密钥验证失败事件
     */
    __publicField(this, "tokenInvalid");
    /**
     * 错误事件
     */
    __publicField(this, "someErrorEvent");
    /**
     * 附件预览模式
     * 'drawer': 在右侧抽屉中预览
     * 'window': 在新窗口中打开
     */
    __publicField(this, "filePreviewMode", "window");
    /**
     * 面试模式：text - 文本模式，video - 视频模式
     */
    __publicField(this, "interviewMode", "text");
    /**
     * 录制错误事件
     */
    __publicField(this, "recordingError");
    /**
     * 是否显示复制按钮
     */
    __publicField(this, "showCopyButton", true);
    /**
     * 是否显示点赞点踩按钮
     */
    __publicField(this, "showFeedbackButtons", true);
    __publicField(this, "selectedFile", null);
    __publicField(this, "isUploading", false);
    __publicField(this, "uploadedFileInfo", null);
    __publicField(this, "showChatModal", false);
    __publicField(this, "jobDescription", "");
    __publicField(this, "isSubmitting", false);
    __publicField(this, "tokenInvalidListener");
    __publicField(this, "removeErrorListener");
    __publicField(this, "handleClose", () => {
      this.modalClosed.emit();
    });
    __publicField(this, "handleFileChange", (event) => {
      const input = event.target;
      if (input.files && input.files.length > 0) {
        this.selectedFile = input.files[0];
      }
    });
    __publicField(this, "handleUploadClick", () => {
      var _a;
      const fileInput = (_a = this.hostElement.shadowRoot) == null ? void 0 : _a.querySelector(".file-input");
      fileInput == null ? void 0 : fileInput.click();
    });
    __publicField(this, "clearSelectedFile", () => {
      var _a;
      this.selectedFile = null;
      this.uploadedFileInfo = null;
      const fileInput = (_a = this.hostElement.shadowRoot) == null ? void 0 : _a.querySelector(".file-input");
      if (fileInput) {
        fileInput.value = "";
      }
    });
    __publicField(this, "handleJobDescriptionChange", (event) => {
      const textarea = event.target;
      this.jobDescription = textarea.value;
    });
    __publicField(this, "handleStartInterview", async () => {
      var _a;
      if (!this.selectedFile) {
        alert("请上传简历");
        return;
      }
      if (!((_a = this.customInputs) == null ? void 0 : _a.job_info) && !this.jobDescription.trim()) {
        alert("请输入职位描述");
        return;
      }
      this.isSubmitting = true;
      try {
        if (!this.uploadedFileInfo) {
          await this.uploadFile();
          if (!this.uploadedFileInfo) {
            this.isSubmitting = false;
            return;
          }
        }
        this.showChatModal = true;
      } catch (error) {
        console.error("开始面试时出错:", error);
        SentryReporter.captureError(error, {
          action: "handleStartInterview",
          component: "pcm-mnms-zp-modal",
          title: "开始面试时出错"
        });
        ErrorEventBus.emitError({
          error,
          message: "开始面试时出错，请重试"
        });
      } finally {
        this.isSubmitting = false;
      }
    });
    registerInstance(this, hostRef);
    this.modalClosed = createEvent(this, "modalClosed");
    this.uploadSuccess = createEvent(this, "uploadSuccess");
    this.streamComplete = createEvent(this, "streamComplete");
    this.conversationStart = createEvent(this, "conversationStart");
    this.interviewComplete = createEvent(this, "interviewComplete");
    this.tokenInvalid = createEvent(this, "tokenInvalid");
    this.someErrorEvent = createEvent(this, "someErrorEvent");
    this.recordingError = createEvent(this, "recordingError");
  }
  get hostElement() {
    return getElement(this);
  }
  handleTokenChange(newToken) {
    if (newToken && newToken !== authStore.getToken()) {
      authStore.setToken(newToken);
    }
  }
  async handleIsOpenChange(newValue) {
    var _a, _b;
    if (!newValue) {
      this.clearSelectedFile();
      this.showChatModal = false;
      this.jobDescription = "";
    } else {
      if (this.customInputs && this.customInputs.job_info) {
        this.jobDescription = this.customInputs.job_info;
      }
      await verifyApiKey(this.token);
      if (((_a = this.customInputs) == null ? void 0 : _a.file_url) && ((_b = this.customInputs) == null ? void 0 : _b.job_info) || this.conversationId) {
        this.showChatModal = true;
      }
    }
  }
  componentWillLoad() {
    if (this.zIndex) {
      configStore.setItem("modal-zIndex", this.zIndex);
    }
    if (this.token) {
      authStore.setToken(this.token);
    }
    this.tokenInvalidListener = () => {
      this.tokenInvalid.emit();
    };
    this.removeErrorListener = ErrorEventBus.addErrorListener((errorDetail) => {
      this.someErrorEvent.emit(errorDetail);
    });
    document.addEventListener("pcm-token-invalid", this.tokenInvalidListener);
  }
  disconnectedCallback() {
    document.removeEventListener("pcm-token-invalid", this.tokenInvalidListener);
    if (this.removeErrorListener) {
      this.removeErrorListener();
    }
  }
  async uploadFile() {
    if (!this.selectedFile)
      return;
    this.isUploading = true;
    try {
      const result = await uploadFileToBackend(this.selectedFile, {}, {
        "tags": ["resume"]
      });
      this.uploadedFileInfo = result;
      this.uploadSuccess.emit(result);
    } catch (error) {
      console.error("文件上传错误:", error);
      this.clearSelectedFile();
      SentryReporter.captureError(error, {
        action: "uploadFile",
        component: "pcm-mnms-zp-modal",
        title: "文件上传失败"
      });
      ErrorEventBus.emitError({
        error,
        message: "文件上传失败，请重试"
      });
    } finally {
      this.isUploading = false;
    }
  }
  render() {
    var _a, _b, _c, _d, _e, _f, _g;
    if (!this.isOpen)
      return null;
    const modalStyle = {
      zIndex: String(this.zIndex)
    };
    const containerClass = {
      "modal-container": true,
      "fullscreen": this.fullscreen,
      "pc-layout": true
    };
    const overlayClass = {
      "modal-overlay": true,
      "fullscreen-overlay": this.fullscreen
    };
    const isLoading = this.conversationId && !this.showChatModal;
    const hideJdInput = Boolean(this.customInputs && this.customInputs.job_info);
    const hideResumeUpload = Boolean(this.customInputs && this.customInputs.file_url);
    const hasFileAndJob = Boolean(((_a = this.customInputs) == null ? void 0 : _a.file_url) && ((_b = this.customInputs) == null ? void 0 : _b.job_info));
    return h("div", { class: overlayClass, style: modalStyle }, h("div", { class: containerClass }, this.isShowHeader && h("div", { class: "modal-header" }, h("div", { class: "header-left" }, this.icon && h("img", { src: this.icon, class: "header-icon", alt: "应用图标" }), h("div", null, this.modalTitle)), this.isNeedClose && h("button", { class: "close-button", onClick: this.handleClose }, h("span", null, "×"))), !this.showChatModal && !this.conversationId && !hasFileAndJob && h("div", { class: "input-container" }, !hideJdInput && h("div", { class: "jd-input-section" }, h("label", { htmlFor: "job-description" }, "请输入职位描述 (JD)"), h("textarea", { id: "job-description", class: "job-description-textarea", placeholder: "请输入职位描述，包括职责、要求等信息...", rows: 6, value: this.jobDescription, onInput: this.handleJobDescriptionChange })), !hideResumeUpload && h("div", { class: "resume-upload-section" }, h("label", null, "上传简历"), h("div", { class: "upload-area", onClick: this.handleUploadClick }, this.selectedFile ? h("div", { class: "file-item" }, h("div", { class: "file-item-content" }, h("span", { class: "file-icon" }, "📝"), h("span", { class: "file-name" }, this.selectedFile.name)), h("button", { class: "remove-file", onClick: (e) => {
      e.stopPropagation();
      this.clearSelectedFile();
    } }, "×")) : h("div", { class: "upload-placeholder" }, h("img", { src: "https://pub.pincaimao.com/static/web/images/home/<USER>" }), h("p", { class: "upload-text" }, "点击上传简历"), h("p", { class: "upload-hint" }, "支持 txt、markdown、pdf、docx、doc、md 格式")))), h("button", { class: "submit-button", disabled: !hideResumeUpload && !this.selectedFile || !hideJdInput && !this.jobDescription.trim() || this.isUploading || this.isSubmitting, onClick: this.handleStartInterview }, this.isUploading ? "上传中..." : this.isSubmitting ? "处理中..." : "开始分析"), h("div", { class: "ai-disclaimer" }, h("p", null, "所有内容均由AI生成仅供参考"), h("p", { class: "beian-info" }, h("span", null, "中央网信办生成式人工智能服务备案号"), "：", h("a", { href: "https://www.pincaimao.com", target: "_blank", rel: "noopener noreferrer" }, "Hunan-PinCaiMao-202412310003"))), h("input", { type: "file", class: "file-input", onChange: this.handleFileChange })), isLoading && h("div", { class: "loading-container" }, h("div", { class: "loading-spinner" }), h("p", { class: "loading-text" }, "正在加载对话...")), this.showChatModal && h("div", null, h("pcm-app-chat-modal", { isOpen: true, modalTitle: this.modalTitle, icon: this.icon, isShowHeader: this.isShowHeader, isNeedClose: this.isShowHeader, fullscreen: this.fullscreen, botId: "3022316191018907", conversationId: this.conversationId, defaultQuery: this.defaultQuery, enableVoice: false, filePreviewMode: this.filePreviewMode, showCopyButton: this.showCopyButton, showFeedbackButtons: this.showFeedbackButtons, customInputs: this.conversationId ? {} : {
      ...this.customInputs,
      file_url: ((_c = this.customInputs) == null ? void 0 : _c.file_url) || ((_d = this.uploadedFileInfo) == null ? void 0 : _d.cos_key),
      file_name: ((_e = this.customInputs) == null ? void 0 : _e.file_name) || ((_f = this.uploadedFileInfo) == null ? void 0 : _f.file_name),
      job_info: ((_g = this.customInputs) == null ? void 0 : _g.job_info) || this.jobDescription
    }, interviewMode: this.interviewMode }))));
  }
  static get watchers() {
    return {
      "token": ["handleTokenChange"],
      "isOpen": ["handleIsOpenChange"]
    };
  }
};
MnmsZpModal.style = pcmMnmsZpModalCss + globalCss;
export {
  MnmsZpModal as pcm_mnms_zp_modal
};
//# sourceMappingURL=pcm-mnms-zp-modal.entry-XLK25F6E.js.map
